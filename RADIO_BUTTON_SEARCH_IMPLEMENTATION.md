# Radio Button Search Implementation

## Overview

This implementation adds radio button options for search type selection in both business search (Company Information) and competitor search (Add More Competitors) sections of the onboarding process.

## Features

- **Two Search Modes**: 'Name' and 'Location' with 'Name' selected by default
- **Dynamic Search Logic**: Different API endpoints and search methods based on selection
- **Maintained Existing Logic**: Business search keeps Google Places fallback, competitor search remains database-only
- **Real-time Switching**: Results clear and re-search when switching between modes
- **Dynamic Placeholders**: Search input placeholder changes based on selected search type

## Implementation Details

### Backend Changes

#### 1. SupabaseService (`apps/platform/src/supabase/SupabaseService.ts`)
- **Added**: `searchRetailersByLocationOnly()` method
  - Searches only in city and state fields
  - Includes fuzzy search fallback for location fields
  - Used by both business and competitor location searches

#### 2. CompetitorService (`apps/platform/src/competitors/CompetitorService.ts`)
- **Added**: `searchBusinessRetailersByLocation()` method
  - Location-based business search with Google Places fallback
  - Maintains existing logic: if < 3 results, adds Google Places cannabis business results
- **Added**: `searchCompetitorRetailersByLocation()` method
  - Location-based competitor search, database-only (no Google Places fallback)

#### 3. MiscController (`apps/platform/src/misc/MiscController.ts`)
- **Added**: `/api/misc/retailers/business-search-location` endpoint
  - For business search by location with Google Places fallback
  - Uses `searchBusinessRetailersByLocation()` method
- **Added**: `/api/misc/retailers/search-location` endpoint
  - For competitor search by location, database-only
  - Uses `searchCompetitorRetailersByLocation()` method

### Frontend Changes

#### 1. Services (`apps/ui/src/services/firestore.ts`)
- **Added**: `searchBusinessRetailersByLocationSupabase()` function
  - Calls `/api/misc/retailers/business-search-location` endpoint
  - Used for business location searches
- **Added**: `searchCompetitorRetailersByLocationSupabase()` function
  - Calls `/api/misc/retailers/search-location` endpoint
  - Used for competitor location searches

#### 2. CompetitorsStep (`apps/ui/src/components/onboarding/CompetitorsStep.tsx`)
- **Added**: Radio button selection for search type
- **Added**: `searchType` state variable ('name' | 'location')
- **Updated**: `debouncedSearchPlaces` function to use different search methods
- **Updated**: Search placeholder text based on search type
- **Updated**: Search tip text based on search type

#### 3. BusinessLookupDummy (`apps/ui/src/views/location/BusinessLookupDummy.tsx`)
- **Added**: Radio button selection for search type
- **Added**: `searchType` state variable ('name' | 'location')
- **Updated**: `debouncedSearch` function to use different search methods
- **Updated**: Search placeholder text based on search type

## API Endpoints

### Business Search Endpoints
```
GET /api/misc/retailers/business-search?query={searchQuery}
```
- **Purpose**: Search businesses by name with Google Places fallback
- **Logic**: Name search → City/State search (if < 3 results) → Google Places (if < 3 results)

```
GET /api/misc/retailers/business-search-location?query={searchQuery}
```
- **Purpose**: Search businesses by location with Google Places fallback
- **Logic**: Location search → Google Places (if < 3 results)

### Competitor Search Endpoints
```
GET /api/misc/retailers/search?query={searchQuery}
```
- **Purpose**: Search competitors by name, database-only
- **Logic**: Name search only, no Google Places fallback

```
GET /api/misc/retailers/search-location?query={searchQuery}
```
- **Purpose**: Search competitors by location, database-only
- **Logic**: Location search only, no Google Places fallback

## User Experience

### Business Search (Company Information Step)
1. **Default**: 'Name' radio button selected
2. **Name Mode**: 
   - Placeholder: "Search by business name..."
   - Searches retailer name fields first, then adds location results if needed, then Google Places
3. **Location Mode**:
   - Placeholder: "Search by city or state..."
   - Searches city/state fields, then adds Google Places if needed

### Competitor Search (Add More Competitors Step)
1. **Default**: 'Name' radio button selected
2. **Name Mode**:
   - Placeholder: "Search by competitor name..."
   - Searches retailer name fields only (database-only)
3. **Location Mode**:
   - Placeholder: "Search by city or state..."
   - Searches city/state fields only (database-only)

## Search Logic Differences

### Business Search vs Competitor Search
- **Business Search**: Includes Google Places fallback for both name and location searches
- **Competitor Search**: Database-only for both name and location searches
- **Reasoning**: Business search helps users find their own business even if not in database; competitor search focuses on known dispensaries in the database

### Name vs Location Search
- **Name Search**: Searches `name` and `dispensary_name` fields
- **Location Search**: Searches `city` and `state` fields
- **Fallback**: Both include fuzzy search capabilities for better matching

## Testing

Use the provided test script to validate the implementation:

```bash
node test-search-endpoints.js
```

The script tests all four search combinations:
1. Business search by name
2. Business search by location  
3. Competitor search by name
4. Competitor search by location

## Translation Keys

The implementation uses the following translation keys:

### CompetitorsStep
- `onboarding.steps.competitors.search_type` - "Search by"
- `onboarding.steps.competitors.search_by_name` - "Name"
- `onboarding.steps.competitors.search_by_location` - "Location"
- `onboarding.steps.competitors.search_name_placeholder` - "Search by competitor name..."
- `onboarding.steps.competitors.search_location_placeholder` - "Search by city or state..."
- `onboarding.steps.competitors.search_name_tip` - "Search for competitors by business name"
- `onboarding.steps.competitors.search_location_tip` - "Search for competitors by city or state"

### BusinessLookupDummy
- `onboarding.steps.company_info.search_type` - "Search by"
- `onboarding.steps.company_info.search_by_name` - "Name"
- `onboarding.steps.company_info.search_by_location` - "Location"
- `onboarding.steps.company_info.search_name_placeholder` - "Search by business name..."
- `onboarding.steps.company_info.search_location_placeholder` - "Search by city or state..."

## Performance Improvements

### Search Cancellation
- **AbortController Support**: All search functions now support AbortSignal for proper cancellation
- **Automatic Cancellation**: Ongoing searches are cancelled when switching search types
- **Debounce Cancellation**: Debounced functions are cancelled to prevent delayed execution
- **Cleanup on Unmount**: Components properly clean up ongoing searches when unmounting

### Reduced API Calls
- **Smart Query Filtering**: Skips Google Places calls for very short or incomplete queries
- **Partial Word Detection**: Avoids API calls for queries with partial words (e.g., "califor")
- **State-only Query Filtering**: Reduces broad state-only searches that are likely to fail
- **Improved Error Handling**: Better logging for API rate limits and rejections

### Error Handling Improvements
- **Rate Limit Handling**: Graceful handling of Google Places API rate limits
- **Reduced Warning Noise**: Only logs warnings for unexpected errors, not common API limitations
- **Structured Error Logging**: Better error categorization and logging

## Future Enhancements

1. **Analytics**: Track which search type users prefer
2. **Smart Defaults**: Remember user's last selected search type
3. **Combined Search**: Option to search both name and location simultaneously
4. **Advanced Filters**: Additional filters like state, zip code, business type
5. **Search History**: Show recent searches for quick access
6. **Caching**: Implement search result caching to reduce API calls
7. **Progressive Enhancement**: Show partial results while searches are in progress
