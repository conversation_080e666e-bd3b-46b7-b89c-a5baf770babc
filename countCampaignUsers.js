// Count users in campaigns
require('dotenv').config();
const mysql = require('mysql2/promise');

// Database connection
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'bakedbotpassword',
  database: 'bakedbot'
};

async function countCampaignUsers() {
  console.log('📊 Counting users by campaign...\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // Get recent email campaigns
    const [campaigns] = await connection.execute(`
      SELECT id, name, list_ids, created_at, state
      FROM campaigns 
      WHERE channel = 'email' 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('📧 Recent Email Campaigns:');
    console.log('='.repeat(80));
    
    for (const campaign of campaigns) {
      let listIds = [];

      try {
        if (campaign.list_ids) {
          // Handle both JSON array format and plain number format
          if (typeof campaign.list_ids === 'string') {
            if (campaign.list_ids.startsWith('[')) {
              // Proper JSON array format: "[1,2,3]"
              listIds = JSON.parse(campaign.list_ids);
            } else {
              // Plain number format: "32" -> convert to [32]
              const num = parseInt(campaign.list_ids);
              if (!isNaN(num)) {
                listIds = [num];
              }
            }
          } else if (Array.isArray(campaign.list_ids)) {
            // Already an array
            listIds = campaign.list_ids;
          } else if (typeof campaign.list_ids === 'number') {
            // Single number
            listIds = [campaign.list_ids];
          }
        }
      } catch (e) {
        console.log(`❌ Campaign ${campaign.id}: Invalid list_ids - ${campaign.list_ids}`);
        console.log(`Error: ${e.message}`);
        continue;
      }

      if (listIds.length === 0) {
        console.log(`\n📧 Campaign ${campaign.id}: ${campaign.name}`);
        console.log(`   Created: ${campaign.created_at}`);
        console.log(`   State: ${campaign.state}`);
        console.log(`   ❌ No target lists configured`);
        continue;
      }
      
      let totalUsers = 0;
      const listDetails = [];
      
      // Count users for each list in the campaign
      for (const listId of listIds) {
        const [userCount] = await connection.execute(`
          SELECT COUNT(DISTINCT ul.user_id) as count
          FROM user_list ul
          WHERE ul.list_id = ?
        `, [listId]);
        
        const count = userCount[0].count;
        totalUsers += count;
        
        // Get list name
        const [listInfo] = await connection.execute(`
          SELECT name FROM lists WHERE id = ?
        `, [listId]);
        
        const listName = listInfo[0]?.name || 'Unknown';
        listDetails.push(`List ${listId} (${listName}): ${count} users`);
      }
      
      // Check if campaign sends exist
      const [sendCount] = await connection.execute(`
        SELECT COUNT(*) as count FROM campaign_sends WHERE campaign_id = ?
      `, [campaign.id]);
      
      const campaignSends = sendCount[0].count;
      
      console.log(`\n📧 Campaign ${campaign.id}: ${campaign.name}`);
      console.log(`   Created: ${campaign.created_at}`);
      console.log(`   State: ${campaign.state}`);
      console.log(`   Target Lists: ${JSON.stringify(listIds)}`);
      console.log(`   List Details:`);
      listDetails.forEach(detail => console.log(`     - ${detail}`));
      console.log(`   📊 Total Target Users: ${totalUsers}`);
      console.log(`   📤 Campaign Sends Created: ${campaignSends}`);
      
      if (totalUsers === 0) {
        console.log(`   ⚠️  WARNING: No users in target lists!`);
      }
      
      if (totalUsers > 0 && campaignSends === 0) {
        console.log(`   ⚠️  WARNING: Users exist but no sends created!`);
      }
      
      if (totalUsers > 0 && campaignSends > 0) {
        console.log(`   ✅ Campaign properly configured`);
      }
    }
    
    // Show the latest campaign details
    if (campaigns.length > 0) {
      const latestCampaign = campaigns[0];
      console.log('\n' + '='.repeat(80));
      console.log(`🎯 LATEST CAMPAIGN ANALYSIS:`);
      console.log(`Campaign ${latestCampaign.id}: ${latestCampaign.name}`);
      
      let listIds = [];
      try {
        if (latestCampaign.list_ids) {
          // Handle both JSON array format and plain number format
          if (typeof latestCampaign.list_ids === 'string') {
            if (latestCampaign.list_ids.startsWith('[')) {
              // Proper JSON array format: "[1,2,3]"
              listIds = JSON.parse(latestCampaign.list_ids);
            } else {
              // Plain number format: "32" -> convert to [32]
              const num = parseInt(latestCampaign.list_ids);
              if (!isNaN(num)) {
                listIds = [num];
              }
            }
          } else if (Array.isArray(latestCampaign.list_ids)) {
            // Already an array
            listIds = latestCampaign.list_ids;
          } else if (typeof latestCampaign.list_ids === 'number') {
            // Single number
            listIds = [latestCampaign.list_ids];
          }
        }
      } catch (e) {
        console.log(`❌ Invalid list_ids JSON`);
        await connection.end();
        return;
      }
      
      let totalUsers = 0;
      for (const listId of listIds) {
        const [userCount] = await connection.execute(`
          SELECT COUNT(DISTINCT ul.user_id) as count
          FROM user_list ul
          WHERE ul.list_id = ?
        `, [listId]);
        totalUsers += userCount[0].count;
      }
      
      console.log(`📊 Total users in latest campaign: ${totalUsers}`);
      
      if (totalUsers === 0) {
        console.log(`\n💡 SOLUTION: Update campaign to target a list with users:`);
        console.log(`   UPDATE campaigns SET list_ids = '[1]' WHERE id = ${latestCampaign.id};`);
      }
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

// Run the analysis
countCampaignUsers()
  .then(() => {
    console.log('\n📊 Campaign user count analysis complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Analysis failed:', error.message);
    process.exit(1);
  });
