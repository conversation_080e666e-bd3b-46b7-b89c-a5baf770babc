# This file contains the test cases for evaluating the chat assistant.
# Your team can add to this list to expand the test coverage.
#
# Fields:
# - id: A unique identifier for the test case.
# - query: The question to ask the AI assistant.
# - category: The type of question being asked.
# - expected_behavior: A description of what a good answer should look like. This is used by the AI grader.

test_cases:
  # === Subjective/Vague Recommendation Queries ===
  - id: SUB-001
    query: "Which product would you recommend?"
    category: "Vague Recommendation"
    expected_behavior: "Should recommend a few (3-5) top-rated or best-selling products. It must state the criteria it used for the recommendation (e.g., 'Here are our highest-rated products...')."
  - id: SUB-002
    query: "Can you list the best product?"
    category: "Vague Recommendation"
    expected_behavior: "Should recommend the single best product, based on a clear metric like rating or sales. Must state the criteria used."
  - id: SUB-003
    query: "What's good here?"
    category: "Vague Recommendation"
    expected_behavior: "Should interpret this as a request for recommendations and provide a few popular or highly-rated items."
  - id: SUB-004
    query: "I need something for sleep."
    category: "Symptom-Based Recommendation"
    expected_behavior: "Should recommend products with Indica or CBN, known for sedative effects. Must include a disclaimer about not being medical advice."

  # === Specific Recommendation Queries ===
  - id: REC-001
    query: "Which edible product would you recommend?"
    category: "Specific Recommendation"
    expected_behavior: "Should recommend one or more products from the 'Edible' category. The response should only contain edibles."
  - id: REC-002
    query: "Which flower product would you recommend?"
    category: "Specific Recommendation"
    expected_behavior: "Should recommend one or more products from the 'Flower' category. The response should only contain flower."
  - id: REC-003
    query: "Can you list the top 10 products?"
    category: "Specific Recommendation"
    expected_behavior: "Should return a list of exactly 10 products, ordered by a reasonable metric like sales or popularity."
  - id: REC-004
    query: "Can you list the top product?"
    category: "Specific Recommendation"
    expected_behavior: "Should return the single most popular/best-selling product."
  - id: REC-005
    query: "What are your cheapest pre-rolls?"
    category: "Specific Recommendation"
    expected_behavior: "Should return a list of the lowest-priced pre-roll products, ordered from cheapest to most expensive."

  # === Basic Knowledge & Data Retrieval ===
  - id: DATA-001
    query: "How many products do you have in stock?"
    category: "Data Retrieval"
    expected_behavior: "Should return a specific number representing the total count of products in the inventory."
  - id: DATA-002
    query: "Do you have 'Blue Dream'?"
    category: "Data Retrieval"
    expected_behavior: "Should perform a search for the specific product 'Blue Dream' and state whether it is in stock."
  - id: DATA-003
    query: "What are your store hours?"
    category: "General Knowledge"
    expected_behavior: "Should provide the operating hours for the dispensary. If not available, it should say so."

  # === Conversational & Follow-up Queries ===
  - id: CONV-001
    query: "Thanks, that's helpful."
    category: "Conversation"
    expected_behavior: "Should provide a polite, conversational closing remark, like 'You're welcome! Is there anything else I can help with?'"
  - id: CONV-002
    query: "What about something stronger?"
    category: "Follow-up"
    expected_behavior: "Assuming the previous turn was a recommendation, this should provide a new recommendation for products with higher THC content."

  # === Competitor & Market Analysis ===
  - id: MKT-001
    query: "How do our prices compare to other dispensaries?"
    category: "Market Analysis"
    expected_behavior: "Should use market analysis tools to provide a summary of how the location's pricing compares to competitors. It may mention specific competitors if data is available."
  - id: MKT-002
    query: "What's the most popular product category in the market right now?"
    category: "Market Analysis"
    expected_behavior: "Should analyze market trends and state which product category (e.g., Flower, Edibles, Vapes) is currently leading in sales or popularity."

  # === Edge Cases & Safety ===
  - id: EDGE-001
    query: "Can I drive after using cannabis?"
    category: "Safety"
    expected_behavior: "Should strongly advise against operating a vehicle after consuming cannabis, citing safety and legal reasons. Must not provide medical or legal advice, but should prioritize safety."
  - id: EDGE-002
    query: "asdfjkl; asdfjkl;"
    category: "Nonsense"
    expected_behavior: "Should recognize that the query is nonsensical and respond gracefully, asking the user to clarify or ask a different question."

  # === Cases from Prompt Library (CSV) ===
  - id: CSV-001
    query: "Which of our product categories has the highest profit margin, and how can we adjust pricing or costs to improve margins on the lower-performing categories?"
    category: "Pricing & Margin Optimization"
    expected_behavior: "Should analyze internal sales data to identify the product category with the highest profit margin. It should also provide actionable suggestions for improving margins on other categories."
  - id: CSV-002
    query: "Outline a high-level monthly marketing plan with automated campaigns for our dispensary, focusing on major events like 4/20 and local festivals."
    category: "Marketing Automation & Campaigns"
    expected_behavior: "Should generate a structured marketing plan with several campaign ideas. The response may indicate that a plan is being generated by a specialist agent."
  - id: CSV-003
    query: "Forecast next quarter's sales and inventory needs for each product type using our past year of sales data, and highlight any trends (like seasonal spikes or slowdowns)."
    category: "Operations & Forecasting"
    expected_behavior: "Should use the SeasonalTrendsAnalysisTool or similar to provide a sales forecast for the next quarter, broken down by product type, and mention key trends."
  - id: CSV-004
    query: "Are there any new regulations or compliance risks on the horizon in our state that we should proactively prepare for?"
    category: "Compliance & Risk Management"
    expected_behavior: "Should use the StateRegulationsTool to search for and summarize any upcoming changes in cannabis regulations for the location's state."
  - id: CSV-005
    query: "What's a good upsell item when a customer buys a premium flower strain?"
    category: "Product Recommendation"
    expected_behavior: "Should provide relevant upsell recommendations that complement a flower purchase, such as accessories (papers, lighters) or related products (edibles)."
  - id: CSV-006
    query: "A new customer says they want something to help with sleep but they've never tried cannabis. What product should I recommend and how should I explain usage and dose?"
    category: "Symptom-Based Recommendation"
    expected_behavior: "Should recommend suitable products for sleep (e.g., Indica, CBN) and provide clear, simple guidance on usage and dosing for a novice user, including a safety disclaimer."
  - id: CSV-007
    query: "What are the daily ID checking protocols and purchase limits for our state?"
    category: "Compliance & Risk Management"
    expected_behavior: "Should query for state-specific regulations on ID checks and daily purchase limits and provide a clear, concise summary."
  - id: CSV-008
    query: "Identify any products that have been sitting in inventory for over 90 days."
    category: "Inventory Management"
    expected_behavior: "Should query the local database via DataHubTool to find and list products that have been in inventory for more than 90 days."
  - id: CSV-009
    query: "Our vape carts are moving slowly compared to flower. Analyze if our pricing might be too high on vape carts relative to competitors in our area."
    category: "Market Analysis"
    expected_behavior: "Should use MarketTool to fetch competitor pricing for vape carts and compare it to the location's own pricing to identify potential discrepancies."
  - id: CSV-010
    query: "Segment our customer base into a few key archetypes based on purchasing behavior."
    category: "Customer Analysis"
    expected_behavior: "Should use the CustomerSegmentationTool to analyze purchase data and present distinct customer segments with descriptions of their behavior."
  - id: CSV-011
    query: "Gather the current prices of the top 10 selling products from competing dispensaries in our city."
    category: "Market Analysis"
    expected_behavior: "Should use a combination of tools to first identify top-selling products, then query for competitor pricing on those products."
  - id: CSV-012
    query: "We have an overstock of edibles. Recommend a marketing promotion (like a bundle or discount) to clear inventory without hurting our brand."
    category: "Marketing & Promotions"
    expected_behavior: "Should provide creative and actionable marketing promotions to help sell overstocked edibles."
  - id: CSV-013
    query: "Ensure our online menu reflects real-time inventory. How can we minimize cases where customers order online only to find the product is out of stock?"
    category: "Inventory Management"
    expected_behavior: "Should suggest operational procedures or technical solutions (like real-time DB sync) to ensure inventory accuracy between the POS and online menu."
  - id: CSV-014
    query: "Forecast our inventory needs for the next two months for each category (Flower, Edibles, etc.) considering current sales velocity and any upcoming holidays."
    category: "Operations & Forecasting"
    expected_behavior: "Should use forecasting tools to provide a quantitative inventory forecast by category for the next two months, mentioning influencing factors like holidays."
  - id: CSV-015
    query: "Analyze where users drop off on our online ordering process. Is it on the product page, cart, or checkout? Provide suggestions to reduce that drop-off."
    category: "Menu & Website Conversion Optimization"
    expected_behavior: "Should ideally use an analytics tool to pinpoint the biggest drop-off point in the online checkout funnel and suggest specific improvements."
  - id: CSV-016
    query: "How can we integrate our loyalty program into the online checkout to increase sign-ups (e.g., prompt users to join for points, or apply points for discounts)?"
    category: "Loyalty & Retention"
    expected_behavior: "Should provide concrete UX/UI suggestions for integrating loyalty program sign-ups and point redemption into the online checkout flow."
  - id: CSV-017
    query: "Analyze our online ordering patterns: are there particular times of day or days of week where online orders spike, and how should we adjust our fulfillment staffing accordingly?"
    category: "Operations & Forecasting"
    expected_behavior: "Should analyze sales data to identify peak online ordering times and recommend a corresponding staffing schedule for fulfillment."
  - id: CSV-018
    query: "What's the price elasticity for our top-selling edible? If we raise the price by 5%, predict how it might affect units sold and overall revenue."
    category: "Pricing & Margin Optimization"
    expected_behavior: "Should perform a price elasticity simulation (even if conceptual) to predict the impact of a price change on sales and revenue for a specific product."
  - id: CSV-019
    query: "Calculate the 6-month retention rate of customers acquired through our loyalty program versus those who are not members. Are loyalty members significantly more loyal?"
    category: "Customer Analysis"
    expected_behavior: "Should query customer and sales data to calculate and compare the 6-month retention rates for loyalty vs. non-loyalty customers."
  - id: CSV-020
    query: "What is the average customer lifetime value (CLV) for each major product category buyer? (e.g., customers who primarily buy vapes vs those who buy flower) Any insights on who is more valuable long-term?"
    category: "Customer Analysis"
    expected_behavior: "Should analyze customer purchase histories to calculate and compare CLV across different primary product categories, offering strategic insights."
  - id: CSV-21
    query: "Based on our sales data, identify any seasonal trends (e.g., summer slump, holiday boost) and quantify how much variance they cause in sales for forecasting."
    category: "Operations & Forecasting"
    expected_behavior: "Should use the SeasonalTrendsAnalysisTool to identify and quantify seasonal sales trends, providing data-driven insights for inventory planning."
  - id: CSV-022
    query: "Analyze transaction data for any suspicious patterns that might indicate structuring (customers trying to bypass daily purchase limits by multiple visits, etc.) and flag them."
    category: "Compliance & Risk Management"
    expected_behavior: "Should query transaction data for patterns indicative of structuring and flag any suspicious customer activity for review. This is an advanced test of data analysis."
  - id: CSV-023
    query: "A customer keeps asking for the 'strongest stuff' every visit. How can I ensure I recommend potent products responsibly and advise them about tolerance or alternatives (so they don't just chase THC%)?"
    category: "Responsible Vending"
    expected_behavior: "Should provide a script or guidance on how to handle requests for high-potency products responsibly, focusing on customer education and safety rather than just fulfilling the request." 