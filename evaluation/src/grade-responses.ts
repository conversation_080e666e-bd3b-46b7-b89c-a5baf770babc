import { OpenAI } from "openai";
import { RawResult, GradedResult, Grade } from "./types";

const graderSystemPrompt = `You are an AI quality assurance expert. Evaluate the following AI-generated answer based on the user's query and the expected behavior.

**User Query:** {query}
**Expected Behavior:** {expected_behavior}
**AI Answer:** {answer}

Please evaluate the answer based on the following criteria:
1.  **Relevance:** Is the answer directly relevant to the query? (Score 1-5)
2.  **Helpfulness:** Is the answer helpful and actionable? Does it meet the expected behavior? (Score 1-5)
3.  **Correctness:** Does the answer seem factually correct and logically sound? (Score 1-5)

Provide your response in this exact JSON format, with no other text, comments, or markdown.
{
  "relevance": <score_1_to_5>,
  "helpfulness": <score_1_to_5>,
  "correctness": <score_1_to_5>,
  "justification": "<brief_justification_for_your_scores>"
}`;

export async function gradeResponses(
  rawResults: RawResult[],
  openai: OpenAI
): Promise<GradedResult[]> {
  const gradedResults: GradedResult[] = [];

  for (const result of rawResults) {
    console.log(`Grading test case: ${result.testCase.id}...`);

    if (result.error) {
      gradedResults.push({
        ...result,
        grade: {
          relevance: 0,
          helpfulness: 0,
          correctness: 0,
          justification: `API call failed with error: ${result.error}`,
          score: 0,
        },
      });
      continue;
    }

    try {
      const answer = result.response.messages[1]?.content || "NO_RESPONSE";

      const prompt = graderSystemPrompt
        .replace("{query}", result.testCase.query)
        .replace("{expected_behavior}", result.testCase.expected_behavior)
        .replace("{answer}", answer);

      const response = await openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [{ role: "system", content: prompt }],
        temperature: 0.2,
        response_format: { type: "json_object" },
      });

      const gradeJson = response.choices[0].message.content;
      if (!gradeJson) {
        throw new Error("Grader returned an empty response.");
      }

      const grade: Grade = JSON.parse(gradeJson);

      // Calculate a weighted score
      const score =
        grade.relevance * 0.2 +
        grade.helpfulness * 0.5 +
        grade.correctness * 0.3;

      gradedResults.push({
        ...result,
        grade: {
          ...grade,
          score: parseFloat(score.toFixed(2)),
        },
      });
    } catch (error) {
      console.error(`Failed to grade test case ${result.testCase.id}:`, error);
      gradedResults.push({
        ...result,
        grade: {
          relevance: 0,
          helpfulness: 0,
          correctness: 0,
          justification: `Failed to grade response. Error: ${
            error instanceof Error ? error.message : String(error)
          }`,
          score: 0,
        },
      });
    }
  }

  return gradedResults;
}
