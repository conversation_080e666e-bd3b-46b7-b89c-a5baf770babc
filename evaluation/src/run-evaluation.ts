import fs from "fs";
import path from "path";
import yaml from "js-yaml";
import axios from "axios";
import { OpenAI } from "openai";
import dotenv from "dotenv";
import { TestCase, TestFile, RawResult, GradedResult } from "./types";
import { gradeResponses } from "./grade-responses";
import { authService } from "./auth";

// Environment variables are loaded in auth.ts

const {
  API_BASE_URL,
  OPENAI_API_KEY,
  DEFAULT_AGENT_ID,
  FIREBASE_EMAIL,
  FIREBASE_PASSWORD,
  LOCATION_ID,
} = process.env;

if (
  !API_BASE_URL ||
  !OPENAI_API_KEY ||
  !DEFAULT_AGENT_ID ||
  !FIREBASE_EMAIL ||
  !FIREBASE_PASSWORD ||
  !LOCATION_ID
) {
  console.error(
    "Error: Missing environment variables. Please create and configure the .env file in the /evaluation directory."
  );
  console.error(
    "Required: API_BASE_URL, OPENAI_API_KEY, DEFAULT_AGENT_ID, FIREBASE_EMAIL, FIREBASE_PASSWORD, LOCATION_ID"
  );
  process.exit(1);
}

const openai = new OpenAI({ apiKey: OPENAI_API_KEY });

/**
 * Runs a single test case against the chat API.
 */
async function runTest(testCase: TestCase): Promise<RawResult> {
  console.log(`Running test case: ${testCase.id} ("${testCase.query}")...`);
  try {
    const headers = await authService.getAuthHeaders();
    const url = `${API_BASE_URL}/api/admin/locations/${LOCATION_ID}/chats/messages`;

    // console.log(`Making request to: ${url}`);
    // console.log(`Headers:`, headers);
    // console.log(`Payload:`, {
    //   agent_id: DEFAULT_AGENT_ID,
    //   message: testCase.query,
    // });

    const response = await axios.post(
      url,
      {
        agent_id: DEFAULT_AGENT_ID,
        message: testCase.query,
      },
      {
        headers,
      }
    );
    return { testCase, response: response.data };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    console.error(`Test case ${testCase.id} failed:`, errorMessage);

    // Log more details about the error
    if (error instanceof Error && "response" in error) {
      const axiosError = error as any;
      console.error(`Status: ${axiosError.response?.status}`);
      console.error(`Status Text: ${axiosError.response?.statusText}`);
      console.error(`Response Data:`, axiosError.response?.data);
    }

    return { testCase, response: null, error: errorMessage };
  }
}

/**
 * Generates and prints a summary report to the console.
 */
function generateReport(gradedResults: GradedResult[]) {
  console.log("\n\n--- Evaluation Report ---");

  const categoryScores: { [key: string]: { scores: number[]; count: number } } =
    {};
  let totalScore = 0;

  for (const result of gradedResults) {
    if (!categoryScores[result.testCase.category]) {
      categoryScores[result.testCase.category] = { scores: [], count: 0 };
    }
    categoryScores[result.testCase.category].scores.push(result.grade.score);
    categoryScores[result.testCase.category].count++;
    totalScore += result.grade.score;
  }

  console.log("\n--- Average Score by Category ---");
  for (const category in categoryScores) {
    const avgScore =
      categoryScores[category].scores.reduce((a, b) => a + b, 0) /
      categoryScores[category].count;
    console.log(`${category.padEnd(30)}: ${avgScore.toFixed(2)} / 5.00`);
  }

  const overallAverage = totalScore / gradedResults.length;
  console.log(`\n--- Overall Average Score ---`);
  console.log(`Total`.padEnd(30) + `: ${overallAverage.toFixed(2)} / 5.00`);

  console.log("\n--- Failing Tests (Score < 3.5) ---");
  const failingTests = gradedResults.filter((r) => r.grade.score < 3.5);

  if (failingTests.length === 0) {
    console.log("All tests passed the quality threshold!");
  } else {
    for (const fail of failingTests) {
      console.log(`\n- ID: ${fail.testCase.id} | Score: ${fail.grade.score}`);
      console.log(`  Query: ${fail.testCase.query}`);
      console.log(
        `  Response: ${(fail.response?.messages[1]?.content || "N/A").substring(
          0,
          150
        )}...`
      );
      console.log(`  Reason: ${fail.grade.justification}`);
    }
  }
  console.log("\n--- End of Report ---");
}

async function main() {
  console.log("Starting AI chat evaluation...");

  // Log environment configuration for debugging
  console.log("Environment configuration:");
  console.log(`API_BASE_URL: ${API_BASE_URL}`);
  console.log(`LOCATION_ID: ${LOCATION_ID}`);
  console.log(`DEFAULT_AGENT_ID: ${DEFAULT_AGENT_ID}`);
  console.log(`FIREBASE_EMAIL: ${FIREBASE_EMAIL}`);

  // 1. Authenticate with Firebase
  try {
    await authService.login(FIREBASE_EMAIL!, FIREBASE_PASSWORD!);
  } catch (error) {
    console.error("Failed to authenticate with Firebase:", error);
    process.exit(1);
  }

  // 2. Load test cases from YAML file
  const testFile = yaml.load(
    fs.readFileSync(path.join(__dirname, "../test-cases.yml"), "utf8")
  ) as TestFile;
  const testCases = testFile.test_cases;
  console.log(`Loaded ${testCases.length} test cases.`);

  // 3. Run tests in parallel and collect raw results
  const rawResultPromises = testCases.map((testCase) => runTest(testCase));
  const rawResults = await Promise.all(rawResultPromises);

  // Create results directory if it doesn't exist
  const resultsDir = path.join(__dirname, "../results");
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }

  // Save raw results to a timestamped file
  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const rawResultsPath = path.join(resultsDir, `raw-results-${timestamp}.json`);
  fs.writeFileSync(rawResultsPath, JSON.stringify(rawResults, null, 2));
  console.log(`\nRaw results saved to ${rawResultsPath}`);

  // 4. Grade the responses using the AI grader
  const gradedResults = await gradeResponses(rawResults, openai);

  // Save graded results to a timestamped file
  const gradedResultsPath = path.join(
    resultsDir,
    `graded-results-${timestamp}.json`
  );
  fs.writeFileSync(gradedResultsPath, JSON.stringify(gradedResults, null, 2));
  console.log(`Graded results saved to ${gradedResultsPath}`);

  // 5. Generate and print the final report
  generateReport(gradedResults);

  // 6. Cleanup
  await authService.logout();
}

main().catch((error) => {
  console.error(
    "An unexpected error occurred during the evaluation process:",
    error
  );
  process.exit(1);
});
