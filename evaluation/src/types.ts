export interface TestCase {
  id: string;
  query: string;
  category: string;
  expected_behavior: string;
}

export interface TestFile {
  test_cases: TestCase[];
}

export interface RawResult {
  testCase: TestCase;
  response: any;
  error?: string;
}

export interface GradedResult extends RawResult {
  grade: {
    relevance: number;
    helpfulness: number;
    correctness: number;
    justification: string;
    score: number; // Overall weighted score
  };
}

export interface Grade {
  relevance: number;
  helpfulness: number;
  correctness: number;
  justification: string;
}
