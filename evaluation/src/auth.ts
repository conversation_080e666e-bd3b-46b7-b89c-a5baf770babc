import { initializeApp } from "firebase/app";
import { getAuth, signInWithEmailAndPassword, Auth, User } from "firebase/auth";
import axios from "axios";
import dotenv from "dotenv";

// Load environment variables first
dotenv.config();

// Debug: Check what env variables are loaded
console.log("Environment variables loaded:");
console.log(
  "FIREBASE_API_KEY:",
  process.env.FIREBASE_API_KEY ? "SET" : "NOT SET"
);
console.log(
  "FIREBASE_AUTH_DOMAIN:",
  process.env.FIREBASE_AUTH_DOMAIN ? "SET" : "NOT SET"
);
console.log(
  "FIREBASE_PROJECT_ID:",
  process.env.FIREBASE_PROJECT_ID ? "SET" : "NOT SET"
);

// Firebase configuration - you'll need to update this with your actual config
const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID,
};

// Validate that required Firebase config is present
const requiredFields = ["apiKey", "authDomain", "projectId", "appId"];
const missingFields = requiredFields.filter(
  (field) => !firebaseConfig[field as keyof typeof firebaseConfig]
);

if (missingFields.length > 0) {
  console.error("❌ Missing required Firebase configuration:");
  missingFields.forEach((field) => {
    console.error(`  - ${field.toUpperCase()}`);
  });
  throw new Error(
    `Missing Firebase configuration: ${missingFields.join(", ")}`
  );
}

console.log("✅ Firebase configuration loaded successfully");

class AuthService {
  private auth: Auth;
  private currentUser: User | null = null;
  private oauthCookie: string | null = null;

  constructor() {
    const app = initializeApp(firebaseConfig);
    this.auth = getAuth(app);
  }

  async login(email: string, password: string): Promise<string> {
    try {
      console.log("Authenticating with Firebase...");
      const userCredential = await signInWithEmailAndPassword(
        this.auth,
        email,
        password
      );
      this.currentUser = userCredential.user;

      // Get the Firebase ID token
      const firebaseIdToken = await this.currentUser.getIdToken();
      console.log("✅ Firebase authentication successful");

      // Exchange Firebase token for app JWT token
      const apiBaseUrl = process.env.API_BASE_URL;
      if (!apiBaseUrl) {
        throw new Error("API_BASE_URL environment variable is required");
      }

      console.log("Exchanging Firebase token for app JWT...");
      const response = await axios.post(
        `${apiBaseUrl}/api/auth/login/firebase/callback`,
        {
          idToken: firebaseIdToken,
        },
        {
          maxRedirects: 0,
          validateStatus: (status) => status === 302 || status < 400, // Allow redirects
          withCredentials: true,
        }
      );

      // Extract the oauth cookie from the response
      const setCookieHeader = response.headers["set-cookie"];
      if (setCookieHeader) {
        const oauthCookie = setCookieHeader.find((cookie: string) =>
          cookie.startsWith("oauth=")
        );

        if (oauthCookie) {
          // Extract just the cookie value part
          this.oauthCookie = oauthCookie.split(";")[0];
          console.log("✅ App JWT token obtained successfully");
          return this.oauthCookie;
        }
      }

      throw new Error("Failed to obtain app JWT token from response");
    } catch (error) {
      console.error("❌ Authentication failed:", error);
      if (axios.isAxiosError(error)) {
        console.error("Response status:", error.response?.status);
        console.error("Response data:", error.response?.data);
        console.error("Response headers:", error.response?.headers);
      }
      throw new Error(
        `Authentication failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  async getAuthHeaders(): Promise<Record<string, string>> {
    if (!this.oauthCookie) {
      throw new Error("Not authenticated. Please login first.");
    }

    // Only attempt refresh if we have a current user and the token might be expiring soon
    // We'll be conservative and not refresh unless absolutely necessary
    try {
      // Parse the current JWT to check expiration
      const cookieValue = this.oauthCookie.split("=")[1];
      const oauthData = JSON.parse(cookieValue);
      const expiresAt = new Date(oauthData.expires_at);
      const now = new Date();
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();
      const fiveMinutesInMs = 5 * 60 * 1000;

      // Only refresh if token expires in less than 5 minutes
      if (timeUntilExpiry > fiveMinutesInMs) {
        // Token is still good for more than 5 minutes, use existing token
        return {
          "Content-Type": "application/json",
          Cookie: this.oauthCookie,
        };
      }

      // Token is close to expiring, attempt refresh
      if (this.currentUser) {
        console.log("Token expires soon, attempting refresh...");
        const firebaseIdToken = await this.currentUser.getIdToken(true); // Force refresh

        // Re-exchange for fresh app token
        const apiBaseUrl = process.env.API_BASE_URL;
        const response = await axios.post(
          `${apiBaseUrl}/api/auth/login/firebase/callback`,
          {
            idToken: firebaseIdToken,
          },
          {
            maxRedirects: 0,
            validateStatus: (status) => status === 302 || status < 400,
            withCredentials: true,
          }
        );

        const setCookieHeader = response.headers["set-cookie"];
        if (setCookieHeader) {
          const oauthCookie = setCookieHeader.find((cookie: string) =>
            cookie.startsWith("oauth=")
          );

          if (oauthCookie) {
            this.oauthCookie = oauthCookie.split(";")[0];
            console.log("✅ Token refreshed successfully");
          }
        }
      }
    } catch (error) {
      // Refresh failed, but we'll continue with the existing token
      // This is fine as long as the existing token is still valid
      console.log(
        "Token refresh failed, continuing with existing token:",
        (error as any).response?.data?.error || (error as Error).message
      );
    }

    return {
      "Content-Type": "application/json",
      Cookie: this.oauthCookie,
    };
  }

  isAuthenticated(): boolean {
    return !!this.oauthCookie && !!this.currentUser;
  }

  async logout(): Promise<void> {
    await this.auth.signOut();
    this.currentUser = null;
    this.oauthCookie = null;
  }
}

export const authService = new AuthService();
