{"name": "chat-evaluation", "version": "1.0.0", "description": "Automated evaluation scripts for the AI chat assistant.", "main": "dist/run-evaluation.js", "scripts": {"start": "ts-node src/run-evaluation.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.8", "dotenv": "^16.4.5", "js-yaml": "^4.1.0", "openai": "^4.38.3", "firebase": "^10.8.0", "firebase-admin": "^12.0.0", "@types/firebase": "^2.4.32"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.12.7", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}