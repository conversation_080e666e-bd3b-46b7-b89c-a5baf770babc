# Chat Evaluation Framework

This directory contains the tools to run an automated evaluation of the Smokey AI assistant's performance.

## Purpose

The goal of this framework is to provide a consistent and objective way to measure the quality of the AI's responses. By running a standard set of test questions against the API, we can:

- Identify regressions (i.e., things that used to work but are now broken).
- Benchmark the impact of changes (e.g., prompt engineering, tool modifications).
- Systematically find and fix failing or poor-quality responses.
- Create a "golden set" of questions and answers to guide future development.

## Setup

1.  **Navigate to this directory:**

    ```bash
    cd evaluation
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Configure Environment Variables:**
    Create a `.env` file in this directory by copying the example file:
    ```bash
    cp .env.example .env
    ```
    Now, edit the `.env` file with the correct values for your local environment:
    - `API_BASE_URL`: The base URL for the chat API (e.g., `http://localhost:3000/api/admin`).
    - `OPENAI_API_KEY`: Your OpenAI API key, which is required for the automated grading step.
    - `DEFAULT_AGENT_ID`: The ID of the agent you want to test against (e.g., `1` for Smokey).
    - `LOCATION_ID`: The ID of the location to test against (e.g., `1`).
    - `FIREBASE_EMAIL`: Email address for Firebase authentication.
    - `FIREBASE_PASSWORD`: Password for Firebase authentication.
    - `FIREBASE_API_KEY`: Your Firebase project API key.
    - `FIREBASE_AUTH_DOMAIN`: Your Firebase auth domain (e.g., `your-project.firebaseapp.com`).
    - `FIREBASE_PROJECT_ID`: Your Firebase project ID.
    - `FIREBASE_STORAGE_BUCKET`: Your Firebase storage bucket.
    - `FIREBASE_MESSAGING_SENDER_ID`: Your Firebase messaging sender ID.
    - `FIREBASE_APP_ID`: Your Firebase app ID.

## How to Run an Evaluation

1.  **Add or modify test cases:**
    Open `test-cases.yml` and add any questions you want to test. Follow the existing format.

2.  **Run the evaluation script:**
    ```bash
    npm start
    ```
    This command will:
    a. Read the questions from `test-cases.yml`.
    b. Send each question to the chat API.
    c. Save the raw responses to `results/evaluation-results-YYYY-MM-DD-HH-MM-SS.json`.
    d. Send the query and response to a grader LLM (e.g., GPT-4) for evaluation.
    e. Save the final graded results to `results/graded-results-YYYY-MM-DD-HH-MM-SS.json`.
    f. Print a summary report to the console.

## The Files

- `package.json` / `tsconfig.json`: Project configuration.
- `.env.example`: Template for environment variables.
- `test-cases.yml`: The list of questions and expected behaviors. **This is the main file your team will edit.**
- `src/run-evaluation.ts`: The main script that orchestrates the evaluation process.
- `src/grade-responses.ts`: The script responsible for sending responses to an LLM for automated grading.
- `src/types.ts`: TypeScript type definitions for the project.
- `results/`: This directory is created automatically and stores the output of each evaluation run.
