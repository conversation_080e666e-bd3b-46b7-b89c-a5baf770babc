version: "3.8"

services:
  mysql:
    image: mysql:8.0.36
    platform: linux/x86_64
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4 
      --collation-server=utf8mb4_unicode_ci 
      --innodb-use-native-aio=0
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_HOST: "%"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p$$MYSQL_ROOT_PASSWORD",
        ]
      interval: 5s
      timeout: 20s
      retries: 30
      start_period: 30s
    entrypoint: >
      /bin/bash -c "
      echo '=== MySQL Configuration ===' &&
      echo 'MYSQL_DATABASE:' $${MYSQL_DATABASE} &&
      echo 'MYSQL_ROOT_HOST:' $${MYSQL_ROOT_HOST} &&
      echo '===========================' &&
      docker-entrypoint.sh mysqld $${@}"
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    expose:
      - 6379
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  api:
    build:
      context: ./apps/platform
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
      - "9229:9229"
    volumes:
      - ./apps/platform:/usr/src/app/apps/platform:delegated
      - api_node_modules:/usr/src/app/apps/platform/node_modules
      - uploads:/usr/src/app/public/uploads
      - ./firebase-credentials.json:/usr/src/app/apps/platform/firebase-credentials.json:ro
    command: >
      /bin/bash -c "
      echo '=== Database Configuration ===' &&
      echo 'DB_CLIENT:' $${DB_CLIENT} &&
      echo 'DB_HOST:' $${DB_HOST} &&
      echo 'DB_USERNAME:' $${DB_USERNAME} &&
      echo 'DB_PASSWORD:' $${DB_PASSWORD} &&
      echo 'DB_DATABASE:' $${DB_DATABASE} &&
      echo 'DB_PORT:' $${DB_PORT} &&
      echo '===========================' &&
      ./start.sh
      "
    environment:
      NODE_ENV: development
      BASE_URL: ${BASE_URL}
      FRONTEND_URL: "http://localhost:3000"
      RUNNER: "api,worker"
      NODE_OPTIONS: "--openssl-legacy-provider --max-old-space-size=8192"
      APP_SECRET: ${APP_SECRET}
      PORT: 3001
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_COMPILED_MESSAGE: ${LOG_COMPILED_MESSAGE:-true}
      DB_CLIENT: ${DB_CLIENT}
      DB_HOST: mysql
      DB_USERNAME: "root"
      DB_PASSWORD: ${DB_PASSWORD}
      DB_PORT: ${DB_PORT}
      DB_DATABASE: ${DB_DATABASE}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      STORAGE_DRIVER: ${STORAGE_DRIVER}
      STORAGE_BASE_URL: ${STORAGE_BASE_URL}
      STORAGE_S3_BUCKET: ${STORAGE_S3_BUCKET}
      STORAGE_S3_ENDPOINT: ${STORAGE_S3_ENDPOINT}
      STORAGE_S3_FORCE_PATH_STYLE: ${STORAGE_S3_FORCE_PATH_STYLE}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      QUEUE_DRIVER: ${QUEUE_DRIVER}
      REDIS_HOST: ${REDIS_HOST}
      REDIS_PORT: ${REDIS_PORT}
      REDIS_TLS: ${REDIS_TLS}
      AWS_SQS_QUEUE_URL: ${AWS_SQS_QUEUE_URL}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      AUTH_DRIVER: ${AUTH_DRIVER}
      AUTH_BASIC_EMAIL: ${AUTH_BASIC_EMAIL}
      AUTH_BASIC_PASSWORD: ${AUTH_BASIC_PASSWORD}
      AUTH_BASIC_NAME: ${AUTH_BASIC_NAME}
      AUTH_SAML_CALLBACK_URL: ${AUTH_SAML_CALLBACK_URL}
      AUTH_SAML_ENTRY_POINT_URL: ${AUTH_SAML_ENTRY_POINT_URL}
      AUTH_SAML_ISSUER: ${AUTH_SAML_ISSUER}
      AUTH_SAML_CERT: ${AUTH_SAML_CERT}
      AUTH_SAML_IS_AUTHN_SIGNED: ${AUTH_SAML_IS_AUTHN_SIGNED}
      AUTH_SAML_NAME: ${AUTH_SAML_NAME}
      AUTH_OPENID_ISSUER_URL: ${AUTH_OPENID_ISSUER_URL}
      AUTH_OPENID_CLIENT_ID: ${AUTH_OPENID_CLIENT_ID}
      AUTH_OPENID_CLIENT_SECRET: ${AUTH_OPENID_CLIENT_SECRET}
      AUTH_OPENID_REDIRECT_URI: ${AUTH_OPENID_REDIRECT_URI}
      AUTH_OPENID_DOMAIN_WHITELIST: ${AUTH_OPENID_DOMAIN_WHITELIST}
      AUTH_OPENID_RESPONSE_TYPES: ${AUTH_OPENID_RESPONSE_TYPES}
      AUTH_OPENID_NAME: ${AUTH_OPENID_NAME}
      AUTH_GOOGLE_ISSUER_URL: ${AUTH_GOOGLE_ISSUER_URL}
      AUTH_GOOGLE_CLIENT_ID: ${AUTH_GOOGLE_CLIENT_ID}
      AUTH_GOOGLE_CLIENT_SECRET: ${AUTH_GOOGLE_CLIENT_SECRET}
      AUTH_GOOGLE_NAME: ${AUTH_GOOGLE_NAME}
      ERROR_DRIVER: ${ERROR_DRIVER}
      ERROR_BUGSNAG_API_KEY: ${ERROR_BUGSNAG_API_KEY}
      ERROR_SENTRY_DSN: ${ERROR_SENTRY_DSN}
      TRACKING_LINK_WRAP: ${TRACKING_LINK_WRAP}
      TRACKING_DEEPLINK_MIRROR_URL: ${TRACKING_DEEPLINK_MIRROR_URL}
      AUTH_FIREBASE_CREDENTIALS: ${AUTH_FIREBASE_CREDENTIALS}
      AUTH_FIREBASE_NAME: ${AUTH_FIREBASE_NAME}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_PHONE_NUMBER: ${TWILIO_PHONE_NUMBER}
      SENDGRID_API_KEY: ${SENDGRID_API_KEY}
      PINECONE_API_KEY: ${PINECONE_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      FIREBASE_API_KEY: ${FIREBASE_API_KEY}
      FIREBASE_PRIVATE_KEY: "${FIREBASE_PRIVATE_KEY}"
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_AUTH_DOMAIN: ${FIREBASE_AUTH_DOMAIN}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_STORAGE_BUCKET: ${FIREBASE_STORAGE_BUCKET}
      FIREBASE_MESSAGING_SENDER_ID: ${FIREBASE_MESSAGING_SENDER_ID}
      FIREBASE_APP_ID: ${FIREBASE_APP_ID}
      FIREBASE_MEASUREMENT_ID: ${FIREBASE_MEASUREMENT_ID}
      REACT_APP_RECAPTCHA_SITE_KEY: ${REACT_APP_RECAPTCHA_SITE_KEY}
      OAUTH_LINKEDIN_REDIRECT_URI: ${OAUTH_LINKEDIN_REDIRECT_URI}
      OAUTH_LINKEDIN_CLIENT_ID: ${OAUTH_LINKEDIN_CLIENT_ID}
      OAUTH_LINKEDIN_CLIENT_SECRET: ${OAUTH_LINKEDIN_CLIENT_SECRET}
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_TOKEN: ${SUPABASE_TOKEN}
      SUPABASE_WORKSPACE_ID: ${SUPABASE_WORKSPACE_ID}
      SUPABASE_API_TOKEN: ${SUPABASE_API_TOKEN}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      SUPABASE_SQL_WAREHOUSE_ID: ${SUPABASE_SQL_WAREHOUSE_ID}
      APP_CHECK_SECRET: ${APP_CHECK_SECRET}
      STRIPE_ENDPOINT_SECRET: ${STRIPE_ENDPOINT_SECRET}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      IDEOGRAM_API_KEY: ${IDEOGRAM_API_KEY}
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:3001/api/health",
        ]
      interval: 20s
      timeout: 10s
      retries: 10
      start_period: 60s
    stdin_open: true
    tty: true
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started

  ui:
    build:
      context: ./apps/ui
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./apps/ui:/usr/src/app/apps/ui:delegated
      - ui_node_modules:/usr/src/app/apps/ui/node_modules
    networks:
      default:
        aliases:
          - ui
    environment:
      NODE_ENV: development
      NODE_OPTIONS: --openssl-legacy-provider
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
      FAST_REFRESH: "true"
      WDS_SOCKET_PORT: "3000"
      HOST: "0.0.0.0"
      DANGEROUSLY_DISABLE_HOST_CHECK: "true"
      PUBLIC_URL: "http://localhost:3000"
      REACT_APP_API_BASE_URL: "/api"
      REACT_APP_FRONTEND_URL: "http://localhost:3000"
      REACT_APP_PROXY_URL: "http://api:3001"
      API_BASE_URL: "/api"
      REACT_APP_AUTH_CALLBACK_URL: "http://localhost:3000/api/auth/login/firebase/callback"
      BROWSER: "none"
      FORCE_COLOR: "1"
      REACT_APP_FIREBASE_API_KEY: ${REACT_APP_FIREBASE_API_KEY}
      REACT_APP_FIREBASE_AUTH_DOMAIN: ${REACT_APP_FIREBASE_AUTH_DOMAIN}
      REACT_APP_FIREBASE_PROJECT_ID: ${REACT_APP_FIREBASE_PROJECT_ID}
      REACT_APP_FIREBASE_STORAGE_BUCKET: ${REACT_APP_FIREBASE_STORAGE_BUCKET}
      REACT_APP_FIREBASE_MESSAGING_SENDER_ID: ${REACT_APP_FIREBASE_MESSAGING_SENDER_ID}
      REACT_APP_FIREBASE_APP_ID: ${REACT_APP_FIREBASE_APP_ID}
      REACT_APP_FIREBASE_MEASUREMENT_ID: ${REACT_APP_FIREBASE_MEASUREMENT_ID}
      REACT_APP_FIREBASE_APP_CHECK_DEBUG_TOKEN: "true"
      REACT_APP_FIREBASE_CONFIG: '{"apiKey":"${REACT_APP_FIREBASE_API_KEY}","authDomain":"${REACT_APP_FIREBASE_AUTH_DOMAIN}","projectId":"${REACT_APP_FIREBASE_PROJECT_ID}","storageBucket":"${REACT_APP_FIREBASE_STORAGE_BUCKET}","messagingSenderId":"${REACT_APP_FIREBASE_MESSAGING_SENDER_ID}","appId":"${REACT_APP_FIREBASE_APP_ID}","measurementId":"${REACT_APP_FIREBASE_MEASUREMENT_ID}"}'
      REACT_APP_AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      REACT_APP_AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      REACT_APP_AWS_REGION: ${AWS_REGION}
      REACT_APP_RECAPTCHA_SITE_KEY: ${REACT_APP_RECAPTCHA_SITE_KEY}
      REACT_APP_GOOGLE_MAPS_API_KEY: ${REACT_APP_GOOGLE_MAPS_API_KEY}
      REACT_APP_FACEBOOK_APP_ID: ${REACT_APP_FACEBOOK_APP_ID}
      REACT_APP_LINKEDIN_CLIENT_ID: ${REACT_APP_LINKEDIN_CLIENT_ID}
      REACT_APP_STRIPE_PUBLISHABLE_KEY: ${REACT_APP_STRIPE_PUBLISHABLE_KEY}
      REACT_APP_STRIPE_PRICING_TABLE_ID: ${REACT_APP_STRIPE_PRICING_TABLE_ID}
      SUPABASE_URL: ${SUPABASE_URL}
      SUPABASE_TOKEN: ${SUPABASE_TOKEN}
      SUPABASE_WORKSPACE_ID: ${SUPABASE_WORKSPACE_ID}
      SUPABASE_API_TOKEN: ${SUPABASE_API_TOKEN}
      SUPABASE_SQL_WAREHOUSE_ID: ${SUPABASE_SQL_WAREHOUSE_ID}
      APP_CHECK_SECRET: ${APP_CHECK_SECRET}
    stdin_open: true
    tty: true
    depends_on:
      api:
        condition: service_healthy

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads:
    driver: local
  api_node_modules:
  ui_node_modules:
