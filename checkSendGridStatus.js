// Check SendGrid API key status and permissions
require('dotenv').config();

const sgMail = require('@sendgrid/mail');

// Set your SendGrid API Key
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

console.log('Checking SendGrid API key status...');
console.log('API Key:', process.env.SENDGRID_API_KEY ? `${process.env.SENDGRID_API_KEY.slice(0, 10)}...${process.env.SENDGRID_API_KEY.slice(-10)}` : 'undefined');

// Test 1: Try to get API key info (this will tell us if the key is valid)
const sgClient = require('@sendgrid/client');
sgClient.setApiKey(process.env.SENDGRID_API_KEY);

async function checkAPIKey() {
  try {
    console.log('\n=== Testing API Key Validity ===');
    const request = {
      url: '/v3/scopes',
      method: 'GET',
    };
    
    const [response, body] = await sgClient.request(request);
    console.log('✅ API Key is valid!');
    console.log('Available scopes:', body.scopes.slice(0, 10)); // Show first 10 scopes
    
    // Test 2: Check sender identities
    console.log('\n=== Checking Sender Identities ===');
    const senderRequest = {
      url: '/v3/verified_senders',
      method: 'GET',
    };
    
    const [senderResponse, senderBody] = await sgClient.request(senderRequest);
    console.log('✅ Verified senders found:', senderBody.results.length);
    
    if (senderBody.results.length > 0) {
      console.log('Verified sender emails:');
      senderBody.results.forEach((sender, index) => {
        console.log(`  ${index + 1}. ${sender.from_email} (${sender.verified ? '✅ Verified' : '❌ Not Verified'})`);
      });
    } else {
      console.log('❌ No verified senders found!');
    }
    
  } catch (error) {
    console.error('❌ API Key test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.body);
    }
  }
}

checkAPIKey();
