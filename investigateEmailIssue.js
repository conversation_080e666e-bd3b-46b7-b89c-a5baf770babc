// Investigation script to check email campaign status
require('dotenv').config();
const mysql = require('mysql2/promise');

// Database connection
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'bakedbotpassword',
  database: 'bakedbot'
};

async function investigateEmailIssue() {
  console.log('🔍 Investigating email campaign issue...\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    // 1. Check recent campaigns
    console.log('📊 Recent Campaigns:');
    const [campaigns] = await connection.execute(`
      SELECT id, name, state, channel, created_at, send_at, provider_id
      FROM campaigns 
      WHERE channel = 'email' 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.table(campaigns);
    
    if (campaigns.length === 0) {
      console.log('❌ No email campaigns found!');
      await connection.end();
      return;
    }
    
    // 2. Check campaign sends for the most recent campaign
    const latestCampaign = campaigns[0];
    console.log(`\n📧 Campaign Sends for Campaign ${latestCampaign.id} (${latestCampaign.name}):`);
    
    const [sends] = await connection.execute(`
      SELECT cs.id, cs.state, cs.created_at, cs.send_at, u.email
      FROM campaign_sends cs
      JOIN users u ON cs.user_id = u.id
      WHERE cs.campaign_id = ?
      ORDER BY cs.created_at DESC
      LIMIT 20
    `, [latestCampaign.id]);
    
    console.table(sends);
    
    // 3. Check campaign send states summary
    console.log(`\n📈 Campaign Send States Summary for Campaign ${latestCampaign.id}:`);
    const [statesSummary] = await connection.execute(`
      SELECT state, COUNT(*) as count
      FROM campaign_sends
      WHERE campaign_id = ?
      GROUP BY state
    `, [latestCampaign.id]);
    
    console.table(statesSummary);
    
    // 4. Check email templates
    console.log(`\n📝 Email Templates for Campaign ${latestCampaign.id}:`);
    const [templates] = await connection.execute(`
      SELECT id, type, data
      FROM templates
      WHERE campaign_id = ? AND type = 'email'
    `, [latestCampaign.id]);
    
    if (templates.length > 0) {
      templates.forEach(template => {
        const templateData = JSON.parse(template.data);
        console.log(`Template ${template.id}:`);
        console.log(`  Subject: ${templateData.subject}`);
        console.log(`  From: ${JSON.stringify(templateData.from)}`);
        console.log(`  Has HTML: ${!!templateData.html}`);
        console.log(`  Has Text: ${!!templateData.text}`);
      });
    } else {
      console.log('❌ No email templates found for this campaign!');
    }
    
    // 5. Check providers
    console.log(`\n🔧 Email Provider for Campaign ${latestCampaign.id}:`);
    const [providers] = await connection.execute(`
      SELECT id, name, type, config
      FROM providers
      WHERE id = ?
    `, [latestCampaign.provider_id]);
    
    if (providers.length > 0) {
      const provider = providers[0];
      console.log(`Provider ${provider.id}: ${provider.name} (${provider.type})`);
      
      // Check if it's SendGrid and has API key
      if (provider.type === 'sendgrid') {
        const config = JSON.parse(provider.config);
        console.log(`  Has API Key: ${!!config.api_key}`);
        console.log(`  API Key starts with: ${config.api_key ? config.api_key.substring(0, 10) + '...' : 'N/A'}`);
      }
    } else {
      console.log('❌ No provider found for this campaign!');
    }
    
    // 6. Check for any failed jobs or errors
    console.log('\n🚨 Recent Failed Campaign Sends:');
    const [failedSends] = await connection.execute(`
      SELECT cs.id, cs.campaign_id, cs.state, cs.created_at, u.email, c.name as campaign_name
      FROM campaign_sends cs
      JOIN users u ON cs.user_id = u.id
      JOIN campaigns c ON cs.campaign_id = c.id
      WHERE cs.state IN ('failed', 'aborted')
      ORDER BY cs.created_at DESC
      LIMIT 10
    `);
    
    if (failedSends.length > 0) {
      console.table(failedSends);
    } else {
      console.log('✅ No failed sends found');
    }
    
    // 7. Check users with email addresses
    console.log('\n👥 Users with Email Addresses (sample):');
    const [users] = await connection.execute(`
      SELECT id, email, created_at
      FROM users
      WHERE email IS NOT NULL AND email != ''
      ORDER BY created_at DESC
      LIMIT 5
    `);
    
    console.table(users);
    
    await connection.end();
    
    // 8. Provide diagnosis
    console.log('\n🎯 DIAGNOSIS:');
    
    if (campaigns.length === 0) {
      console.log('❌ No email campaigns found');
    } else if (sends.length === 0) {
      console.log('❌ Campaign exists but no sends were created - check campaign list generation');
    } else {
      const pendingSends = sends.filter(s => s.state === 'pending').length;
      const sentSends = sends.filter(s => s.state === 'sent').length;
      const failedSends = sends.filter(s => s.state === 'failed').length;
      
      console.log(`📊 Send Status: ${pendingSends} pending, ${sentSends} sent, ${failedSends} failed`);
      
      if (pendingSends > 0) {
        console.log('⚠️  Pending sends exist - job processing may be stuck');
      }
      
      if (templates.length === 0) {
        console.log('❌ No email template found - this will cause job failures');
      }
      
      if (providers.length === 0) {
        console.log('❌ No email provider found - this will cause job failures');
      }
    }
    
  } catch (error) {
    console.error('💥 Investigation failed:', error.message);
  }
}

// Run investigation
investigateEmailIssue()
  .then(() => {
    console.log('\n🔍 Investigation complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Investigation error:', error.message);
    process.exit(1);
  });
