import React from 'react';
import InsightCard from '../apps/ui/src/ui/InsightCard';
import { Insight } from '../apps/ui/src/types';

// Example insights demonstrating the enhanced actionability logic

// ✅ ACTIONABLE INSIGHTS - Show "Create Automation" button
const actionableInsights: Insight[] = [
  {
    id: 1,
    location_id: 1,
    title: "Re-engage Inactive Customers",
    description: "Send targeted email to customers who haven't purchased in 30 days",
    impact: "high",
    type: "automation",
    actions: ["Create email campaign", "Set up customer segmentation"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    location_id: 1,
    title: "Birthday Campaign Opportunity",
    description: "Automatically send birthday discounts to customers",
    impact: "medium",
    type: "campaign",
    actions: ["Create birthday email template", "Set up automated trigger"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
];

// ❌ NON-ACTIONABLE INSIGHTS - Show alternative actions instead
const nonActionableInsights: Insight[] = [
  {
    id: 3,
    location_id: 1,
    title: "Implement Product Category Analysis",
    description: "Consider implementing detailed product category analysis to understand customer preferences",
    impact: "medium",
    type: "general",
    actions: ["Research analysis tools", "Review product data"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    // Will show: "Learn More" button
  },
  {
    id: 4,
    location_id: 1,
    title: "Develop Age-Specific Marketing Strategies",
    description: "Create targeted marketing approaches for different age demographics",
    impact: "high",
    type: "customer",
    actions: ["Analyze customer demographics", "Create persona profiles"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    // Will show: "View Customer Data" button
  },
  {
    id: 5,
    location_id: 1,
    title: "Market Expansion Opportunity",
    description: "Analyze competitor pricing strategies in your area",
    impact: "high",
    type: "market",
    actions: ["Research competitors", "Analyze market trends"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    // Will show: "View Market Analysis" button
  },
  {
    id: 6,
    location_id: 1,
    title: "Product Portfolio Review",
    description: "Evaluate current product mix and identify gaps",
    impact: "medium",
    type: "product",
    actions: ["Review product performance", "Identify trending products"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    // Will show: "Explore Products" button
  },
  {
    id: 7,
    location_id: 1,
    title: "Revenue Analysis Framework",
    description: "Establish comprehensive financial tracking and analysis processes",
    impact: "high",
    type: "financial",
    actions: ["Set up reporting", "Define KPIs"],
    status: "new",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    // Will show: "View Financial Reports" button
  },
];

// Example component showing how insights are rendered differently
export const InsightActionabilityDemo: React.FC = () => {
  const handleRefresh = () => {
    console.log('Refreshing insights...');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>Insight Actionability Enhancement Demo</h2>
      
      <section style={{ marginBottom: '40px' }}>
        <h3 style={{ color: 'green' }}>✅ Actionable Insights</h3>
        <p>These insights show the "Create Automation" button because they can be converted into automated campaigns:</p>
        <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
          {actionableInsights.map(insight => (
            <InsightCard 
              key={insight.id} 
              insight={insight} 
              onRefresh={handleRefresh}
            />
          ))}
        </div>
      </section>

      <section style={{ marginBottom: '40px' }}>
        <h3 style={{ color: 'orange' }}>❌ Non-Actionable Insights</h3>
        <p>These insights show contextual alternative actions instead of "Create Automation":</p>
        <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
          {nonActionableInsights.map(insight => (
            <InsightCard 
              key={insight.id} 
              insight={insight} 
              onRefresh={handleRefresh}
            />
          ))}
        </div>
      </section>

      <section>
        <h3>Compact Mode Examples</h3>
        <p>Compact mode also respects actionability:</p>
        <div style={{ display: 'grid', gap: '8px' }}>
          <InsightCard 
            insight={actionableInsights[0]} 
            onRefresh={handleRefresh}
            compact={true}
          />
          <InsightCard 
            insight={nonActionableInsights[0]} 
            onRefresh={handleRefresh}
            compact={true}
          />
        </div>
      </section>
    </div>
  );
};

export default InsightActionabilityDemo;
