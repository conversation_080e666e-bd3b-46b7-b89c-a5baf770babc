# Insight Patterns Setup Guide

## Database Setup

### 1. Run Migration
```bash
# Navigate to platform directory
cd apps/platform

# Run the migration to create the insight_patterns table
npm run migrate:latest
# or
npx knex migrate:latest
```

### 2. Seed Default Patterns
```bash
# Run the seed to populate default patterns
npm run seed:run
# or
npx knex seed:run --specific=insight_patterns.js
```

### 3. Verify Setup
The migration creates the `insight_patterns` table with these columns:
- `id` (primary key)
- `pattern` (regex pattern)
- `description` (human-readable description)
- `type` (non_actionable | actionable)
- `is_active` (boolean, default true)
- `priority` (integer, default 0)
- `examples` (JSON array of example phrases)
- `created_at` / `updated_at` (timestamps)

## Default Patterns Included

The seed file includes 15 pre-configured patterns:

### Non-Actionable Patterns
1. **implement.*analysis** - "Implement Product Category Analysis"
2. **develop.*strateg** - "Develop Age-Specific Marketing Strategies"
3. **develop.*presence** - "Develop a Social Media Presence" ⭐ *New*
4. **create.*framework** - "Create Analytics Framework"
5. **establish.*process** - "Establish Review Process"
6. **build.*system** - "Build Tracking System"
7. **design.*approach** - "Design Marketing Approach"
8. **conduct.*research** - "Conduct Market Research"
9. **perform.*audit** - "Perform Security Audit"
10. **review.*policies** - "Review Privacy Policies"
11. **analyze.*trends** - "Analyze Market Trends"
12. **consider.*implementing** - "Consider implementing new features"
13. **should.*analyze** - "Should analyze customer behavior"
14. **need.*to.*study** - "Need to study market conditions"
15. **recommend.*reviewing** - "Recommend reviewing current processes"

## Accessing the Admin UI

### Prerequisites
- Admin role required
- Navigate to any location in the platform

### Steps
1. Go to **Data Hub** section in the sidebar
2. Click **"Insight Patterns"** (only visible to admins)
3. Manage patterns through the UI:
   - ✅ Create new patterns
   - ✏️ Edit existing patterns
   - 🗑️ Delete patterns
   - 🧪 Test patterns with sample text
   - 🔍 Debug pattern matching

## Testing the Implementation

### 1. Test Pattern Matching
Use the built-in test feature in the admin UI:
- Enter pattern: `develop.*presence`
- Enter test text: "Develop a Social Media Presence"
- Should return: ✅ Matches

### 2. Test Insight Behavior
Create or wait for insights with titles like:
- "Develop a Social Media Presence" → Should show alternative action
- "Re-engage Inactive Customers" → Should show "Create Automation"

### 3. Verify API Endpoints
```bash
# Get all patterns (admin only)
GET /api/insights/patterns

# Test a pattern
POST /api/insights/patterns/test
{
  "pattern": "develop.*presence",
  "testText": "Develop a Social Media Presence"
}
```

## Troubleshooting

### Pattern Not Working
1. Check if pattern is active: `is_active = true`
2. Verify regex syntax in the test interface
3. Check pattern priority (higher numbers checked first)
4. Clear pattern cache: patterns are cached for 5 minutes

### UI Not Showing
1. Verify admin role permissions
2. Check if "Insight Patterns" appears in Data Hub menu
3. Ensure route is properly registered in router

### Database Issues
```bash
# Check if table exists
SELECT * FROM insight_patterns LIMIT 5;

# Check migration status
npx knex migrate:status

# Rollback if needed
npx knex migrate:rollback
```

## Performance Notes

- Patterns are cached for 5 minutes for optimal performance
- Cache is automatically cleared when patterns are modified
- Higher priority patterns are checked first to optimize matching
- Inactive patterns are excluded from matching to improve speed

## Security

- All pattern management endpoints require admin role
- Regex patterns are validated before saving
- Pattern testing is sandboxed and safe
- No user input is directly executed as regex
