-- Market Analysis SQL Function for Supabase
-- This function should be executed in the Supabase SQL Editor to enable market analysis
-- Last updated: Enhanced with robust error handling, data validation, and performance optimizations

-- Create comprehensive indexes for better performance (run these first)
CREATE INDEX IF NOT EXISTS idx_products_retailer_id ON products(retailer_id);
CREATE INDEX IF NOT EXISTS idx_products_retailer_category_price ON products(retailer_id, category, latest_price) WHERE latest_price IS NOT NULL AND latest_price > 0;
CREATE INDEX IF NOT EXISTS idx_products_category_weight ON products(category, display_weight) WHERE category IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_price_range ON products(latest_price) WHERE latest_price > 0 AND latest_price < 10000;
CREATE INDEX IF NOT EXISTS idx_products_active_listings ON products(retailer_id, category, latest_price, created_at) WHERE latest_price > 0 AND created_at > NOW() - INTERVAL '180 days';

-- Geographic indexes for regional analysis
CREATE INDEX IF NOT EXISTS idx_retailers_location ON retailers(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_retailers_city_state ON retailers(city, state) WHERE city IS NOT NULL AND state IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_with_location ON products(retailer_id, category, latest_price) WHERE latest_price > 0;

CREATE OR REPLACE FUNCTION get_market_snapshot_data(
  competitor_place_ids TEXT[],
  user_retailer_id TEXT DEFAULT NULL
)
RETURNS TABLE (
  category TEXT,
  subcategory TEXT,
  display_weight TEXT,
  latest_price NUMERIC,
  retailer_id TEXT,
  is_user_product BOOLEAN,
  product_count INTEGER,
  avg_price NUMERIC,
  min_price NUMERIC,
  max_price NUMERIC,
  last_updated TIMESTAMP
) AS $$
DECLARE
  query_limit INTEGER := 200000;
  price_lower_bound NUMERIC := 0.01;
  price_upper_bound NUMERIC := 10000.00;
BEGIN
  -- Input validation
  IF competitor_place_ids IS NULL OR array_length(competitor_place_ids, 1) IS NULL THEN
    RAISE NOTICE 'No competitor IDs provided';
    RETURN;
  END IF;

  -- Log the function call for debugging
  RAISE NOTICE 'Market analysis called with % competitors and user_retailer_id: %', 
    array_length(competitor_place_ids, 1), 
    COALESCE(user_retailer_id, 'NULL');

  RETURN QUERY
    -- Competitor products with enhanced filtering and validation
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      false AS is_user_product,
      1 AS product_count,
      ROUND(p.latest_price::NUMERIC, 2) AS avg_price,
      ROUND(p.latest_price::NUMERIC, 2) AS min_price,
      ROUND(p.latest_price::NUMERIC, 2) AS max_price,
      p.updated_at::TIMESTAMP AS last_updated
    FROM products p
    WHERE
      p.retailer_id = ANY(competitor_place_ids)
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      -- Only include recent products (last 6 months)
      -- Filter out obvious data quality issues
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      -- Exclude products with suspicious pricing patterns
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
      
    UNION ALL
    
    -- User products with same enhanced filtering
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      true AS is_user_product,
      1 AS product_count,
      ROUND(p.latest_price::NUMERIC, 2) AS avg_price,
      ROUND(p.latest_price::NUMERIC, 2) AS min_price,
      ROUND(p.latest_price::NUMERIC, 2) AS max_price,
      p.updated_at::TIMESTAMP AS last_updated
    FROM products p
    WHERE
      user_retailer_id IS NOT NULL
      AND p.retailer_id = user_retailer_id
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      -- Only include recent products (last 6 months)
      -- Filter out obvious data quality issues
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      -- Exclude products with suspicious pricing patterns
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
    
    ORDER BY category, display_weight, latest_price
    LIMIT query_limit; -- Prevent runaway queries
    
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in get_market_snapshot_data: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- NEW: Regional Market Analysis by City/State
CREATE OR REPLACE FUNCTION get_regional_market_data_by_location(
  target_city TEXT DEFAULT NULL,
  target_state TEXT DEFAULT NULL,
  user_retailer_id TEXT DEFAULT NULL,
  max_retailers INTEGER DEFAULT 50
)
RETURNS TABLE (
  category TEXT,
  subcategory TEXT,
  display_weight TEXT,
  latest_price NUMERIC,
  retailer_id TEXT,
  retailer_name TEXT,
  retailer_city TEXT,
  retailer_state TEXT,
  is_user_product BOOLEAN,
  product_count INTEGER
) AS $$
DECLARE
  query_limit INTEGER := 200000;
  price_lower_bound NUMERIC := 0.01;
  price_upper_bound NUMERIC := 10000.00;
BEGIN
  -- Input validation
  IF target_city IS NULL AND target_state IS NULL THEN
    RAISE NOTICE 'Either city or state must be provided';
    RETURN;
  END IF;

  RAISE NOTICE 'Regional market analysis called for city: %, state: %, user_retailer_id: %', 
    COALESCE(target_city, 'ANY'), 
    COALESCE(target_state, 'ANY'),
    COALESCE(user_retailer_id, 'NULL');

  RETURN QUERY
    -- Regional competitor products
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      false AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      -- Geographic filters
      (target_city IS NULL OR r.city ILIKE target_city)
      AND (target_state IS NULL OR r.state ILIKE target_state)
      -- Exclude user's own products from competitor analysis
      AND (user_retailer_id IS NULL OR p.retailer_id != user_retailer_id)
      -- Standard quality filters
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
      -- Limit to top retailers by product count to prevent overwhelming results
      AND p.retailer_id IN (
        SELECT p2.retailer_id 
        FROM products p2 
        INNER JOIN retailers r2 ON p2.retailer_id = r2.id::TEXT
        WHERE 
          (target_city IS NULL OR r2.city ILIKE target_city)
          AND (target_state IS NULL OR r2.state ILIKE target_state)
          AND p2.latest_price > 0
        GROUP BY p2.retailer_id 
        ORDER BY COUNT(*) DESC 
        LIMIT max_retailers
      )
      
    UNION ALL
    
    -- User products (if provided)
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      true AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      user_retailer_id IS NOT NULL
      AND p.retailer_id = user_retailer_id
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
    
    ORDER BY category, display_weight, latest_price
    LIMIT query_limit;
    
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in get_regional_market_data_by_location: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- NEW: Regional Market Analysis by Radius (lat/lng) - OPTIMIZED
CREATE OR REPLACE FUNCTION get_regional_market_data_by_radius(
  center_latitude NUMERIC,
  center_longitude NUMERIC,
  radius_miles NUMERIC DEFAULT 30,
  user_retailer_id TEXT DEFAULT NULL,
  max_retailers INTEGER DEFAULT 50
)
RETURNS TABLE (
  category TEXT,
  subcategory TEXT,
  display_weight TEXT,
  latest_price NUMERIC,
  retailer_id TEXT,
  retailer_name TEXT,
  retailer_city TEXT,
  retailer_state TEXT,
  distance_miles NUMERIC,
  is_user_product BOOLEAN,
  product_count INTEGER
) AS $$
DECLARE
  query_limit INTEGER := 200000; -- Further reduced for better performance
  price_lower_bound NUMERIC := 0.01;
  price_upper_bound NUMERIC := 10000.00;
  -- Calculate approximate bounding box for faster pre-filtering
  lat_delta NUMERIC := radius_miles / 69.0; -- ~69 miles per degree latitude
  lng_delta NUMERIC := radius_miles / (69.0 * cos(radians(center_latitude))); -- longitude varies by latitude
  min_lat NUMERIC := center_latitude - lat_delta;
  max_lat NUMERIC := center_latitude + lat_delta;
  min_lng NUMERIC := center_longitude - lng_delta;
  max_lng NUMERIC := center_longitude + lng_delta;
BEGIN
  -- Input validation
  IF center_latitude IS NULL OR center_longitude IS NULL THEN
    RAISE NOTICE 'Both latitude and longitude must be provided';
    RETURN;
  END IF;
  
  IF radius_miles <= 0 OR radius_miles > 500 THEN
    RAISE NOTICE 'Radius must be between 0 and 500 miles';
    RETURN;
  END IF;

  RAISE NOTICE 'Regional market analysis called for lat: %, lng: %, radius: % miles, user_retailer_id: %, bounding box: lat[%,%], lng[%,%]', 
    center_latitude, center_longitude, radius_miles, COALESCE(user_retailer_id, 'NULL'), min_lat, max_lat, min_lng, max_lng;

  RETURN QUERY
    -- Regional competitor products within radius
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      ROUND(
        (3959 * acos(
          cos(radians(center_latitude)) * 
          cos(radians(r.latitude::NUMERIC)) * 
          cos(radians(r.longitude::NUMERIC) - radians(center_longitude)) + 
          sin(radians(center_latitude)) * 
          sin(radians(r.latitude::NUMERIC))
        ))::NUMERIC, 2
      ) AS distance_miles,
      false AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      -- Fast bounding box pre-filter (much faster than Haversine)
      r.latitude IS NOT NULL 
      AND r.longitude IS NOT NULL
      AND r.latitude::NUMERIC BETWEEN min_lat AND max_lat
      AND r.longitude::NUMERIC BETWEEN min_lng AND max_lng
      -- Then precise radius filter (only on pre-filtered results)
      AND (3959 * acos(
        cos(radians(center_latitude)) * 
        cos(radians(r.latitude::NUMERIC)) * 
        cos(radians(r.longitude::NUMERIC) - radians(center_longitude)) + 
        sin(radians(center_latitude)) * 
        sin(radians(r.latitude::NUMERIC))
      )) <= radius_miles
      -- Exclude user's own products from competitor analysis
      AND (user_retailer_id IS NULL OR p.retailer_id != user_retailer_id)
      -- Standard quality filters
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
      -- Limit to top retailers by product count within radius (optimized with bounding box)
      AND p.retailer_id IN (
        SELECT p2.retailer_id 
        FROM products p2 
        INNER JOIN retailers r2 ON p2.retailer_id = r2.id::TEXT
        WHERE 
          r2.latitude IS NOT NULL 
          AND r2.longitude IS NOT NULL
          -- Fast bounding box pre-filter
          AND r2.latitude::NUMERIC BETWEEN min_lat AND max_lat
          AND r2.longitude::NUMERIC BETWEEN min_lng AND max_lng
          AND p2.latest_price > 0
          -- Only do expensive distance calc for already filtered results
          AND (3959 * acos(
            cos(radians(center_latitude)) * 
            cos(radians(r2.latitude::NUMERIC)) * 
            cos(radians(r2.longitude::NUMERIC) - radians(center_longitude)) + 
            sin(radians(center_latitude)) * 
            sin(radians(r2.latitude::NUMERIC))
          )) <= radius_miles
        GROUP BY p2.retailer_id 
        ORDER BY COUNT(*) DESC 
        LIMIT max_retailers
      )
      
    UNION ALL
    
    -- User products (if provided and within analysis region)
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      ROUND(
        (3959 * acos(
          cos(radians(center_latitude)) * 
          cos(radians(r.latitude::NUMERIC)) * 
          cos(radians(r.longitude::NUMERIC) - radians(center_longitude)) + 
          sin(radians(center_latitude)) * 
          sin(radians(r.latitude::NUMERIC))
        ))::NUMERIC, 2
      ) AS distance_miles,
      true AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      user_retailer_id IS NOT NULL
      AND p.retailer_id = user_retailer_id
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
    
    ORDER BY category, display_weight, latest_price
    LIMIT query_limit;
    
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in get_regional_market_data_by_radius: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Create a helper function for data quality statistics
CREATE OR REPLACE FUNCTION get_market_data_quality_stats(
  competitor_place_ids TEXT[],
  user_retailer_id TEXT DEFAULT NULL
)
RETURNS TABLE (
  total_products INTEGER,
  valid_products INTEGER,
  invalid_products INTEGER,
  categories_found INTEGER,
  price_range_min NUMERIC,
  price_range_max NUMERIC,
  avg_price NUMERIC,
  data_quality_score NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER AS total_products,
    COUNT(CASE WHEN p.latest_price > 0 AND p.latest_price < 10000 THEN 1 END)::INTEGER AS valid_products,
    COUNT(CASE WHEN p.latest_price <= 0 OR p.latest_price >= 10000 OR p.latest_price IS NULL THEN 1 END)::INTEGER AS invalid_products,
    COUNT(DISTINCT p.category)::INTEGER AS categories_found,
    MIN(CASE WHEN p.latest_price > 0 THEN p.latest_price END) AS price_range_min,
    MAX(CASE WHEN p.latest_price < 10000 THEN p.latest_price END) AS price_range_max,
    ROUND(AVG(CASE WHEN p.latest_price > 0 AND p.latest_price < 10000 THEN p.latest_price END), 2) AS avg_price,
    ROUND(
      (COUNT(CASE WHEN p.latest_price > 0 AND p.latest_price < 10000 THEN 1 END)::NUMERIC / 
       NULLIF(COUNT(*), 0)) * 100, 2
    ) AS data_quality_score
  FROM products p
  WHERE (
    p.retailer_id = ANY(competitor_place_ids) 
    OR (user_retailer_id IS NOT NULL AND p.retailer_id = user_retailer_id)
  );
END;
$$ LANGUAGE plpgsql;

-- FAST Alternative: Regional Market Analysis by Approximate Radius (bounding box only - much faster)
CREATE OR REPLACE FUNCTION get_regional_market_data_by_approximate_radius(
  center_latitude NUMERIC,
  center_longitude NUMERIC,
  radius_miles NUMERIC DEFAULT 30,
  user_retailer_id TEXT DEFAULT NULL,
  max_retailers INTEGER DEFAULT 30
)
RETURNS TABLE (
  category TEXT,
  subcategory TEXT,
  display_weight TEXT,
  latest_price NUMERIC,
  retailer_id TEXT,
  retailer_name TEXT,
  retailer_city TEXT,
  retailer_state TEXT,
  is_user_product BOOLEAN,
  product_count INTEGER
) AS $$
DECLARE
  query_limit INTEGER := 200000; -- Small limit for fast response
  price_lower_bound NUMERIC := 0.01;
  price_upper_bound NUMERIC := 10000.00;
  -- Calculate approximate bounding box for filtering
  lat_delta NUMERIC := radius_miles / 69.0; 
  lng_delta NUMERIC := radius_miles / (69.0 * cos(radians(center_latitude)));
  min_lat NUMERIC := center_latitude - lat_delta;
  max_lat NUMERIC := center_latitude + lat_delta;
  min_lng NUMERIC := center_longitude - lng_delta;
  max_lng NUMERIC := center_longitude + lng_delta;
BEGIN
  -- Input validation
  IF center_latitude IS NULL OR center_longitude IS NULL THEN
    RAISE NOTICE 'Both latitude and longitude must be provided';
    RETURN;
  END IF;
  
  RAISE NOTICE 'Fast regional market analysis called for lat: %, lng: %, approximate radius: % miles', 
    center_latitude, center_longitude, radius_miles;

  RETURN QUERY
    -- Regional competitor products within approximate radius (bounding box only)
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      false AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      -- Fast bounding box filter only (no expensive Haversine calculation)
      r.latitude IS NOT NULL 
      AND r.longitude IS NOT NULL
      AND r.latitude::NUMERIC BETWEEN min_lat AND max_lat
      AND r.longitude::NUMERIC BETWEEN min_lng AND max_lng
      -- Exclude user's own products from competitor analysis
      AND (user_retailer_id IS NULL OR p.retailer_id != user_retailer_id)
      -- Standard quality filters
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
      -- Limit to top retailers by product count (fast filter)
      AND p.retailer_id IN (
        SELECT p2.retailer_id 
        FROM products p2 
        INNER JOIN retailers r2 ON p2.retailer_id = r2.id::TEXT
        WHERE 
          r2.latitude IS NOT NULL 
          AND r2.longitude IS NOT NULL
          AND r2.latitude::NUMERIC BETWEEN min_lat AND max_lat
          AND r2.longitude::NUMERIC BETWEEN min_lng AND max_lng
          AND p2.latest_price > 0
        GROUP BY p2.retailer_id 
        ORDER BY COUNT(*) DESC 
        LIMIT max_retailers
      )
      
    UNION ALL
    
    -- User products (if provided)
    SELECT
      TRIM(p.category) AS category,
      TRIM(COALESCE(p.subcategory, 'General')) AS subcategory,
      TRIM(COALESCE(p.display_weight, 'unit')) AS display_weight,
      ROUND(p.latest_price::NUMERIC, 2) AS latest_price,
      p.retailer_id,
      COALESCE(r.dispensary_name, r.id::TEXT) AS retailer_name,
      r.city AS retailer_city,
      r.state AS retailer_state,
      true AS is_user_product,
      1 AS product_count
    FROM products p
    INNER JOIN retailers r ON p.retailer_id = r.id::TEXT
    WHERE
      user_retailer_id IS NOT NULL
      AND p.retailer_id = user_retailer_id
      AND p.latest_price IS NOT NULL
      AND p.latest_price >= price_lower_bound
      AND p.latest_price <= price_upper_bound
      AND p.category IS NOT NULL
      AND length(trim(p.category)) > 0
      AND p.category != ''
      AND p.category NOT ILIKE '%test%'
      AND p.category NOT ILIKE '%sample%'
      AND (p.product_name IS NULL OR length(trim(p.product_name)) > 0)
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%test%')
      AND (p.product_name IS NULL OR p.product_name NOT ILIKE '%sample%')
      AND NOT (p.latest_price = 0.01 OR p.latest_price = 9999.99 OR p.latest_price = 1.00)
    
    ORDER BY category, display_weight, latest_price
    LIMIT query_limit;
    
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in get_regional_market_data_by_approximate_radius: %', SQLERRM;
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Test the functions (optional - can be run to verify they work)
-- SELECT * FROM get_market_snapshot_data(ARRAY['8646', '14416'], '14187');
-- SELECT * FROM get_market_data_quality_stats(ARRAY['8646', '14416'], '14187');

-- SELECT * FROM get_regional_market_data_by_location('Detroit', 'Michigan', 'user_retailer_id', 25);

-- Radius-based analysis (with precise distance calculation - may be slower for large datasets):
-- SELECT * FROM get_regional_market_data_by_radius(42.3314, -83.0458, 50, 'user_retailer_id', 14187);

-- Fast approximate radius analysis (bounding box only - much faster, slightly less precise):
-- SELECT * FROM get_regional_market_data_by_approximate_radius(42.3314, -83.0458, 50, 'user_retailer_id', 14187);