-- Create fuzzy search functions for location-only and name-only searches
-- This migration adds the missing fuzzy search functions that are referenced in the code

-- Function to search retailers with fuzzy matching on location fields only (city, state)
CREATE OR REPLACE FUNCTION search_retailers_fuzzy_location_only(
  search_term TEXT,
  similarity_threshold FLOAT DEFAULT 0.3,
  max_results INTEGER DEFAULT 10
)
RETURNS SETOF retailers
LANGUAGE plpgsql
AS $$
BEGIN
  -- Search only location fields (city and state) with similarity scoring
  RETURN QUERY
  SELECT r.*
  FROM retailers r
  WHERE
    -- Check city similarity
    similarity(LOWER(r.city), LOWER(search_term)) > similarity_threshold OR
    -- Check state similarity  
    similarity(LOWER(r.state), LOWER(search_term)) > similarity_threshold OR
    -- Use pattern matching as a fallback for location fields only
    r.city ILIKE '%' || search_term || '%' OR
    r.state ILIKE '%' || search_term || '%'
  -- Order by most relevant location match
  ORDER BY 
    -- Primary sort - higher weight for exact location matches
    GREATEST(
      similarity(LOWER(r.city), <PERSON>OW<PERSON>(search_term)),
      similarity(LOWER(r.state), <PERSON>OWER(search_term))
    ) DESC,
    -- Secondary sort - exact matches first
    CASE WHEN 
      r.city ILIKE '%' || search_term || '%' OR 
      r.state ILIKE '%' || search_term || '%' 
    THEN 1 ELSE 0 END DESC
  LIMIT max_results;
END;
$$;

-- Function to search retailers with fuzzy matching on name fields only
CREATE OR REPLACE FUNCTION search_retailers_fuzzy_name_only(
  search_term TEXT,
  similarity_threshold FLOAT DEFAULT 0.3,
  max_results INTEGER DEFAULT 10
)
RETURNS SETOF retailers
LANGUAGE plpgsql
AS $$
BEGIN
  -- Search only name fields with similarity scoring
  RETURN QUERY
  SELECT r.*
  FROM retailers r
  WHERE
    -- Check name similarity (name and dispensary_name fields only)
    similarity(LOWER(r.name), LOWER(search_term)) > similarity_threshold OR
    similarity(LOWER(r.dispensary_name), LOWER(search_term)) > similarity_threshold OR
    -- Use pattern matching as a fallback for name fields only
    r.name ILIKE '%' || search_term || '%' OR
    r.dispensary_name ILIKE '%' || search_term || '%'
  -- Order by most relevant name match
  ORDER BY 
    -- Primary sort - higher weight for name fields
    GREATEST(
      similarity(LOWER(r.name), LOWER(search_term)),
      similarity(LOWER(r.dispensary_name), LOWER(search_term))
    ) DESC,
    -- Secondary sort - exact matches first
    CASE WHEN 
      r.name ILIKE '%' || search_term || '%' OR 
      r.dispensary_name ILIKE '%' || search_term || '%' 
    THEN 1 ELSE 0 END DESC
  LIMIT max_results;
END;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION search_retailers_fuzzy_location_only IS 'Fuzzy search for retailers by location fields only (city, state). Used for location-based radio button searches.';
COMMENT ON FUNCTION search_retailers_fuzzy_name_only IS 'Fuzzy search for retailers by name fields only (name, dispensary_name). Used for name-based radio button searches.';
