#!/usr/bin/env node

/**
 * Test the updated actionability endpoint with full insight data
 */

const axios = require('axios');
const knex = require('knex');

// Database configuration
const config = {
  client: 'mysql2',
  connection: {
    host: process.env.DB_HOST || 'mysql',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
};

const API_BASE_URL = 'http://localhost:3001';
const LOCATION_ID = 1;

async function testUpdatedActionability() {
  const db = knex(config);
  
  try {
    console.log('🧪 Testing Updated Actionability Endpoint...\n');
    
    // Get actual insights from the database
    const insights = await db('insights')
      .select('id', 'title', 'description', 'type', 'actions')
      .where('location_id', 1)
      .limit(5);

    console.log(`📋 Testing ${insights.length} real insights:\n`);

    for (const insight of insights) {
      console.log(`🔍 Testing: "${insight.title}"`);
      console.log(`   Type: ${insight.type}`);
      console.log(`   Actions: ${insight.actions}`);

      // Parse actions to show what we expect
      let actionsArray = [];
      if (insight.actions) {
        if (typeof insight.actions === 'string') {
          try {
            // Try parsing as JSON first
            actionsArray = JSON.parse(insight.actions);
          } catch {
            // If JSON parsing fails, split by comma (legacy format)
            actionsArray = insight.actions.split(',').map(a => a.trim()).filter(a => a.length > 0);
          }
        }
      }
      console.log(`   Parsed Actions (${actionsArray.length}): ${JSON.stringify(actionsArray.slice(0, 2))}...`);

      // Expected result based on our logic
      let expectedActionable = true;
      
      // Check if it should match non-actionable patterns
      const textToCheck = `${insight.title} ${insight.description || ''}`.toLowerCase();
      const nonActionablePatterns = [
        'develop a.*plan for',
        'prepare.*data',
        'create.*framework',
        'implement.*analysis'
      ];
      
      let matchesNonActionable = false;
      for (const pattern of nonActionablePatterns) {
        try {
          const regex = new RegExp(pattern, 'i');
          if (regex.test(textToCheck)) {
            console.log(`   ❌ Should match non-actionable pattern: "${pattern}"`);
            expectedActionable = false;
            matchesNonActionable = true;
            break;
          }
        } catch (error) {
          console.log(`   ⚠️  Error testing pattern: ${error.message}`);
        }
      }

      if (!matchesNonActionable) {
        // Check type + actions logic
        const hasValidType = insight.type === 'automation' || insight.type === 'campaign';
        const hasActions = actionsArray.length > 0;
        expectedActionable = hasValidType && hasActions;
        console.log(`   📊 Type check: ${hasValidType}, Actions check: ${hasActions}`);
      }

      console.log(`   🎯 Expected: ${expectedActionable ? 'ACTIONABLE' : 'NON-ACTIONABLE'}`);

      // Test the API (this will fail due to auth, but we can see the request structure)
      try {
        const response = await axios.post(
          `${API_BASE_URL}/api/admin/locations/${LOCATION_ID}/insights/check-actionability`,
          {
            title: insight.title,
            description: insight.description,
            type: insight.type,
            actions: insight.actions
          },
          {
            headers: { 'Content-Type': 'application/json' },
            validateStatus: function (status) { return status < 500; }
          }
        );

        if (response.status === 200 && response.data.success) {
          const actualActionable = response.data.data.isActionable;
          console.log(`   🔧 API Result: ${actualActionable ? 'ACTIONABLE' : 'NON-ACTIONABLE'}`);
          
          if (actualActionable === expectedActionable) {
            console.log('   ✅ MATCH!');
          } else {
            console.log('   ❌ MISMATCH!');
          }
        } else {
          console.log(`   ⚠️  API returned: ${response.status} (auth required)`);
        }
      } catch (error) {
        if (error.code === 'ECONNREFUSED') {
          console.log('   ⚠️  API server not accessible');
        } else {
          console.log(`   ⚠️  API error: ${error.message}`);
        }
      }

      console.log('');
    }

    console.log('📊 Summary:');
    console.log('   - Updated endpoint now accepts full insight data (type, actions)');
    console.log('   - Actions parsing handles both JSON and comma-separated formats');
    console.log('   - Logic matches the full actionability check');
    console.log('   - Non-actionable patterns take precedence over type+actions');
  } catch (error) {
    console.error('❌ Error in test:', error);
  } finally {
    await db.destroy();
  }
}

if (require.main === module) {
  testUpdatedActionability()
    .then(() => {
      console.log('✅ Test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testUpdatedActionability };
