#!/usr/bin/env node

/**
 * Test script for resend invitation functionality
 * This script tests the AdminInvitationService resend functionality
 */

// Load environment variables
require('dotenv').config();

const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || "127.0.0.1",
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
};

async function testResendInvitation() {
  let connection;
  
  try {
    console.log("🧪 Testing Resend Invitation Functionality...\n");

    // Connect to database
    connection = await mysql.createConnection(dbConfig);
    console.log("✅ Connected to database\n");

    // 1. Get organization admins (excluding the first one to simulate current user)
    console.log("1️⃣ Finding organization admins for testing...");
    const [admins] = await connection.execute(`
      SELECT id, email, first_name, last_name, role, organization_id, created_at
      FROM admins 
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    if (admins.length < 2) {
      console.log("❌ Need at least 2 admins to test resend functionality");
      console.log("💡 Create more admins through the UI first");
      return;
    }

    console.log(`✅ Found ${admins.length} admin(s):`);
    admins.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.email} (${admin.first_name} ${admin.last_name}) - Role: ${admin.role}`);
    });

    // 2. Simulate resend invitation (admin 1 sending to admin 2)
    const invitingAdmin = admins[0];
    const targetAdmin = admins[1];

    console.log(`\n2️⃣ Testing resend invitation:`);
    console.log(`   From: ${invitingAdmin.email} (${invitingAdmin.first_name} ${invitingAdmin.last_name})`);
    console.log(`   To: ${targetAdmin.email} (${targetAdmin.first_name} ${targetAdmin.last_name})`);

    // 3. Get organization details
    const [organizations] = await connection.execute(`
      SELECT id, username, domain
      FROM organizations 
      WHERE id = ?
    `, [invitingAdmin.organization_id]);

    if (organizations.length === 0) {
      console.log("❌ Organization not found!");
      return;
    }

    const organization = organizations[0];
    console.log(`   Organization: ${organization.username} (ID: ${organization.id})`);

    // 4. Check for email providers
    console.log(`\n3️⃣ Checking email provider availability...`);
    const [locations] = await connection.execute(`
      SELECT id, name FROM locations WHERE organization_id = ?
    `, [organization.id]);

    let hasEmailProvider = false;
    for (const location of locations) {
      const [providers] = await connection.execute(`
        SELECT id, name, type, is_default
        FROM providers 
        WHERE location_id = ? AND group_name = 'email'
      `, [location.id]);

      if (providers.length > 0) {
        console.log(`✅ Location ${location.name} has ${providers.length} email provider(s)`);
        providers.forEach(p => {
          console.log(`   - ${p.name} (${p.type}) ${p.is_default ? '[DEFAULT]' : ''}`);
        });
        hasEmailProvider = true;
      }
    }

    if (!hasEmailProvider) {
      console.log("❌ No email providers found!");
      console.log("💡 Configure an email provider in Location Settings → Providers");
      return;
    }

    // 5. Test the resend invitation service
    console.log(`\n4️⃣ Testing AdminInvitationService.resendInvitationEmail...`);
    
    try {
      const { AdminInvitationService } = require('../src/auth/AdminInvitationService');
      
      const success = await AdminInvitationService.resendInvitationEmail({
        admin: targetAdmin,
        organization,
        invitedBy: invitingAdmin,
      });

      if (success) {
        console.log("✅ Resend invitation email sent successfully!");
        console.log(`📬 Check ${targetAdmin.email} for the reminder email`);
        console.log("\n📧 Expected email content:");
        console.log(`   Subject: Reminder: Access your ${organization.username} account on BakedBot`);
        console.log(`   Content: Reminder about admin access with different messaging than initial invitation`);
      } else {
        console.log("❌ Failed to send resend invitation email");
        console.log("💡 Check the application logs for detailed error messages");
      }
    } catch (error) {
      console.error("❌ Error testing resend invitation:", error.message);
    }

    // 6. Test self-invitation prevention
    console.log(`\n5️⃣ Testing self-invitation prevention...`);
    
    try {
      const { AdminInvitationService } = require('../src/auth/AdminInvitationService');
      
      const success = await AdminInvitationService.resendInvitationEmail({
        admin: invitingAdmin, // Same as invitedBy
        organization,
        invitedBy: invitingAdmin,
      });

      if (!success) {
        console.log("✅ Self-invitation correctly prevented");
      } else {
        console.log("❌ Self-invitation was not prevented (this is a bug)");
      }
    } catch (error) {
      console.error("❌ Error testing self-invitation prevention:", error.message);
    }

    console.log(`\n🎯 SUMMARY:`);
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Admin data: ${admins.length} admin(s) found`);
    console.log(`✅ Organization: ${organization.username}`);
    console.log(`${hasEmailProvider ? '✅' : '❌'} Email providers: ${hasEmailProvider ? 'Available' : 'Missing'}`);
    console.log(`✅ Resend invitation service: Tested`);
    console.log(`✅ Self-invitation prevention: Tested`);
  } catch (error) {
    console.error("❌ Error during test:", error.message);
    console.error("Stack trace:", error.stack);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the script
if (require.main === module) {
  testResendInvitation();
}

module.exports = { testResendInvitation };
