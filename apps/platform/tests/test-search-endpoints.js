#!/usr/bin/env node

/**
 * Test script to validate the new search endpoints
 * Run this script to test both name and location-based searches
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3001'; // Adjust if your backend runs on a different port
const AUTH_TOKEN = process.env.AUTH_TOKEN || 'your-auth-token-here';

// Test queries - including the specific case mentioned by the user
const testQueries = {
  name: ['Green Dragon', 'Dispensary', 'Cannabis Store', 'Chicago'],
  location: ['Denver', 'Colorado', 'California', 'Los Angeles', 'Chicago', 'Hartford']
};

async function testEndpoint(endpoint, query, description) {
  try {
    console.log(`\n🧪 Testing ${description}: "${query}"`);
    console.log(`📡 Endpoint: ${endpoint}`);
    
    const response = await axios.get(`${BASE_URL}${endpoint}?query=${encodeURIComponent(query)}`, {
      headers: {
        Authorization: `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (response.status === 200 && response.data.retailers) {
      console.log(`✅ Success: Found ${response.data.retailers.length} results`);
      
      // Show first few results
      const results = response.data.retailers.slice(0, 3);
      results.forEach((retailer, index) => {
        console.log(`   ${index + 1}. ${retailer.name || retailer.dispensary_name} - ${retailer.city}, ${retailer.state}`);
      });
      
      return { success: true, count: response.data.retailers.length, data: response.data };
    } else {
      console.log(`⚠️  Unexpected response format`);
      return { success: false, error: 'Unexpected response format' };
    }
  } catch (error) {
    if (error.response) {
      console.log(`❌ HTTP Error ${error.response.status}: ${error.response.data?.error || error.message}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.log(`❌ Connection refused - is the backend server running on ${BASE_URL}?`);
    } else {
      console.log(`❌ Error: ${error.message}`);
    }
    return { success: false, error: error.message };
  }
}

async function testChicagoCase() {
  console.log('\n🔍 SPECIFIC TEST: Chicago Search Issue');
  console.log('=' .repeat(50));

  // Test the specific case mentioned by the user
  const query = 'Chicago';

  console.log(`\n📋 Testing "${query}" - Name vs Location Search`);

  // Test name search
  const nameResult = await testEndpoint(
    '/api/misc/retailers/business-search',
    query,
    'Business Name Search for "Chicago"'
  );

  // Test location search
  const locationResult = await testEndpoint(
    '/api/misc/retailers/business-search-location',
    query,
    'Business Location Search for "Chicago"'
  );

  console.log('\n📊 COMPARISON RESULTS:');
  console.log(`Name search found: ${nameResult.count || 0} results`);
  console.log(`Location search found: ${locationResult.count || 0} results`);

  if (nameResult.success && locationResult.success) {
    console.log('\n🔍 Checking for "Transcend Counseling Chicago, Hartford, Michigan":');

    // Check if the problematic business appears in name search (should be there)
    const nameHasTranscend = nameResult.data?.retailers?.some(r =>
      (r.name || r.dispensary_name || '').toLowerCase().includes('transcend') &&
      (r.name || r.dispensary_name || '').toLowerCase().includes('chicago')
    );

    // Check if the problematic business appears in location search (should NOT be there)
    const locationHasTranscend = locationResult.data?.retailers?.some(r =>
      (r.name || r.dispensary_name || '').toLowerCase().includes('transcend') &&
      (r.name || r.dispensary_name || '').toLowerCase().includes('chicago')
    );

    console.log(`✅ Name search includes Transcend: ${nameHasTranscend ? 'YES (correct)' : 'NO'}`);
    console.log(`${locationHasTranscend ? '❌' : '✅'} Location search includes Transcend: ${locationHasTranscend ? 'YES (INCORRECT - this is the bug!)' : 'NO (correct)'}`);

    if (locationHasTranscend) {
      console.log('\n🐛 BUG CONFIRMED: Location search is returning businesses with "Chicago" in the name instead of city!');
    } else {
      console.log('\n✅ No bug detected: Location search is working correctly!');
    }
  }
}

async function runTests() {
  console.log('🚀 Testing Radio Button Search Implementation');
  console.log('='.repeat(50));

  // Run the specific Chicago test first
  await testChicagoCase();

  const results = {
    businessName: [],
    businessLocation: [],
    competitorName: [],
    competitorLocation: []
  };

  // Test Business Search by Name
  console.log('\n📋 BUSINESS SEARCH BY NAME');
  for (const query of testQueries.name) {
    const result = await testEndpoint(
      '/api/misc/retailers/business-search',
      query,
      'Business Name Search'
    );
    results.businessName.push(result);
  }

  // Test Business Search by Location
  console.log('\n📋 BUSINESS SEARCH BY LOCATION');
  for (const query of testQueries.location) {
    const result = await testEndpoint(
      '/api/misc/retailers/business-search-location',
      query,
      'Business Location Search'
    );
    results.businessLocation.push(result);
  }

  // Test Competitor Search by Name
  console.log('\n📋 COMPETITOR SEARCH BY NAME');
  for (const query of testQueries.name) {
    const result = await testEndpoint(
      '/api/misc/retailers/search',
      query,
      'Competitor Name Search'
    );
    results.competitorName.push(result);
  }

  // Test Competitor Search by Location
  console.log('\n📋 COMPETITOR SEARCH BY LOCATION');
  for (const query of testQueries.location) {
    const result = await testEndpoint(
      '/api/misc/retailers/search-location',
      query,
      'Competitor Location Search'
    );
    results.competitorLocation.push(result);
  }

  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('='.repeat(50));
  
  const categories = [
    { name: 'Business Name Search', results: results.businessName },
    { name: 'Business Location Search', results: results.businessLocation },
    { name: 'Competitor Name Search', results: results.competitorName },
    { name: 'Competitor Location Search', results: results.competitorLocation }
  ];

  categories.forEach(category => {
    const successful = category.results.filter(r => r.success).length;
    const total = category.results.length;
    const totalResults = category.results
      .filter(r => r.success)
      .reduce((sum, r) => sum + (r.count || 0), 0);
    
    console.log(`${category.name}: ${successful}/${total} tests passed, ${totalResults} total results found`);
  });

  const allSuccessful = categories.every(cat => 
    cat.results.every(r => r.success)
  );

  if (allSuccessful) {
    console.log('\n🎉 All tests passed! Radio button search implementation is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the backend server and database connection.');
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testEndpoint, runTests };
