#!/usr/bin/env node

/**
 * Test script to verify pattern matching against specific insight titles
 */

const knex = require('knex');

// Database configuration using environment variables
const config = {
  client: 'mysql2',
  connection: {
    host: process.env.DB_HOST || 'mysql',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
};

async function testPatternMatching() {
  const db = knex(config);
  
  try {
    console.log('🧪 Testing Pattern Matching Against Insight Titles...\n');
    
    // Get the patterns we're testing
    const patterns = await db('insight_patterns')
      .whereIn('pattern', ['develop a.*plan for', 'prepare.*data'])
      .where('is_active', true);
    
    console.log('📋 Active patterns to test:');
    patterns.forEach(pattern => {
      console.log(`   - "${pattern.pattern}" (${pattern.description})`);
    });

    // Test cases
    const testCases = [
      'Develop a Marketing Plan for Future Campaigns',
      'Prepare for Data Collection',
      'develop a marketing plan for customers',
      'prepare customer data',
      'Send targeted email to customers',
      'Create automation for inactive users'
    ];

    console.log('\n🎯 Testing against insight titles:');
    
    for (const testCase of testCases) {
      console.log(`\n   Testing: "${testCase}"`);
      console.log(`   Lowercase: "${testCase.toLowerCase()}"`);
      
      let matched = false;
      for (const pattern of patterns) {
        try {
          const regex = new RegExp(pattern.pattern, 'i');
          const isMatch = regex.test(testCase.toLowerCase());
          
          if (isMatch) {
            console.log(`   ✅ MATCHES pattern: "${pattern.pattern}"`);
            console.log(`   📝 Description: ${pattern.description}`);
            console.log(`   🚫 Should be NON-ACTIONABLE`);
            matched = true;
          }
        } catch (error) {
          console.log(`   ❌ Error testing pattern "${pattern.pattern}": ${error.message}`);
        }
      }
      
      if (!matched) {
        console.log(`   ⚪ No pattern matches - should be ACTIONABLE`);
      }
    }

    // Test the specific patterns individually
    console.log('\n🔍 Individual pattern tests:');
    
    const specificTests = [
      {
        pattern: 'develop a.*plan for',
        testText: 'Develop a Marketing Plan for Future Campaigns',
        shouldMatch: true
      },
      {
        pattern: 'prepare.*data',
        testText: 'Prepare for Data Collection',
        shouldMatch: true
      },
      {
        pattern: 'develop a.*plan for',
        testText: 'Send targeted email',
        shouldMatch: false
      }
    ];

    for (const test of specificTests) {
      console.log(`\n   Pattern: "${test.pattern}"`);
      console.log(`   Text: "${test.testText}"`);
      console.log(`   Expected: ${test.shouldMatch ? 'MATCH' : 'NO MATCH'}`);
      
      try {
        const regex = new RegExp(test.pattern, 'i');
        const actualMatch = regex.test(test.testText.toLowerCase());
        console.log(`   Actual: ${actualMatch ? 'MATCH' : 'NO MATCH'}`);
        console.log(`   Result: ${actualMatch === test.shouldMatch ? '✅ CORRECT' : '❌ INCORRECT'}`);
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }

    console.log('\n📊 Summary:');
    console.log('   - Patterns are stored correctly in database');
    console.log('   - Regex matching works as expected');
    console.log('   - Case insensitive matching is working');
    console.log('   - The issue is likely in the UI not using server-side actionability check');
  } catch (error) {
    console.error('❌ Error in pattern matching test:', error);
  } finally {
    await db.destroy();
  }
}

if (require.main === module) {
  testPatternMatching()
    .then(() => {
      console.log('\n✅ Pattern matching test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testPatternMatching };
