#!/usr/bin/env node

/**
 * Test actions parsing logic
 */

function parseActions(actions) {
  console.log(`Input: ${JSON.stringify(actions)}`);
  console.log(`Type: ${typeof actions}`);
  
  let actionsArray = [];
  if (actions) {
    if (Array.isArray(actions)) {
      actionsArray = actions;
      console.log('Already an array');
    } else if (typeof actions === 'string') {
      try {
        // Try parsing as JSON first
        actionsArray = JSON.parse(actions);
        console.log('Parsed as JSO<PERSON> successfully');
      } catch (error) {
        console.log(`JSON parse failed: ${error.message}`);
        // If JSON parsing fails, split by comma (legacy format)
        actionsArray = actions.split(',').map(a => a.trim()).filter(a => a.length > 0);
        console.log('Parsed as comma-separated');
      }
    }
  }
  
  console.log(`Result: ${JSON.stringify(actionsArray)}`);
  console.log(`Length: ${actionsArray.length}`);
  console.log('');
  
  return actionsArray;
}

console.log('🧪 Testing Actions Parsing Logic...\n');

// Test cases
const testCases = [
  // JSON array format (what's actually in the database)
  '["Design a loyalty program structure", "Promote the program via social media and text", "Track participation and adjust based on customer feedback"]',
  
  // Comma-separated format (legacy)
  'Design a loyalty program structure,Promote the program via social media and text,Track participation and adjust based on customer feedback',
  
  // Already an array
  ["Design a loyalty program structure", "Promote the program via social media and text"],
  
  // Empty cases
  '',
  null,
  undefined,
  '[]',
  
  // Invalid JSON
  'Design a loyalty program structure,Promote the program via social media and text,Track participation and adjust based on customer feedback'
];

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}:`);
  parseActions(testCase);
});

console.log('✅ Actions parsing test completed!');
