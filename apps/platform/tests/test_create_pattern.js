#!/usr/bin/env node

/**
 * Test script to create a new insight pattern and verify the response structure
 */

const knex = require('knex');

// Database configuration using environment variables
const config = {
  client: 'mysql2',
  connection: {
    host: process.env.DB_HOST || 'mysql',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
};

async function testCreatePattern() {
  const db = knex(config);
  
  try {
    console.log('🧪 Testing Pattern Creation and Response Structure...\n');
    
    // Create a test pattern
    const testPattern = {
      pattern: 'test.*pattern',
      description: 'Test pattern for debugging',
      type: 'non_actionable',
      is_active: true,
      priority: 5,
      examples: JSON.stringify(['Test example 1', 'Test example 2', 'Test example 3'])
    };

    console.log('📝 Creating test pattern...');
    const [insertId] = await db('insight_patterns').insert(testPattern);
    console.log(`✅ Created pattern with ID: ${insertId}`);

    // Retrieve the pattern
    console.log('\n📊 Retrieving pattern from database...');
    const retrievedPattern = await db('insight_patterns').where('id', insertId).first();
    
    console.log('Raw pattern data:');
    console.log(`   ID: ${retrievedPattern.id}`);
    console.log(`   Pattern: ${retrievedPattern.pattern}`);
    console.log(`   Examples (raw): ${retrievedPattern.examples}`);
    console.log(`   Examples type: ${typeof retrievedPattern.examples}`);

    // Simulate the API transformation
    console.log('\n🔄 Simulating API transformation...');
    let examples = [];
    
    if (!retrievedPattern.examples) {
      examples = [];
    } else if (Array.isArray(retrievedPattern.examples)) {
      examples = retrievedPattern.examples;
    } else if (typeof retrievedPattern.examples === 'string') {
      try {
        const parsed = JSON.parse(retrievedPattern.examples);
        examples = Array.isArray(parsed) ? parsed : [];
      } catch {
        examples = [];
      }
    }

    const apiResponse = {
      ...retrievedPattern,
      examples,
    };

    console.log('API response format:');
    console.log(`   Examples: ${JSON.stringify(apiResponse.examples)}`);
    console.log(`   Examples type: ${typeof apiResponse.examples}`);
    console.log(`   Examples is array: ${Array.isArray(apiResponse.examples)}`);
    console.log(`   Examples length: ${apiResponse.examples.length}`);

    // Test the UI operations
    console.log('\n🎯 Testing UI operations...');
    try {
      const sliced = apiResponse.examples.slice(0, 3);
      console.log(`   ✅ slice(0, 3): ${JSON.stringify(sliced)}`);
      
      const mapped = sliced.map((example, index) => `${index}: ${example}`);
      console.log(`   ✅ map(): ${JSON.stringify(mapped)}`);
      
      console.log(`   ✅ Length check: ${apiResponse.examples.length > 0}`);
      console.log(`   ✅ Array check: ${Array.isArray(apiResponse.examples)}`);
    } catch (error) {
      console.log(`   ❌ UI operation error: ${error.message}`);
    }

    // Clean up - delete the test pattern
    console.log('\n🧹 Cleaning up...');
    await db('insight_patterns').where('id', insertId).del();
    console.log('✅ Test pattern deleted');

    console.log('\n🎉 All tests passed! The pattern creation and response structure work correctly.');
  } catch (error) {
    console.error('❌ Error in test:', error);
  } finally {
    await db.destroy();
  }
}

if (require.main === module) {
  testCreatePattern()
    .then(() => {
      console.log('\n✅ Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCreatePattern };
