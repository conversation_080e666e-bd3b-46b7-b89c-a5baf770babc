#!/usr/bin/env node

/**
 * Test script for email template generation with undefined values
 * This script tests the AdminInvitationService template generation to ensure no 'undefined' appears
 */

console.log("🧪 Testing Email Template Generation...\n");

try {
  // Import the AdminInvitationService
  const { AdminInvitationService } = require('../src/auth/AdminInvitationService');
  
  // Create test data that simulates the undefined issue
  const testAdmin = {
    id: 1,
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    organization_id: 1,
    role: 'admin'
  };

  const testOrganization = {
    id: 1,
    username: '<EMAIL>',
    domain: 'northwestern.edu'
  };

  // Test case 1: Admin with undefined first_name, last_name, and email
  const problematicInvitedBy = {
    id: 2,
    first_name: undefined,
    last_name: undefined,
    email: undefined,
    organization_id: 1,
    role: 'owner'
  };

  console.log("📧 Test Case 1: Inviter with all undefined values");
  console.log("Inviter data:", JSON.stringify(problematicInvitedBy, null, 2));

  // Access the private method for testing (this is a hack for testing)
  const email1 = AdminInvitationService.generateInvitationEmail({
    admin: testAdmin,
    organization: testOrganization,
    invitedBy: problematicInvitedBy
  });

  console.log("Generated email subject:", email1.subject);
  console.log("Generated email from:", email1.from);
  console.log("Generated email to:", email1.to);
  
  // Check for 'undefined' in the email content
  const hasUndefinedInHtml = email1.html.includes('undefined');
  const hasUndefinedInText = email1.text.includes('undefined');
  
  console.log(`HTML contains 'undefined': ${hasUndefinedInHtml ? '❌ YES' : '✅ NO'}`);
  console.log(`Text contains 'undefined': ${hasUndefinedInText ? '❌ YES' : '✅ NO'}`);
  
  if (hasUndefinedInHtml || hasUndefinedInText) {
    console.log("\n❌ FAILED: Email still contains 'undefined'");
    console.log("\nHTML content preview:");
    console.log(email1.html.substring(0, 500) + "...");
    console.log("\nText content preview:");
    console.log(email1.text.substring(0, 300) + "...");
  } else {
    console.log("\n✅ SUCCESS: No 'undefined' found in email content");
  }

  // Test case 2: Admin with partial data
  console.log("\n📧 Test Case 2: Inviter with only first name");
  const partialInvitedBy = {
    id: 3,
    first_name: 'John',
    last_name: undefined,
    email: '<EMAIL>',
    organization_id: 1,
    role: 'admin'
  };

  const email2 = AdminInvitationService.generateInvitationEmail({
    admin: testAdmin,
    organization: testOrganization,
    invitedBy: partialInvitedBy
  });

  const hasUndefinedInHtml2 = email2.html.includes('undefined');
  const hasUndefinedInText2 = email2.text.includes('undefined');
  
  console.log(`HTML contains 'undefined': ${hasUndefinedInHtml2 ? '❌ YES' : '✅ NO'}`);
  console.log(`Text contains 'undefined': ${hasUndefinedInText2 ? '❌ YES' : '✅ NO'}`);

  // Test case 3: Admin with complete data
  console.log("\n📧 Test Case 3: Inviter with complete data");
  const completeInvitedBy = {
    id: 4,
    first_name: 'Jane',
    last_name: 'Doe',
    email: '<EMAIL>',
    organization_id: 1,
    role: 'admin'
  };

  const email3 = AdminInvitationService.generateInvitationEmail({
    admin: testAdmin,
    organization: testOrganization,
    invitedBy: completeInvitedBy
  });

  const hasUndefinedInHtml3 = email3.html.includes('undefined');
  const hasUndefinedInText3 = email3.text.includes('undefined');
  
  console.log(`HTML contains 'undefined': ${hasUndefinedInHtml3 ? '❌ YES' : '✅ NO'}`);
  console.log(`Text contains 'undefined': ${hasUndefinedInText3 ? '❌ YES' : '✅ NO'}`);

  // Summary
  const allTestsPassed = !hasUndefinedInHtml && !hasUndefinedInText && 
                         !hasUndefinedInHtml2 && !hasUndefinedInText2 && 
                         !hasUndefinedInHtml3 && !hasUndefinedInText3;

  console.log(`\n🎯 OVERALL RESULT: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allTestsPassed) {
    console.log("🎉 The 'undefined' issue has been fixed!");
    console.log("💡 The email templates now handle missing admin data gracefully.");
  }
} catch (error) {
  console.error("❌ Error during template test:", error.message);
  console.error("Stack trace:", error.stack);
}

console.log("\n✨ Template test completed!");
