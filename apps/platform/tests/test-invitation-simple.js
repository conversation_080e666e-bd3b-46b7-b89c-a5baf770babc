#!/usr/bin/env node

/**
 * Simple test for admin invitation functionality
 * This script tests if the AdminInvitationService can be imported and basic functionality works
 */

console.log("🧪 Testing Admin Invitation Service Import...");

try {
  // Test if we can import the service
  const { AdminInvitationService } = require('../src/auth/AdminInvitationService');
  console.log("✅ AdminInvitationService imported successfully");
  
  // Test if the service has the expected methods
  if (typeof AdminInvitationService.sendInvitationEmail === 'function') {
    console.log("✅ sendInvitationEmail method exists");
  } else {
    console.log("❌ sendInvitationEmail method missing");
  }
  
  console.log("✅ Basic import test passed");
} catch (error) {
  console.error("❌ Failed to import AdminInvitationService:", error.message);
  console.error("Stack trace:", error.stack);
}

console.log("\n💡 To test email sending:");
console.log("1. Ensure you have email providers configured in your locations");
console.log("2. Try adding a new admin through the UI");
console.log("3. Check the application logs for detailed error messages");
console.log("4. Run the debug SQL script to check your email provider configuration");
