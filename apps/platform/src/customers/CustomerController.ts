/**
 * @swagger
 * tags:
 *   name: Customer
 *   description: |
 *     Customer self-service endpoints for authenticated users.
 *
 *     ## Authentication
 *
 *     Customer endpoints require dual authentication:
 *
 *     **Headers Required:**
 *     - `Authorization: Bearer your_location_api_key` (Required - identifies the location)
 *     - `x-user-token: user_jwt_token` (Required - identifies the specific user)
 *
 *     This allows users who belong to multiple locations to access their data for a specific location.
 */

import Router from "@koa/router";
import { ParameterizedContext, Next } from "koa";
import { logger } from "../config/logger";
import { LocationState, verify } from "../auth/AuthMiddleware";
import { getLocation, getLocationApiKey } from "../locations/LocationService";
import { RequestError } from "../core/errors";
import { LocationError } from "../locations/LocationError";
import { getUserFromClientId } from "../users/UserRepository";
import Location from "../locations/Location";
import App from "../app";
import * as admin from "firebase-admin";

// Define interfaces for customer context
interface CustomerState {
  apiKey?: any;
  locationId?: number;
  location?: Location;
  user?: any;
}

const router = new Router<CustomerState>({
  prefix: "/customer",
});

// Define JWT token payload interface (same as OrderController)
interface JwtPayload {
  sub: string;
  exp?: number;
  iat?: number;
}

// Firebase token verification function
const verifyFirebaseToken = async (idToken: string) => {
  try {
    // Get the initialized Firebase admin instance
    const firebaseApp = admin.apps.find((app) => app !== null);
    if (!firebaseApp) {
      throw new Error("Firebase admin not initialized");
    }

    const decodedToken = await firebaseApp.auth().verifyIdToken(idToken, true);
    return decodedToken;
  } catch (error) {
    logger.warn({
      message: "Firebase token verification failed",
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
};

// Customer middleware that implements dual authentication (API key + user token)
const customerAuthMiddleware = async (
  ctx: ParameterizedContext<CustomerState>,
  next: Next
) => {
  try {
    // Step 1: Validate API key (required)
    const authHeader = String(ctx.request.headers.authorization || "");
    let apiToken: string | undefined;

    if (authHeader.startsWith("Bearer ")) {
      apiToken = authHeader.substring(7, authHeader.length);
    }

    if (!apiToken) {
      logger.warn({
        message: "CustomerController: Missing API key",
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("API key is required", 401);
    }

    // Validate API key
    const apiKey = await getLocationApiKey(apiToken);
    if (!apiKey) {
      const maskedToken =
        apiToken.length > 8
          ? `${apiToken.substring(0, 4)}...${apiToken.substring(
              apiToken.length - 4
            )}`
          : "***";

      logger.warn({
        message: "CustomerController: Invalid API key",
        masked_token: maskedToken,
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("Invalid API key", 401);
    }

    // Store API key and location ID in context state
    ctx.state.apiKey = apiKey;
    ctx.state.locationId = apiKey.location_id;

    // Get full location details
    const location = await Location.find(apiKey.location_id);
    if (!location) {
      logger.warn({
        message: "CustomerController: Location not found",
        location_id: apiKey.location_id,
        path: ctx.path,
      });
      throw new RequestError("Location not found", 404);
    }

    ctx.state.location = location;

    // Step 2: Validate user token (required for customer endpoints)
    const userAuthHeader = ctx.request.headers["x-user-token"] as string;
    if (!userAuthHeader) {
      throw new RequestError("User token is required", 401);
    }

    let user;
    try {
      // Use Firebase token verification instead of generic JWT verification
      const decoded = await verifyFirebaseToken(userAuthHeader);

      if (!decoded.sub) {
        throw new RequestError("Invalid user token payload", 401);
      }

      const auth_id = decoded.sub;

      // Get user from the specific location using auth_id
      user = await getUserFromClientId(apiKey.location_id, {
        auth_id,
      });

      if (!user) {
        logger.warn({
          message: "User not found with auth token in this location",
          location_id: apiKey.location_id,
          auth_id,
          path: ctx.path,
        });
        throw new RequestError("User not found in this location", 404);
      }

      ctx.state.user = user;

      logger.info({
        message: "CustomerController: Dual authentication successful",
        location_id: apiKey.location_id,
        user_id: user.id,
        path: ctx.path,
      });
    } catch (error) {
      if (error instanceof RequestError) {
        throw error;
      }

      logger.warn({
        message: "CustomerController: Error verifying user token",
        error: error instanceof Error ? error.message : "Unknown error",
        path: ctx.path,
      });
      throw new RequestError("Invalid user token", 401);
    }

    return next();
  } catch (error) {
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 401;
      ctx.body = { error: error.message };
    } else {
      logger.error({
        message: "CustomerController: Unexpected auth error",
        error: error instanceof Error ? error.message : "Unknown error",
        path: ctx.path,
      });
      ctx.status = 500;
      ctx.body = { error: "Authentication error" };
    }
  }
};

/**
 * @swagger
 * /customer/me:
 *   get:
 *     summary: Get Current User Data
 *     description: |
 *       Retrieves the authenticated customer's own user data for a specific location.
 *
 *       **Headers Required:**
 *       - `Authorization: Bearer your_location_api_key` (Required - identifies the location)
 *       - `x-user-token: user_jwt_token` (Required - identifies the specific user)
 *     tags: [Customer]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     responses:
 *       200:
 *         description: User data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized - invalid or missing API key or user token
 *       404:
 *         description: User not found in this location
 */
router.get(
  "/me",
  customerAuthMiddleware,
  async (ctx: ParameterizedContext<CustomerState>) => {
    // For customer scope, return the authenticated user's data
    if (!ctx.state.user) {
      ctx.status = 404;
      ctx.body = { error: "User not found" };
      return;
    }

    // Return the authenticated user's data
    ctx.body = {
      ...ctx.state.user,
      name: `${ctx.state.user.data?.first_name || ""} ${
        ctx.state.user.data?.last_name || ""
      }`.trim(),
    };
  }
);

/**
 * @swagger
 * /customer/orders:
 *   get:
 *     summary: Get My Orders
 *     description: |
 *       Retrieves the authenticated customer's own orders with pagination for a specific location.
 *
 *       **Headers Required:**
 *       - `Authorization: Bearer your_location_api_key` (Required - identifies the location)
 *       - `x-user-token: user_jwt_token` (Required - identifies the specific user)
 *     tags: [Customer]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: string
 *         description: Cursor for pagination
 *       - in: query
 *         name: page
 *         schema:
 *           type: string
 *           enum: [prev, next]
 *         description: Page direction
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, cancelled]
 *         description: Filter orders by status
 *     responses:
 *       200:
 *         description: Customer's order history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Order'
 *                       - type: object
 *                         properties:
 *                           items:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/OrderItem'
 *                 nextCursor:
 *                   type: string
 *                 prevCursor:
 *                   type: string
 *                 limit:
 *                   type: integer
 *       401:
 *         description: Unauthorized - invalid or missing API key or user token
 *       404:
 *         description: User not found in this location
 */
router.get(
  "/orders",
  customerAuthMiddleware,
  async (ctx: ParameterizedContext<CustomerState>) => {
    try {
      if (!ctx.state.location) {
        throw new RequestError("Location not found", 404);
      }
      const locationId = ctx.state.location.id;

      // Get authenticated customer user
      if (!ctx.state.user || !ctx.state.user.id) {
        ctx.status = 404;
        ctx.body = { error: "User not found" };
        return;
      }

      const userId = ctx.state.user.id;

      logger.info({
        message: "Getting customer orders",
        userId,
        locationId,
        query: ctx.query,
        path: ctx.path,
      });

      // Extract cursor pagination parameters
      const cursor = ctx.query.cursor as string;
      const page = (ctx.query.page as string) || "next";
      const limit = Math.min(
        100,
        Math.max(1, parseInt(ctx.query.limit as string) || 20)
      );

      // Extract status filter
      const statusFilter = (ctx.query.status as string)?.trim();

      // Build query for customer's orders only
      let query = App.main.db("orders").where({
        "orders.location_id": locationId,
        "orders.user_id": userId,
      });

      // Apply status filter if provided
      if (statusFilter) {
        query = query.where("orders.status", statusFilter);
      }

      // Apply sorting by creation date (most recent first)
      query = query.orderBy("orders.created_at", "desc");

      // Apply cursor pagination
      if (cursor) {
        const operator = page === "prev" ? ">" : "<";
        query = query.where("orders.id", operator, cursor);
      }

      // Get one extra item to check if there are more results
      const orders = await query.limit(limit + 1).select("orders.*");

      const hasMore = orders.length > limit;
      if (hasMore) {
        orders.pop(); // Remove the extra item we fetched
      }

      // Get order items for each order
      const ordersWithItems = await Promise.all(
        orders.map(async (order) => {
          const items = await App.main
            .db("order_items")
            .where("order_id", order.id)
            .select("*");

          return {
            ...order,
            items,
          };
        })
      );

      logger.info({
        message: "Retrieved customer orders",
        count: ordersWithItems.length,
        hasMore,
        userId,
        locationId,
        statusFilter,
      });

      ctx.body = {
        results: ordersWithItems,
        nextCursor: hasMore
          ? ordersWithItems[ordersWithItems.length - 1]?.id
          : undefined,
        prevCursor: cursor,
        limit,
      };
    } catch (error) {
      logger.error({
        message: "Error fetching customer orders",
        error: error instanceof Error ? error.message : "Unknown error",
        userId: ctx.state.user?.id,
        locationId: ctx.state.location?.id,
      });

      ctx.status = 500;
      ctx.body = { error: "Failed to fetch orders" };
    }
  }
);

/**
 * @swagger
 * /customer/orders/{id}:
 *   get:
 *     summary: Get My Order Details
 *     description: |
 *       Retrieves detailed information about a specific order belonging to the authenticated customer.
 *
 *       **Headers Required:**
 *       - `Authorization: Bearer your_location_api_key` (Required - identifies the location)
 *       - `x-user-token: user_jwt_token` (Required - identifies the specific user)
 *     tags: [Customer]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order details
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Order'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/OrderItem'
 *       401:
 *         description: Unauthorized - invalid or missing API key or user token
 *       404:
 *         description: Order not found in this location or does not belong to customer
 */
router.get(
  "/orders/:id",
  customerAuthMiddleware,
  async (ctx: ParameterizedContext<CustomerState>) => {
    try {
      if (!ctx.state.location) {
        throw new RequestError("Location not found", 404);
      }
      const locationId = ctx.state.location.id;

      // Get authenticated customer user
      if (!ctx.state.user || !ctx.state.user.id) {
        ctx.status = 404;
        ctx.body = { error: "User not found" };
        return;
      }

      const userId = ctx.state.user.id;
      const orderId = parseInt(ctx.params.id);

      // Get order that belongs to the authenticated customer
      const order = await App.main
        .db("orders")
        .where({
          "orders.id": orderId,
          "orders.location_id": locationId,
          "orders.user_id": userId, // Ensure customer can only see their own orders
        })
        .select("orders.*")
        .first();

      if (!order) {
        ctx.status = 404;
        ctx.body = { error: "Order not found" };
        return;
      }

      // Get order items
      const items = await App.main
        .db("order_items")
        .where("order_id", orderId)
        .select("*");

      // Return the order with items
      ctx.body = {
        ...order,
        items,
      };
    } catch (error) {
      logger.error("Error getting customer order details:", error);
      ctx.status = 500;
      ctx.body = {
        error: "An error occurred while fetching the order",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

export default router;
