import { SchemaAwareSegmentationService } from "../tools/segmentation/SchemaAwareSegmentationService";
import { RuleValidationService } from "../tools/segmentation/RuleValidationService";
import { RelationshipAwareRuleGenerator } from "../tools/segmentation/RelationshipAwareRuleGenerator";
import { RuleTree } from "../rules/Rule";

// Mock DataHub tool
const mockDataHubTool = {
  execute: jest.fn(),
};

describe("SchemaAwareSegmentationService", () => {
  let service: SchemaAwareSegmentationService;

  beforeEach(async () => {
    service = new SchemaAwareSegmentationService(mockDataHubTool);
    await service.initialize();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Schema Building", () => {
    test("should build comprehensive data schema", async () => {
      const schemaForAI = service.getSchemaForAI();
      
      expect(schemaForAI).toContain("pos_data");
      expect(schemaForAI).toContain("products");
      expect(schemaForAI).toContain("users");
      expect(schemaForAI).toContain("customer_name");
      expect(schemaForAI).toContain("gross_sales");
      expect(schemaForAI).toContain("Join on product_name");
    });

    test("should include relationship information", async () => {
      const schemaForAI = service.getSchemaForAI();
      
      expect(schemaForAI).toContain("pos_data ↔ products");
      expect(schemaForAI).toContain("pos_data ↔ locations");
      expect(schemaForAI).toContain("Available Join Patterns");
    });

    test("should include field constraints", async () => {
      const schemaForAI = service.getSchemaForAI();
      
      expect(schemaForAI).toContain("Constraints:");
      expect(schemaForAI).toContain("recreational");
      expect(schemaForAI).toContain("medical");
    });
  });

  describe("Schema-Aware Segmentation", () => {
    beforeEach(() => {
      // Mock sample data response
      mockDataHubTool.execute.mockResolvedValue({
        status: "success",
        data: {
          results: [
            {
              customer_name: "John Doe",
              customer_type: "recreational",
              order_date: "2024-01-15",
              gross_sales: 75.50,
              net_sales: 70.00,
              master_category: "Flower",
              product_name: "Blue Dream",
              birth_date: "1990-05-15",
              budtender_name: "Alice",
              inventory_profit: 25.00,
            },
            {
              customer_name: "Jane Smith",
              customer_type: "medical",
              order_date: "2024-01-16",
              gross_sales: 120.00,
              net_sales: 115.00,
              master_category: "Edibles",
              product_name: "CBD Gummies",
              birth_date: "1985-08-22",
              budtender_name: "Bob",
              inventory_profit: 40.00,
            },
          ],
        },
      });
    });

    test("should run schema-aware segmentation successfully", async () => {
      const result = await service.runSchemaAwareSegmentation({
        location_id: 1,
        start_date: "2024-01-01",
        end_date: "2024-01-31",
        segmentation_strategy: "hybrid",
      });

      expect(result.segments).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.metadata.schemaVersion).toBe("1.0.0");
      expect(result.metadata.dataQuality).toBeGreaterThan(0);
    });

    test("should handle different segmentation strategies", async () => {
      const strategies = ["behavioral", "demographic", "value_based", "hybrid"];

      for (const strategy of strategies) {
        const result = await service.runSchemaAwareSegmentation({
          location_id: 1,
          start_date: "2024-01-01",
          end_date: "2024-01-31",
          segmentation_strategy: strategy as any,
        });

        expect(result.segments).toBeDefined();
        expect(result.metadata.generatedRules).toBeGreaterThan(0);
      }
    });

    test("should calculate data quality metrics", async () => {
      // Mock data quality query response
      mockDataHubTool.execute.mockResolvedValueOnce({
        status: "success",
        data: {
          results: [
            {
              total_records: 100,
              records_with_customer: 95,
              records_with_birthdate: 80,
              records_with_product: 98,
            },
          ],
        },
      });

      const result = await service.runSchemaAwareSegmentation({
        location_id: 1,
        start_date: "2024-01-01",
        end_date: "2024-01-31",
      });

      expect(result.metadata.dataQuality).toBeCloseTo(0.935, 2); // Weighted average
    });
  });
});

describe("RuleValidationService", () => {
  let validationService: RuleValidationService;
  let mockSchema: any;

  beforeEach(() => {
    mockSchema = {
      tables: {
        pos_data: {
          name: "pos_data",
          fields: [
            {
              name: "customer_name",
              type: "string",
              description: "Customer name",
              nullable: true,
            },
            {
              name: "gross_sales",
              type: "number",
              description: "Sales amount",
              nullable: false,
              constraints: { min: 0 },
            },
            {
              name: "customer_type",
              type: "string",
              description: "Customer type",
              nullable: true,
              constraints: { enum: ["recreational", "medical"] },
            },
          ],
        },
      },
    };

    validationService = new RuleValidationService(mockSchema);
  });

  describe("Rule Validation", () => {
    test("should validate valid rules", () => {
      const validRule: RuleTree = {
        uuid: "test-uuid",
        type: "string",
        group: "pos",
        path: "$.customer_name",
        operator: "=",
        value: "John Doe",
      };

      const result = validationService.validateRule(validRule);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.enhancedRule).toBeDefined();
    });

    test("should detect invalid field types", () => {
      const invalidRule: RuleTree = {
        uuid: "test-uuid",
        type: "number", // Wrong type for string field
        group: "pos",
        path: "$.customer_name",
        operator: "=",
        value: "John Doe",
      };

      const result = validationService.validateRule(invalidRule);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes("Type mismatch"))).toBe(true);
    });

    test("should validate enum constraints", () => {
      const invalidEnumRule: RuleTree = {
        uuid: "test-uuid",
        type: "string",
        group: "pos",
        path: "$.customer_type",
        operator: "=",
        value: "invalid_type", // Not in enum
      };

      const result = validationService.validateRule(invalidEnumRule);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes("not in allowed values"))).toBe(true);
    });

    test("should validate numeric constraints", () => {
      const invalidNumericRule: RuleTree = {
        uuid: "test-uuid",
        type: "number",
        group: "pos",
        path: "$.gross_sales",
        operator: "=",
        value: -10, // Below minimum
      };

      const result = validationService.validateRule(invalidNumericRule);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes("below minimum"))).toBe(true);
    });

    test("should validate wrapper rules", () => {
      const validWrapperRule: RuleTree = {
        uuid: "test-uuid",
        type: "wrapper",
        group: "pos",
        path: "$",
        operator: "and",
        children: [
          {
            uuid: "child-uuid",
            type: "string",
            group: "pos",
            path: "$.customer_name",
            operator: "=",
            value: "John Doe",
          },
        ],
      };

      const result = validationService.validateRule(validWrapperRule);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test("should detect invalid wrapper rules", () => {
      const invalidWrapperRule: RuleTree = {
        uuid: "test-uuid",
        type: "wrapper",
        group: "pos",
        path: "$",
        operator: "=", // Invalid operator for wrapper
        children: [],
      };

      const result = validationService.validateRule(invalidWrapperRule);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes("Wrapper rule must use logical operator"))).toBe(true);
      expect(result.errors.some(e => e.includes("Wrapper rule must have children"))).toBe(true);
    });
  });

  describe("Rule Enhancement", () => {
    test("should enhance rules with field metadata", () => {
      const rule: RuleTree = {
        uuid: "test-uuid",
        type: "string",
        group: "pos",
        path: "$.customer_name",
        operator: "=",
        value: "John Doe",
      };

      const result = validationService.validateRule(rule);

      expect(result.enhancedRule).toBeDefined();
      expect((result.enhancedRule as any).fieldMetadata).toBeDefined();
      expect((result.enhancedRule as any).fieldMetadata.description).toBe("Customer name");
    });

    test("should provide improvement suggestions", () => {
      const rule: RuleTree = {
        uuid: "test-uuid",
        type: "string",
        group: "pos",
        path: "$.customer_name",
        operator: ">", // Unusual operator for string
        value: "John Doe",
      };

      const result = validationService.validateRule(rule);

      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });
  });
});

describe("RelationshipAwareRuleGenerator", () => {
  let generator: RelationshipAwareRuleGenerator;
  let mockSchema: any;

  beforeEach(() => {
    mockSchema = {
      tables: {
        pos_data: {
          name: "pos_data",
          relationships: {
            product: {
              table: "products",
              localField: "product_name",
              foreignField: "product_name",
              type: "belongs_to",
            },
          },
        },
        products: {
          name: "products",
          relationships: {
            pos_transactions: {
              table: "pos_data",
              localField: "product_name",
              foreignField: "product_name",
              type: "has_many",
            },
          },
        },
      },
    };

    generator = new RelationshipAwareRuleGenerator(mockSchema);
  });

  describe("Rule Generation", () => {
    test("should generate relationship-aware rules", () => {
      const context = {
        schema: mockSchema,
        availableFields: ["pos_data.customer_name", "products.latest_price"],
        sampleData: [],
        businessRules: ["High-value customers spend more than $500"],
        segmentationGoals: ["Identify high-value customers"],
      };

      const rules = generator.generateSegmentationRules(context);

      expect(rules.length).toBeGreaterThan(0);
      expect(rules.some(r => r.requiredJoins.length > 0)).toBe(true);
      expect(rules.some(r => r.complexity === "moderate" || r.complexity === "complex")).toBe(true);
    });

    test("should generate SQL queries for rules", () => {
      const rule = {
        rule: {
          uuid: "test-uuid",
          type: "wrapper" as const,
          group: "pos" as const,
          path: "$",
          operator: "and" as const,
          children: [
            {
              uuid: "child-uuid",
              type: "number" as const,
              group: "pos" as const,
              path: "pos_data.gross_sales",
              operator: ">" as const,
              value: 100,
            },
          ],
        },
        requiredJoins: [
          {
            leftTable: "pos_data",
            rightTable: "products",
            leftField: "product_name",
            rightField: "product_name",
            joinType: "INNER" as const,
          },
        ],
        affectedTables: ["pos_data", "products"],
        complexity: "moderate" as const,
        estimatedPerformance: "medium" as const,
      };

      const sql = generator.generateSQLQuery(rule, 1);

      expect(sql).toContain("SELECT DISTINCT pos_data.customer_name");
      expect(sql).toContain("INNER JOIN products");
      expect(sql).toContain("WHERE pos_data.location_id = 1");
      expect(sql).toContain("pos_data.gross_sales > 100");
    });

    test("should estimate rule performance", () => {
      const rule = {
        rule: {} as RuleTree,
        requiredJoins: [
          {
            leftTable: "pos_data",
            rightTable: "products",
            leftField: "product_name",
            rightField: "product_name",
            joinType: "INNER" as const,
          },
        ],
        affectedTables: ["pos_data", "products"],
        complexity: "complex" as const,
        estimatedPerformance: "slow" as const,
      };

      const performance = generator.estimateRulePerformance(rule);

      expect(performance.estimatedRows).toBeGreaterThan(0);
      expect(performance.indexUsage.length).toBeGreaterThan(0);
      expect(performance.recommendations.length).toBeGreaterThan(0);
    });
  });
});
