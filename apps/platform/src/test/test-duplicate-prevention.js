#!/usr/bin/env node

/**
 * Test script to verify duplicate prevention is working
 */

// Load environment variables
require('dotenv').config();

const knex = require("knex");

// Database configuration
const dbConfig = {
  client: "mysql2",
  connection: {
    host: process.env.DB_HOST || "127.0.0.1",
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
};

// Initialize database connection
const db = knex(dbConfig);

async function testDuplicatePrevention() {
  try {
    console.log("Testing duplicate prevention...\n");

    // Test 1: Try to insert a duplicate insight
    const testInsight = {
      location_id: 1,
      title: "Test Duplicate Insight",
      description: "This is a test insight to check duplicate prevention",
      impact: "medium",
      type: "automation",
      status: "new",
      actions: JSON.stringify(["Test action"]),
      delivery_channel: "email"
    };

    console.log("1. Inserting first test insight...");
    try {
      const result1 = await db('insights').insert(testInsight);
      console.log("   ✓ First insight inserted successfully with ID:", result1[0]);
    } catch (error) {
      console.log("   ✗ Error inserting first insight:", error.message);
    }

    console.log("\n2. Attempting to insert duplicate insight...");
    try {
      const result2 = await db('insights').insert(testInsight);
      console.log("   ✗ Duplicate insight was inserted (this should not happen):", result2[0]);
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        console.log("   ✓ Duplicate prevented successfully! Error:", error.message);
      } else {
        console.log("   ✗ Unexpected error:", error.message);
      }
    }

    // Test 2: Check current duplicates
    console.log("\n3. Checking for existing duplicates...");
    const duplicates = await db.raw(`
      SELECT location_id, title, COUNT(*) as count
      FROM insights 
      WHERE status IN ('new', 'viewed')
      GROUP BY location_id, title 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `);

    const duplicateResults = duplicates[0] || [];
    if (duplicateResults.length > 0) {
      console.log("   Found duplicates:");
      duplicateResults.forEach(dup => {
        console.log(`   - Location ${dup.location_id}: "${dup.title}" (${dup.count} copies)`);
      });
    } else {
      console.log("   ✓ No duplicates found!");
    }

    // Test 3: Clean up test data
    console.log("\n4. Cleaning up test data...");
    const deleted = await db('insights')
      .where('title', 'Test Duplicate Insight')
      .del();
    console.log(`   ✓ Cleaned up ${deleted} test insights`);

    console.log("\n✓ Duplicate prevention test completed successfully!");

  } catch (error) {
    console.error("Error during test:", error);
  } finally {
    await db.destroy();
  }
}

// Run the test
if (require.main === module) {
  testDuplicatePrevention();
}

module.exports = { testDuplicatePrevention };
