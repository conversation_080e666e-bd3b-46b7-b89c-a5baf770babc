/**
 * Test script to verify automation plan generation fixes
 * Run with: node apps/platform/src/tools/test-automation-fix.js
 */

const { VotingAgent } = require('../insights/agents/VotingAgent');
const { ValidationAgent } = require('../insights/agents/ValidationAgent');

async function testFallbackPlan() {
  console.log('Testing fallback plan generation...');
  
  const votingAgent = new VotingAgent();
  
  // Mock insight data
  const mockInsight = {
    id: 15,
    title: 'Test Marketing Campaign',
    description: 'Test campaign for customer engagement',
    delivery_channel: 'email',
    type: 'campaign',
    location_id: 1
  };
  
  // Mock context data
  const mockContext = {
    location: {
      id: 1,
      name: 'Test Location',
      email: '<EMAIL>'
    },
    subscriptions: {
      email: { id: 1 },
      text: { id: 2 }
    },
    providers: {
      email: { id: 1 },
      text: { id: 2 }
    },
    existingResources: {
      tags: [],
      lists: [],
      campaigns: [],
      templates: [],
      journeys: []
    },
    channelConfig: {
      email: { enabled: true },
      text: { enabled: true }
    }
  };
  
  try {
    // Test the fallback plan generation directly
    const fallbackPlan = votingAgent.generateFallbackPlan(mockInsight, mockContext);
    console.log('✅ Fallback plan generated successfully');
    console.log('Plan structure:', Object.keys(fallbackPlan));
    console.log('Items:', Object.keys(fallbackPlan.items));
    
    // Test validation of the fallback plan
    const validationAgent = new ValidationAgent();
    const validated = await validationAgent.validateAndRepair(
      JSON.stringify(fallbackPlan),
      require('../insights/schemas').AutomationPlanSchema,
      mockContext
    );
    
    console.log('✅ Fallback plan validated successfully');
    console.log('Validated plan has', Object.keys(validated.items).length, 'items');
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Error details:', error);
    return false;
  }
}

async function testMinimalPlanStructure() {
  console.log('\nTesting minimal plan structure creation...');
  
  const validationAgent = new ValidationAgent();
  
  // Test with empty plan
  const emptyPlan = {
    name: '',
    description: '',
    items: {}
  };
  
  try {
    const fixed = validationAgent.fixSchemaViolations(emptyPlan, null);
    console.log('✅ Minimal plan structure created');
    console.log('Fixed plan has', Object.keys(fixed.items).length, 'items');
    console.log('Plan name:', fixed.name);
    console.log('Plan description:', fixed.description);
    
    return true;
  } catch (error) {
    console.error('❌ Minimal plan test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Running automation plan generation fix tests...\n');
  
  const results = [];
  
  results.push(await testFallbackPlan());
  results.push(await testMinimalPlanStructure());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! The automation plan generation fixes should work.');
  } else {
    console.log('⚠️  Some tests failed. Please check the error messages above.');
  }
  
  process.exit(passed === total ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
