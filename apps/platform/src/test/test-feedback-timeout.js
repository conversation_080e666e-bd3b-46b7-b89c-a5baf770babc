#!/usr/bin/env node

/**
 * Test script to verify the feedback endpoint timeout fixes
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const LOCATION_ID = 1;
const INSIGHT_ID = 10;

async function testFeedbackEndpoint() {
  console.log('🧪 Testing feedback endpoint timeout fixes...\n');

  const feedbackData = {
    success: true,
    steps: [
      {
        step: 'test-step',
        success: true,
        message: 'Test step completed successfully'
      }
    ]
  };

  try {
    console.log(`📤 Sending feedback to: ${API_BASE_URL}/admin/locations/${LOCATION_ID}/dashboard/insights/${INSIGHT_ID}/feedback`);
    console.log(`📊 Feedback data:`, JSON.stringify(feedbackData, null, 2));
    console.log(`⏰ Starting at: ${new Date().toISOString()}\n`);

    const startTime = Date.now();
    
    const response = await axios.post(
      `${API_BASE_URL}/admin/locations/${LOCATION_ID}/dashboard/insights/${INSIGHT_ID}/feedback`,
      feedbackData,
      {
        timeout: 180000, // 3 minutes timeout
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // You may need to adjust this
        }
      }
    );

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ Success! Response received in ${duration}ms`);
    console.log(`📥 Response status: ${response.status}`);
    console.log(`📥 Response data:`, JSON.stringify(response.data, null, 2));
    console.log(`⏰ Completed at: ${new Date().toISOString()}`);

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    console.error(`❌ Error after ${duration}ms:`);
    
    if (error.response) {
      console.error(`📥 Status: ${error.response.status}`);
      console.error(`📥 Response:`, JSON.stringify(error.response.data, null, 2));
      
      if (error.response.status === 504) {
        console.error(`\n🔍 Gateway Timeout detected. This indicates:`);
        console.error(`   - The request took longer than the configured timeout`);
        console.error(`   - The server may still be processing the request in the background`);
        console.error(`   - Our timeout fixes may need further adjustment`);
      }
    } else if (error.code === 'ECONNABORTED') {
      console.error(`🔍 Request timeout detected (client-side timeout)`);
    } else {
      console.error(`🔍 Network or other error:`, error.message);
    }
    
    console.error(`⏰ Failed at: ${new Date().toISOString()}`);
  }
}

async function testHealthCheck() {
  console.log('\n🏥 Testing API health check...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, {
      timeout: 5000
    });
    
    console.log(`✅ API is healthy - Status: ${response.status}`);
    return true;
  } catch (error) {
    console.error(`❌ API health check failed:`, error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Gateway Timeout Fix Verification\n');
  
  // First check if API is healthy
  const isHealthy = await testHealthCheck();
  
  if (!isHealthy) {
    console.log('\n⚠️  API is not responding. Please check if the containers are running.');
    console.log('   Run: docker ps');
    console.log('   Run: docker logs ai-marketing-assistant-monorepo-api-1');
    process.exit(1);
  }
  
  // Test the feedback endpoint
  await testFeedbackEndpoint();
  
  console.log('\n🎯 Test completed!');
  console.log('\n📋 Summary of fixes applied:');
  console.log('   ✓ Increased backend timeout from 30s to 120s');
  console.log('   ✓ Increased proxy timeout from 60s to 180s');
  console.log('   ✓ Increased nginx timeout from 300s to 600s');
  console.log('   ✓ Increased server timeout from 60s to 180s');
  console.log('   ✓ Added retry logic to frontend API calls');
  console.log('   ✓ Improved nodemon configuration to prevent restarts');
  console.log('   ✓ Enhanced error logging and debugging');
}

if (require.main === module) {
  main().catch(console.error);
}
