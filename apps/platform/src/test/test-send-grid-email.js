// Test SendGrid email sending directly
require('dotenv').config();

async function testSendGridEmail() {
  console.log('📧 Testing SendGrid Email Sending');
  console.log('=================================\n');
  
  try {
    // Import SendGrid
    const sgMail = require('@sendgrid/mail');
    
    // Set API key
    const apiKey = process.env.SENDGRID_API_KEY;
    if (!apiKey) {
      console.error('❌ SENDGRID_API_KEY not found in environment variables');
      return;
    }
    
    console.log('✅ SendGrid API key found');
    sgMail.setApiKey(apiKey);
    
    // Test email configuration
    const testEmail = {
      to: '<EMAIL>', // Your test email
      from: {
        email: '<EMAIL>', // Try with demo email that might be verified
        name: 'BakedBot Team'
      },
      subject: 'Test Email - Campaign Issue Investigation',
      html: `
        <html>
          <body>
            <h2>🔧 Campaign Issue Test Email</h2>
            <p>This is a test email to verify SendGrid configuration is working.</p>
            <p><strong>Campaign Investigation Results:</strong></p>
            <ul>
              <li>✅ SendGrid API key is configured</li>
              <li>✅ Templates are fixed</li>
              <li>✅ Provider is set to SendGrid</li>
              <li>✅ 4 pending sends are ready</li>
              <li>❓ Testing direct email sending...</li>
            </ul>
            <p>If you receive this email, SendGrid is working correctly!</p>
            <p>Best regards,<br>Campaign Investigation Script</p>
          </body>
        </html>
      `,
      text: `Campaign Issue Test Email

This is a test email to verify SendGrid configuration is working.

Campaign Investigation Results:
- ✅ SendGrid API key is configured
- ✅ Templates are fixed  
- ✅ Provider is set to SendGrid
- ✅ 4 pending sends are ready
- ❓ Testing direct email sending...

If you receive this email, SendGrid is working correctly!

Best regards,
Campaign Investigation Script`
    };
    
    console.log('📤 Sending test email...');
    console.log(`   To: ${testEmail.to}`);
    console.log(`   From: ${testEmail.from.email}`);
    console.log(`   Subject: ${testEmail.subject}`);
    
    const response = await sgMail.send(testEmail);
    
    console.log('✅ Email sent successfully!');
    console.log('📊 SendGrid Response:', response[0].statusCode);
    
    if (response[0].statusCode === 202) {
      console.log('🎉 SendGrid accepted the email for delivery');
      console.log('📧 Check your inbox: <EMAIL>');
    }
    
    console.log('\n🔧 Since direct SendGrid works, the issue is likely:');
    console.log('1. Email job queue processing is not working');
    console.log('2. Email job workers are not running');
    console.log('3. Job queue system needs to be restarted');
    
    console.log('\n💡 Next steps:');
    console.log('1. Check if Bull/Redis job queue is working');
    console.log('2. Restart the worker processes');
    console.log('3. Manually process the pending campaign sends');
    
  } catch (error) {
    console.error('💥 Error sending test email:', error.message);
    
    if (error.response) {
      console.error('📊 SendGrid Error Response:', error.response.body);
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Verify SendGrid API key is valid');
    console.log('2. Check if sender email is verified in SendGrid');
    console.log('3. Check SendGrid account status');
  }
}

// Run the test
testSendGridEmail();
