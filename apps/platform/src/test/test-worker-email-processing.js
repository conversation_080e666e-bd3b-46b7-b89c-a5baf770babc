// Test if the worker can now process email jobs automatically
require('dotenv').config();
const mysql = require('mysql2/promise');

// Database connection
const dbConfig = {
  host: 'mysql',
  port: 3306,
  user: 'root',
  password: 'bakedbotpassword',
  database: 'bakedbot'
};

async function testWorkerEmailProcessing() {
  console.log('🧪 Testing Worker Email Processing');
  console.log('==================================\n');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    
    console.log('📋 Step 1: Creating a test campaign...');
    
    // Create a new test campaign
    const [campaignResult] = await connection.execute(`
      INSERT INTO campaigns (
        name, state, type, channel, location_id, provider_id, subscription_id,
        delivery, created_at, updated_at
      ) VALUES (
        'Worker Test Campaign',
        'active',
        'email',
        'email',
        1,
        1,
        1,
        '{"sent": 0, "delivered": 0, "opened": 0, "clicked": 0, "bounced": 0, "unsubscribed": 0}',
        NOW(),
        NOW()
      )
    `);
    
    const campaignId = campaignResult.insertId;
    console.log(`✅ Created campaign ${campaignId}`);
    
    console.log('\n📋 Step 2: Creating email template...');
    
    // Create template for the campaign
    const templateData = {
      subject: `Worker Test - Automated Email Processing`,
      html: `
        <html>
          <body>
            <h2>🤖 Worker Test - Automated Email Processing</h2>
            <p>Dear Valued Customer,</p>
            <p>This email was sent automatically by the worker service to test the long-term fix!</p>
            <p><strong>✅ TypeScript compilation errors: FIXED</strong></p>
            <p><strong>✅ Worker service: RUNNING</strong></p>
            <p><strong>✅ Email job processing: AUTOMATED</strong></p>
            <p>If you received this email, it means the worker is successfully processing individual email jobs!</p>
            <p>Best regards,<br>The BakedBot Worker Service</p>
          </body>
        </html>
      `,
      text: `Worker Test - Automated Email Processing\n\nDear Valued Customer,\n\nThis email was sent automatically by the worker service to test the long-term fix!\n\n✅ TypeScript compilation errors: FIXED\n✅ Worker service: RUNNING\n✅ Email job processing: AUTOMATED\n\nIf you received this email, it means the worker is successfully processing individual email jobs!\n\nBest regards,\nThe BakedBot Worker Service`,
      from: {
        email: "<EMAIL>",
        name: "BakedBot Worker Service"
      }
    };
    
    await connection.execute(`
      INSERT INTO templates (campaign_id, location_id, data, created_at, updated_at)
      VALUES (?, ?, ?, NOW(), NOW())
    `, [campaignId, 1, JSON.stringify(templateData)]);
    
    console.log('✅ Template created');
    
    console.log('\n📋 Step 3: Creating campaign sends...');
    
    // Create campaign sends for test recipients
    const testRecipients = [
      { user_id: 1, email: '<EMAIL>' },
      { user_id: 2, email: '<EMAIL>' },
      { user_id: 3, email: '<EMAIL>' }
    ];
    
    for (const recipient of testRecipients) {
      await connection.execute(`
        INSERT INTO campaign_sends (
          campaign_id, user_id, state, send_at, created_at, updated_at
        ) VALUES (?, ?, 'pending', NOW(), NOW(), NOW())
      `, [campaignId, recipient.user_id]);
      
      console.log(`✅ Created send for ${recipient.email}`);
    }
    
    console.log('\n📋 Step 4: Waiting for worker to process emails...');
    console.log('⏳ The worker should automatically pick up and process these email jobs');
    console.log('⏳ Checking status every 10 seconds for 2 minutes...');
    
    // Monitor the campaign sends for 2 minutes
    let attempts = 0;
    const maxAttempts = 12; // 2 minutes with 10-second intervals
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      attempts++;
      
      console.log(`\n📊 Check ${attempts}/${maxAttempts} - Checking campaign status...`);
      
      // Check campaign send status
      const [sendStatus] = await connection.execute(`
        SELECT 
          state,
          COUNT(*) as count
        FROM campaign_sends 
        WHERE campaign_id = ?
        GROUP BY state
      `, [campaignId]);
      
      console.log('📧 Send Status:');
      sendStatus.forEach(status => {
        console.log(`   ${status.state}: ${status.count} emails`);
      });
      
      // Check if all emails have been sent
      const [pendingCount] = await connection.execute(`
        SELECT COUNT(*) as pending 
        FROM campaign_sends 
        WHERE campaign_id = ? AND state = 'pending'
      `, [campaignId]);
      
      if (pendingCount[0].pending === 0) {
        console.log('\n🎉 SUCCESS! All emails have been processed by the worker!');
        
        // Get final status
        const [finalStatus] = await connection.execute(`
          SELECT 
            state,
            COUNT(*) as count
          FROM campaign_sends 
          WHERE campaign_id = ?
          GROUP BY state
        `, [campaignId]);
        
        console.log('\n📊 Final Results:');
        finalStatus.forEach(status => {
          console.log(`   ✅ ${status.state}: ${status.count} emails`);
        });
        
        console.log('\n🎯 LONG-TERM FIX CONFIRMED:');
        console.log('✅ Worker service is running');
        console.log('✅ TypeScript errors are resolved');
        console.log('✅ Email jobs are being processed automatically');
        console.log('✅ No manual intervention required');
        
        break;
      }
      
      if (attempts === maxAttempts) {
        console.log('\n⚠️  Timeout reached. Final status:');
        sendStatus.forEach(status => {
          console.log(`   ${status.state}: ${status.count} emails`);
        });
        
        if (pendingCount[0].pending > 0) {
          console.log('\n❌ Some emails are still pending. Worker may need more investigation.');
        }
      }
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

// Run the test
testWorkerEmailProcessing();
