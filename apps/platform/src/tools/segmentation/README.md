# Schema-Aware Customer Segmentation Implementation

## Overview

This implementation transforms the basic customer segmentation in Smokey Growth Engine insights into a sophisticated, schema-aware system that leverages AI-driven rule generation with deep understanding of database relationships and constraints.

## ✅ Implementation Status

### Phase 1: Data Schema Representation for AI Context ✅
- **SchemaAwareSegmentationService.ts**: Core service with comprehensive schema building
- **DataSchema interfaces**: Complete type definitions for tables, fields, and relationships
- **AI Context Generation**: Schema representation optimized for LLM consumption
- **Relationship Mapping**: Full understanding of table joins and foreign keys

### Phase 2: Helper Functions for Rule Validation ✅
- **RuleValidationService.ts**: Comprehensive rule validation against schema
- **Type Compatibility**: Validates rule types against field types
- **Constraint Validation**: Enforces field constraints (min/max, enums, patterns)
- **Operator Compatibility**: Ensures operators work with field types
- **Rule Enhancement**: Adds metadata and suggestions for improvement

### Phase 3: Relationship-Aware Rule Generation ✅
- **RelationshipAwareRuleGenerator.ts**: Generates rules that understand table joins
- **Cross-Table Rules**: Creates complex rules spanning multiple tables
- **SQL Generation**: Converts rule trees to optimized SQL queries
- **Performance Estimation**: Analyzes rule complexity and performance impact
- **Join Optimization**: Intelligent join pattern selection

## Key Features Implemented

### 1. Data Schema Representation for AI Context

```typescript
// Comprehensive schema with relationships
const schema = {
  tables: {
    pos_data: {
      fields: [...],
      relationships: {
        product: { table: "products", localField: "product_name", ... },
        location: { table: "locations", localField: "location_id", ... }
      }
    }
  }
}

// AI-optimized schema description
const aiContext = service.getSchemaForAI();
// Returns detailed schema with join patterns and segmentation fields
```

### 2. Helper Functions for Rule Validation

```typescript
// Validate rules against schema
const validation = ruleValidationService.validateRule(rule);
// Returns: { isValid, errors, warnings, suggestions, enhancedRule }

// Type compatibility checking
validateTypeCompatibility(rule, field, result);

// Constraint validation
validateValueConstraints(rule, field, result);

// Enhanced rules with metadata
const enhanced = enhanceRule(rule);
```

### 3. Relationship-Aware Rule Generation

```typescript
// Generate cross-table rules
const rules = generator.generateSegmentationRules(context);

// Each rule includes join information
{
  rule: RuleTree,
  requiredJoins: JoinSpecification[],
  affectedTables: string[],
  complexity: "simple" | "moderate" | "complex",
  estimatedPerformance: "fast" | "medium" | "slow"
}

// SQL generation with joins
const sql = generator.generateSQLQuery(rule, locationId);
```

## Usage Examples

### Basic Schema-Aware Segmentation

```typescript
const service = new SchemaAwareSegmentationService(dataHubTool);
await service.initialize();

const result = await service.runSchemaAwareSegmentation({
  location_id: 1,
  start_date: "2024-01-01",
  end_date: "2024-01-31",
  segmentation_strategy: "hybrid"
});

// Returns enhanced segments with schema-aware rules
console.log(result.segments);
console.log(result.metadata.dataQuality);
console.log(result.metadata.generatedRules);
```

### Custom Rule Validation

```typescript
const validationService = new RuleValidationService(schema);

const rule: RuleTree = {
  uuid: "rule-1",
  type: "number",
  group: "pos",
  path: "pos_data.gross_sales",
  operator: ">",
  value: 500
};

const validation = validationService.validateRule(rule);
if (!validation.isValid) {
  console.log("Errors:", validation.errors);
  console.log("Suggestions:", validation.suggestions);
}
```

### Relationship-Aware Rule Generation

```typescript
const generator = new RelationshipAwareRuleGenerator(schema);

const context = {
  schema,
  availableFields: ["pos_data.customer_name", "products.latest_price"],
  sampleData: [...],
  businessRules: ["High-value customers spend more than $500"],
  segmentationGoals: ["Identify premium customers"]
};

const rules = generator.generateSegmentationRules(context);

// Generate SQL for execution
rules.forEach(rule => {
  const sql = generator.generateSQLQuery(rule, locationId);
  console.log("Generated SQL:", sql);
  
  const performance = generator.estimateRulePerformance(rule);
  console.log("Performance estimate:", performance);
});
```

## Integration with AnalysisTool

The enhanced customer segmentation is now integrated into the existing AnalysisTool:

```typescript
// Automatic schema-aware segmentation
const result = await analysisTool.execute({
  analysis_type: "customer_segmentation",
  location_id: 1,
  start_date: "2024-01-01",
  end_date: "2024-01-31",
  additional_params: {
    segmentation_strategy: "hybrid",
    use_schema_aware: true
  }
});

// Falls back to legacy segmentation if schema-aware fails
```

## Benefits Achieved

### 1. ✅ Data Schema Representation for AI Context
- Complete database schema understanding
- AI-optimized schema descriptions
- Relationship mapping for intelligent rule generation
- Field constraint awareness

### 2. ✅ Helper Functions for Rule Validation
- Type safety validation
- Constraint enforcement
- Operator compatibility checking
- Rule enhancement with metadata
- Performance suggestions

### 3. ✅ Relationship-Aware Rule Generation
- Cross-table rule generation
- Intelligent join pattern selection
- SQL query optimization
- Performance impact analysis
- Complex behavioral pattern detection

## Advanced Features

### Multi-Table Segmentation Rules
```typescript
// Example: Premium customers who prefer high-THC products
{
  rule: {
    type: "wrapper",
    operator: "and",
    children: [
      { path: "pos_data.gross_sales", operator: ">", value: 500 },
      { path: "products.percentage_thc", operator: ">", value: 20 },
      { path: "products.category", operator: "=", value: "Flower" }
    ]
  },
  requiredJoins: [
    {
      leftTable: "pos_data",
      rightTable: "products", 
      leftField: "product_name",
      rightField: "product_name",
      joinType: "INNER"
    }
  ]
}
```

### Data Quality Assessment
```typescript
// Automatic data quality calculation
const dataQuality = await service.calculateDataQuality(locationId, startDate, endDate);
// Returns weighted score based on field completeness
```

### Performance Optimization
```typescript
// Rule performance estimation
const performance = generator.estimateRulePerformance(rule);
// Returns: { estimatedRows, indexUsage, recommendations }
```

## Testing

Comprehensive test suite covers:
- Schema building and validation
- Rule generation and validation
- Cross-table relationship handling
- Performance estimation
- Integration workflows

Run tests:
```bash
npm test -- --testPathPattern=SchemaAwareSegmentation
```

## Future Enhancements

1. **Machine Learning Integration**: Use ML models for pattern detection
2. **Real-time Rule Optimization**: Dynamic rule adjustment based on performance
3. **Advanced Join Strategies**: More sophisticated join optimization
4. **Custom Schema Extensions**: Support for custom field definitions
5. **Rule Caching**: Cache validated and optimized rules for performance

## Conclusion

This implementation successfully transforms basic customer segmentation into a sophisticated, schema-aware system that:

- ✅ Builds comprehensive data schema representations for AI context
- ✅ Provides helper functions to validate and enhance rules based on schema
- ✅ Implements relationship-aware rule generation that understands joins between tables

The system now provides intelligent, data-driven customer segmentation with full awareness of database structure, relationships, and constraints, enabling more accurate and performant segmentation strategies.
