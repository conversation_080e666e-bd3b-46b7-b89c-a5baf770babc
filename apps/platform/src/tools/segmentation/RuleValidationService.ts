import { RuleTree, RuleType, RuleGroup, Operator } from "../../rules/Rule";
import { DataSchema, DataSchemaField, DataSchemaTable } from "./SchemaAwareSegmentationService";
import { logger } from "../../config/logger";
import { z } from "zod";

/**
 * Rule validation result
 */
export interface RuleValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  enhancedRule?: RuleTree;
}

/**
 * Field validation context
 */
export interface FieldValidationContext {
  field: DataSchemaField;
  table: DataSchemaTable;
  value: any;
  operator: Operator;
}

/**
 * Service for validating and enhancing rules based on schema
 */
export class RuleValidationService {
  private schema: DataSchema;

  constructor(schema: DataSchema) {
    this.schema = schema;
  }

  /**
   * Validate a rule tree against the schema
   */
  validateRule(rule: RuleTree): RuleValidationResult {
    const result: RuleValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };

    try {
      this.validateRuleRecursive(rule, result);
      
      // If there are errors, mark as invalid
      if (result.errors.length > 0) {
        result.isValid = false;
      }

      // Try to enhance the rule if it's valid
      if (result.isValid) {
        result.enhancedRule = this.enhanceRule(rule);
      }

      logger.info("Rule validation completed", {
        isValid: result.isValid,
        errorsCount: result.errors.length,
        warningsCount: result.warnings.length,
        suggestionsCount: result.suggestions.length,
      });

    } catch (error) {
      result.isValid = false;
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      logger.error("Rule validation failed", { error, rule });
    }

    return result;
  }

  /**
   * Recursively validate rule tree
   */
  private validateRuleRecursive(rule: RuleTree, result: RuleValidationResult): void {
    // Validate rule structure
    this.validateRuleStructure(rule, result);

    // Validate field and value compatibility
    if (rule.group === "pos" || rule.group === "user") {
      this.validateFieldAndValue(rule, result);
    }

    // Validate operator compatibility
    this.validateOperatorCompatibility(rule, result);

    // Recursively validate children
    if (rule.children && rule.children.length > 0) {
      rule.children.forEach(child => this.validateRuleRecursive(child, result));
    }
  }

  /**
   * Validate basic rule structure
   */
  private validateRuleStructure(rule: RuleTree, result: RuleValidationResult): void {
    // Check required fields
    if (!rule.uuid) {
      result.errors.push("Rule must have a UUID");
    }

    if (!rule.type) {
      result.errors.push("Rule must have a type");
    }

    if (!rule.group) {
      result.errors.push("Rule must have a group");
    }

    if (!rule.operator) {
      result.errors.push("Rule must have an operator");
    }

    // Validate wrapper rules
    if (rule.type === "wrapper") {
      if (!["and", "or", "xor", "none"].includes(rule.operator)) {
        result.errors.push(`Wrapper rule must use logical operator, got: ${rule.operator}`);
      }
      
      if (!rule.children || rule.children.length === 0) {
        result.errors.push("Wrapper rule must have children");
      }
    }

    // Validate non-wrapper rules
    if (rule.type !== "wrapper" && !rule.path) {
      result.errors.push("Non-wrapper rule must have a path");
    }
  }

  /**
   * Validate field and value compatibility with schema
   */
  private validateFieldAndValue(rule: RuleTree, result: RuleValidationResult): void {
    if (!rule.path || rule.type === "wrapper") return;

    // Parse the path to get table and field
    const pathInfo = this.parsePath(rule.path);
    if (!pathInfo) {
      result.errors.push(`Invalid path format: ${rule.path}`);
      return;
    }

    const { tableName, fieldName } = pathInfo;

    // Check if table exists in schema
    const table = this.schema.tables[tableName];
    if (!table) {
      result.errors.push(`Table '${tableName}' not found in schema`);
      return;
    }

    // Check if field exists in table
    const field = table.fields.find(f => f.name === fieldName);
    if (!field) {
      result.errors.push(`Field '${fieldName}' not found in table '${tableName}'`);
      return;
    }

    // Validate type compatibility
    this.validateTypeCompatibility(rule, field, result);

    // Validate value constraints
    this.validateValueConstraints(rule, field, result);

    // Validate operator compatibility with field type
    this.validateOperatorFieldCompatibility(rule, field, result);
  }

  /**
   * Parse a rule path to extract table and field information
   */
  private parsePath(path: string): { tableName: string; fieldName: string } | null {
    // Handle different path formats
    // Examples: "$.customer_name", "pos_data.customer_name", "customer_name"
    
    if (path.startsWith("$.")) {
      // JSONPath format - assume pos_data table for now
      return {
        tableName: "pos_data",
        fieldName: path.substring(2),
      };
    }

    if (path.includes(".")) {
      const [tableName, fieldName] = path.split(".", 2);
      return { tableName, fieldName };
    }

    // Simple field name - assume pos_data table
    return {
      tableName: "pos_data",
      fieldName: path,
    };
  }

  /**
   * Validate type compatibility between rule and field
   */
  private validateTypeCompatibility(rule: RuleTree, field: DataSchemaField, result: RuleValidationResult): void {
    const ruleTypeToFieldType: Record<RuleType, string[]> = {
      string: ["string"],
      number: ["number"],
      boolean: ["boolean"],
      date: ["date"],
      array: ["array"],
      wrapper: [], // Not applicable
    };

    const compatibleTypes = ruleTypeToFieldType[rule.type];
    if (compatibleTypes && !compatibleTypes.includes(field.type)) {
      result.errors.push(
        `Type mismatch: rule type '${rule.type}' not compatible with field type '${field.type}' for field '${field.name}'`
      );
    }
  }

  /**
   * Validate value against field constraints
   */
  private validateValueConstraints(rule: RuleTree, field: DataSchemaField, result: RuleValidationResult): void {
    if (rule.value === null || rule.value === undefined) {
      if (!field.nullable) {
        result.warnings.push(`Field '${field.name}' is not nullable but rule value is null/undefined`);
      }
      return;
    }

    const constraints = field.constraints;
    if (!constraints) return;

    // Validate numeric constraints
    if (field.type === "number" && typeof rule.value === "number") {
      if (constraints.min !== undefined && rule.value < constraints.min) {
        result.errors.push(`Value ${rule.value} is below minimum ${constraints.min} for field '${field.name}'`);
      }
      if (constraints.max !== undefined && rule.value > constraints.max) {
        result.errors.push(`Value ${rule.value} is above maximum ${constraints.max} for field '${field.name}'`);
      }
    }

    // Validate enum constraints
    if (constraints.enum && !constraints.enum.includes(String(rule.value))) {
      result.errors.push(
        `Value '${rule.value}' is not in allowed values [${constraints.enum.join(", ")}] for field '${field.name}'`
      );
    }

    // Validate pattern constraints
    if (constraints.pattern && typeof rule.value === "string") {
      const regex = new RegExp(constraints.pattern);
      if (!regex.test(rule.value)) {
        result.errors.push(`Value '${rule.value}' does not match pattern '${constraints.pattern}' for field '${field.name}'`);
      }
    }
  }

  /**
   * Validate operator compatibility
   */
  private validateOperatorCompatibility(rule: RuleTree, result: RuleValidationResult): void {
    const validOperators: Operator[] = [
      "=", "!=", "<", "<=", ">", ">=",
      "is set", "is not set", "or", "and", "xor",
      "empty", "contains", "starts with", "ends with",
      "any", "none"
    ];

    if (!validOperators.includes(rule.operator)) {
      result.errors.push(`Invalid operator: ${rule.operator}`);
    }
  }

  /**
   * Validate operator compatibility with field type
   */
  private validateOperatorFieldCompatibility(rule: RuleTree, field: DataSchemaField, result: RuleValidationResult): void {
    const stringOperators = ["=", "!=", "contains", "starts with", "ends with", "is set", "is not set", "empty"];
    const numericOperators = ["=", "!=", "<", "<=", ">", ">=", "is set", "is not set"];
    const dateOperators = ["=", "!=", "<", "<=", ">", ">=", "is set", "is not set"];
    const booleanOperators = ["=", "!=", "is set", "is not set"];
    const arrayOperators = ["any", "none", "contains", "is set", "is not set", "empty"];

    let validOperators: string[] = [];

    switch (field.type) {
      case "string":
        validOperators = stringOperators;
        break;
      case "number":
        validOperators = numericOperators;
        break;
      case "date":
        validOperators = dateOperators;
        break;
      case "boolean":
        validOperators = booleanOperators;
        break;
      case "array":
        validOperators = arrayOperators;
        break;
    }

    if (!validOperators.includes(rule.operator)) {
      result.warnings.push(
        `Operator '${rule.operator}' may not be compatible with field type '${field.type}' for field '${field.name}'`
      );
      result.suggestions.push(
        `Consider using one of these operators for ${field.type} fields: ${validOperators.join(", ")}`
      );
    }
  }

  /**
   * Enhance a rule with schema-based improvements
   */
  private enhanceRule(rule: RuleTree): RuleTree {
    const enhanced = { ...rule };

    // Add schema-based enhancements
    if (rule.children) {
      enhanced.children = rule.children.map(child => this.enhanceRule(child));
    }

    // Add metadata about the field if available
    if (rule.path && rule.group === "pos") {
      const pathInfo = this.parsePath(rule.path);
      if (pathInfo) {
        const table = this.schema.tables[pathInfo.tableName];
        const field = table?.fields.find(f => f.name === pathInfo.fieldName);
        if (field) {
          // Add field metadata to the rule for better processing
          (enhanced as any).fieldMetadata = {
            description: field.description,
            type: field.type,
            nullable: field.nullable,
            constraints: field.constraints,
          };
        }
      }
    }

    return enhanced;
  }

  /**
   * Suggest rule improvements based on schema
   */
  suggestRuleImprovements(rule: RuleTree): string[] {
    const suggestions: string[] = [];

    // Analyze the rule and provide suggestions
    if (rule.type === "wrapper" && rule.children && rule.children.length === 1) {
      suggestions.push("Consider simplifying single-child wrapper rules");
    }

    // Add more sophisticated suggestions based on schema analysis
    if (rule.path && rule.group === "pos") {
      const pathInfo = this.parsePath(rule.path);
      if (pathInfo) {
        const table = this.schema.tables[pathInfo.tableName];
        const field = table?.fields.find(f => f.name === pathInfo.fieldName);
        
        if (field?.relationships && field.relationships.length > 0) {
          suggestions.push(
            `Field '${field.name}' has relationships with ${field.relationships.map(r => r.table).join(", ")}. Consider adding related field conditions.`
          );
        }
      }
    }

    return suggestions;
  }
}
