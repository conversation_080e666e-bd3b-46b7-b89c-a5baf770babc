import { RuleTree, RuleType, RuleGroup, Operator } from "../../rules/Rule";
import { DataSchema, DataSchemaTable, RuleGenerationContext } from "./SchemaAwareSegmentationService";
import { uuid } from "../../utilities";
import { logger } from "../../config/logger";

/**
 * Join specification for cross-table rules
 */
export interface JoinSpecification {
  leftTable: string;
  rightTable: string;
  leftField: string;
  rightField: string;
  joinType: "INNER" | "LEFT" | "RIGHT" | "FULL";
}

/**
 * Complex rule pattern that spans multiple tables
 */
export interface CrossTableRulePattern {
  name: string;
  description: string;
  tables: string[];
  joins: JoinSpecification[];
  conditions: RuleTree[];
  businessLogic: string;
}

/**
 * Generated rule with join information
 */
export interface RelationshipAwareRule {
  rule: RuleTree;
  requiredJoins: JoinSpecification[];
  affectedTables: string[];
  complexity: "simple" | "moderate" | "complex";
  estimatedPerformance: "fast" | "medium" | "slow";
}

/**
 * Service for generating relationship-aware rules that understand table joins
 */
export class RelationshipAwareRuleGenerator {
  private schema: DataSchema;

  constructor(schema: DataSchema) {
    this.schema = schema;
  }

  /**
   * Generate customer segmentation rules based on business patterns
   */
  generateSegmentationRules(context: RuleGenerationContext): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    try {
      // Generate high-value customer rules
      rules.push(...this.generateHighValueCustomerRules());

      // Generate frequency-based rules
      rules.push(...this.generateFrequencyBasedRules());

      // Generate product preference rules
      rules.push(...this.generateProductPreferenceRules());

      // Generate demographic-based rules
      rules.push(...this.generateDemographicRules());

      // Generate cross-table behavioral rules
      rules.push(...this.generateCrossTableBehavioralRules());

      logger.info("Generated relationship-aware segmentation rules", {
        totalRules: rules.length,
        complexRules: rules.filter(r => r.complexity === "complex").length,
      });

    } catch (error) {
      logger.error("Error generating segmentation rules", { error });
    }

    return rules;
  }

  /**
   * Generate high-value customer identification rules
   */
  private generateHighValueCustomerRules(): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    // Rule 1: High total spend customers
    const highSpendRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.total_spend",
          operator: ">",
          value: 1000,
        },
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.transaction_count",
          operator: ">=",
          value: 5,
        },
      ],
    };

    rules.push({
      rule: highSpendRule,
      requiredJoins: [],
      affectedTables: ["pos_data"],
      complexity: "simple",
      estimatedPerformance: "fast",
    });

    // Rule 2: High-value customers with premium product preferences
    const premiumCustomerRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "pos_data.gross_sales",
          operator: ">",
          value: 500,
        },
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "products.latest_price",
          operator: ">",
          value: 50,
        },
      ],
    };

    rules.push({
      rule: premiumCustomerRule,
      requiredJoins: [
        {
          leftTable: "pos_data",
          rightTable: "products",
          leftField: "product_name",
          rightField: "product_name",
          joinType: "INNER",
        },
      ],
      affectedTables: ["pos_data", "products"],
      complexity: "moderate",
      estimatedPerformance: "medium",
    });

    return rules;
  }

  /**
   * Generate frequency-based customer rules
   */
  private generateFrequencyBasedRules(): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    // Frequent customers (visits per month)
    const frequentCustomerRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.visit_frequency_per_month",
          operator: ">=",
          value: 4,
        },
        {
          uuid: uuid(),
          type: "date",
          group: "pos",
          path: "$.last_visit_date",
          operator: ">=",
          value: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // Last 30 days
        },
      ],
    };

    rules.push({
      rule: frequentCustomerRule,
      requiredJoins: [],
      affectedTables: ["pos_data"],
      complexity: "simple",
      estimatedPerformance: "fast",
    });

    return rules;
  }

  /**
   * Generate product preference-based rules
   */
  private generateProductPreferenceRules(): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    // Cannabis connoisseur (high THC preference)
    const connoisseurRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "products.percentage_thc",
          operator: ">",
          value: 20,
        },
        {
          uuid: uuid(),
          type: "string",
          group: "pos",
          path: "products.category",
          operator: "=",
          value: "Flower",
        },
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.purchase_count",
          operator: ">=",
          value: 3,
        },
      ],
    };

    rules.push({
      rule: connoisseurRule,
      requiredJoins: [
        {
          leftTable: "pos_data",
          rightTable: "products",
          leftField: "product_name",
          rightField: "product_name",
          joinType: "INNER",
        },
      ],
      affectedTables: ["pos_data", "products"],
      complexity: "moderate",
      estimatedPerformance: "medium",
    });

    // Wellness-focused customers (CBD preference)
    const wellnessRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "products.percentage_cbd",
          operator: ">",
          value: 10,
        },
        {
          uuid: uuid(),
          type: "string",
          group: "pos",
          path: "pos_data.customer_type",
          operator: "=",
          value: "medical",
        },
      ],
    };

    rules.push({
      rule: wellnessRule,
      requiredJoins: [
        {
          leftTable: "pos_data",
          rightTable: "products",
          leftField: "product_name",
          rightField: "product_name",
          joinType: "INNER",
        },
      ],
      affectedTables: ["pos_data", "products"],
      complexity: "moderate",
      estimatedPerformance: "medium",
    });

    return rules;
  }

  /**
   * Generate demographic-based rules
   */
  private generateDemographicRules(): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    // Young adult segment (21-30 years old)
    const youngAdultRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.age",
          operator: ">=",
          value: 21,
        },
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.age",
          operator: "<=",
          value: 30,
        },
        {
          uuid: uuid(),
          type: "string",
          group: "pos",
          path: "pos_data.customer_type",
          operator: "=",
          value: "recreational",
        },
      ],
    };

    rules.push({
      rule: youngAdultRule,
      requiredJoins: [],
      affectedTables: ["pos_data"],
      complexity: "simple",
      estimatedPerformance: "fast",
    });

    return rules;
  }

  /**
   * Generate complex cross-table behavioral rules
   */
  private generateCrossTableBehavioralRules(): RelationshipAwareRule[] {
    const rules: RelationshipAwareRule[] = [];

    // Brand loyal customers with high engagement
    const brandLoyalRule: RuleTree = {
      uuid: uuid(),
      type: "wrapper",
      group: "pos",
      path: "$",
      operator: "and",
      children: [
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.brand_diversity_score",
          operator: "<=",
          value: 3, // Low brand diversity = high loyalty
        },
        {
          uuid: uuid(),
          type: "number",
          group: "pos",
          path: "$.total_spend",
          operator: ">",
          value: 300,
        },
        {
          uuid: uuid(),
          type: "string",
          group: "pos",
          path: "products.brand_name",
          operator: "is set",
          value: null,
        },
      ],
    };

    rules.push({
      rule: brandLoyalRule,
      requiredJoins: [
        {
          leftTable: "pos_data",
          rightTable: "products",
          leftField: "product_name",
          rightField: "product_name",
          joinType: "INNER",
        },
      ],
      affectedTables: ["pos_data", "products"],
      complexity: "complex",
      estimatedPerformance: "slow",
    });

    return rules;
  }

  /**
   * Generate SQL query for a relationship-aware rule
   */
  generateSQLQuery(rule: RelationshipAwareRule, locationId: number): string {
    let query = "SELECT DISTINCT pos_data.customer_name";
    let fromClause = " FROM pos_data";
    let whereClause = ` WHERE pos_data.location_id = ${locationId}`;

    // Add joins
    rule.requiredJoins.forEach(join => {
      fromClause += ` ${join.joinType} JOIN ${join.rightTable} ON ${join.leftTable}.${join.leftField} = ${join.rightTable}.${join.rightField}`;
    });

    // Convert rule tree to SQL conditions
    const conditions = this.ruleTreeToSQL(rule.rule);
    if (conditions) {
      whereClause += ` AND (${conditions})`;
    }

    return query + fromClause + whereClause;
  }

  /**
   * Convert rule tree to SQL WHERE conditions
   */
  private ruleTreeToSQL(rule: RuleTree): string {
    if (rule.type === "wrapper") {
      if (!rule.children || rule.children.length === 0) {
        return "";
      }

      const childConditions = rule.children
        .map(child => this.ruleTreeToSQL(child))
        .filter(condition => condition.length > 0);

      if (childConditions.length === 0) {
        return "";
      }

      const operator = rule.operator === "and" ? " AND " : " OR ";
      return `(${childConditions.join(operator)})`;
    }

    // Handle leaf nodes
    const field = rule.path?.replace("$.", "pos_data.") || rule.path;
    const value = typeof rule.value === "string" ? `'${rule.value}'` : rule.value;

    switch (rule.operator) {
      case "=":
        return `${field} = ${value}`;
      case "!=":
        return `${field} != ${value}`;
      case ">":
        return `${field} > ${value}`;
      case ">=":
        return `${field} >= ${value}`;
      case "<":
        return `${field} < ${value}`;
      case "<=":
        return `${field} <= ${value}`;
      case "contains":
        return `${field} LIKE '%${rule.value}%'`;
      case "starts with":
        return `${field} LIKE '${rule.value}%'`;
      case "ends with":
        return `${field} LIKE '%${rule.value}'`;
      case "is set":
        return `${field} IS NOT NULL`;
      case "is not set":
        return `${field} IS NULL`;
      case "empty":
        return `(${field} IS NULL OR ${field} = '')`;
      default:
        return "";
    }
  }

  /**
   * Estimate rule performance impact
   */
  estimateRulePerformance(rule: RelationshipAwareRule): {
    estimatedRows: number;
    indexUsage: string[];
    recommendations: string[];
  } {
    const recommendations: string[] = [];
    const indexUsage: string[] = [];

    // Analyze joins
    rule.requiredJoins.forEach(join => {
      indexUsage.push(`Index on ${join.leftTable}.${join.leftField}`);
      indexUsage.push(`Index on ${join.rightTable}.${join.rightField}`);
    });

    // Analyze rule complexity
    if (rule.complexity === "complex") {
      recommendations.push("Consider adding composite indexes for complex rules");
    }

    if (rule.requiredJoins.length > 2) {
      recommendations.push("Multiple joins detected - consider query optimization");
    }

    return {
      estimatedRows: this.estimateResultSize(rule),
      indexUsage,
      recommendations,
    };
  }

  /**
   * Estimate result set size for a rule
   */
  private estimateResultSize(rule: RelationshipAwareRule): number {
    // Simple heuristic based on rule complexity and joins
    let baseSize = 1000; // Assume 1000 customers base

    // Adjust based on complexity
    switch (rule.complexity) {
      case "simple":
        baseSize *= 0.3; // Simple rules typically match 30% of customers
        break;
      case "moderate":
        baseSize *= 0.15; // Moderate rules match 15%
        break;
      case "complex":
        baseSize *= 0.05; // Complex rules match 5%
        break;
    }

    // Adjust based on joins (more joins = more selective)
    baseSize *= Math.pow(0.8, rule.requiredJoins.length);

    return Math.round(baseSize);
  }
}
