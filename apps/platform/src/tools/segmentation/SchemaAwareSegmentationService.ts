import { logger } from "../../config/logger";
import { RuleTree, RuleType, RuleGroup, Operator } from "../../rules/Rule";
import { check } from "../../rules/RuleEngine";
import { z } from "zod";

/**
 * Data schema representation for AI context
 */
export interface DataSchemaField {
  name: string;
  type: "string" | "number" | "boolean" | "date" | "array" | "object";
  description: string;
  nullable: boolean;
  constraints?: {
    min?: number;
    max?: number;
    enum?: string[];
    pattern?: string;
  };
  relationships?: {
    table: string;
    field: string;
    type: "one-to-one" | "one-to-many" | "many-to-many";
  }[];
}

export interface DataSchemaTable {
  name: string;
  description: string;
  fields: DataSchemaField[];
  primaryKey: string;
  indexes: string[];
  relationships: {
    [key: string]: {
      table: string;
      localField: string;
      foreignField: string;
      type: "belongs_to" | "has_many" | "has_one";
    };
  };
}

export interface DataSchema {
  tables: { [tableName: string]: DataSchemaTable };
  version: string;
  lastUpdated: Date;
}

/**
 * Enhanced customer segment with schema-aware rules
 */
export interface SchemaAwareCustomerSegment {
  id: string;
  name: string;
  description: string;
  rules: RuleTree;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    aiGenerated: boolean;
    confidence: number;
    dataQuality: number;
  };
  customers?: any[];
  metrics?: {
    totalCustomers: number;
    totalSpend: number;
    avgTransactionValue: number;
    frequency: number;
  };
}

/**
 * Rule generation context for AI
 */
export interface RuleGenerationContext {
  schema: DataSchema;
  availableFields: string[];
  sampleData: Record<string, any>[];
  businessRules: string[];
  segmentationGoals: string[];
}

/**
 * Schema-aware customer segmentation service
 */
export class SchemaAwareSegmentationService {
  private schema: DataSchema | null = null;
  private dataHubTool: any;

  constructor(dataHubTool: any) {
    this.dataHubTool = dataHubTool;
  }

  /**
   * Initialize the service with current database schema
   */
  async initialize(): Promise<void> {
    try {
      this.schema = await this.buildDataSchema();
      logger.info("SchemaAwareSegmentationService initialized with schema", {
        tablesCount: Object.keys(this.schema.tables).length,
      });
    } catch (error) {
      logger.error("Failed to initialize SchemaAwareSegmentationService", { error });
      throw error;
    }
  }

  /**
   * Build comprehensive data schema representation
   */
  private async buildDataSchema(): Promise<DataSchema> {
    const schema: DataSchema = {
      tables: {},
      version: "1.0.0",
      lastUpdated: new Date(),
    };

    // Define core tables with their schema information
    schema.tables.pos_data = {
      name: "pos_data",
      description: "Point of sale transaction data with customer and product information",
      primaryKey: "id",
      indexes: ["location_id", "order_date", "customer_name"],
      fields: [
        {
          name: "id",
          type: "number",
          description: "Unique transaction identifier",
          nullable: false,
        },
        {
          name: "location_id",
          type: "number",
          description: "Location where transaction occurred",
          nullable: false,
          relationships: [
            {
              table: "locations",
              field: "id",
              type: "one-to-many",
            },
          ],
        },
        {
          name: "customer_name",
          type: "string",
          description: "Customer name for the transaction",
          nullable: true,
        },
        {
          name: "customer_type",
          type: "string",
          description: "Type of customer (recreational, medical)",
          nullable: true,
          constraints: {
            enum: ["recreational", "medical"],
          },
        },
        {
          name: "order_date",
          type: "date",
          description: "Date when the order was placed",
          nullable: false,
        },
        {
          name: "gross_sales",
          type: "number",
          description: "Total sales amount before discounts",
          nullable: false,
          constraints: { min: 0 },
        },
        {
          name: "net_sales",
          type: "number",
          description: "Final sales amount after discounts",
          nullable: false,
          constraints: { min: 0 },
        },
        {
          name: "master_category",
          type: "string",
          description: "Product category for the transaction",
          nullable: true,
        },
        {
          name: "product_name",
          type: "string",
          description: "Name of the product purchased",
          nullable: true,
          relationships: [
            {
              table: "products",
              field: "product_name",
              type: "one-to-many",
            },
          ],
        },
        {
          name: "birth_date",
          type: "date",
          description: "Customer birth date for age-based segmentation",
          nullable: true,
        },
        {
          name: "budtender_name",
          type: "string",
          description: "Staff member who handled the transaction",
          nullable: true,
        },
        {
          name: "inventory_profit",
          type: "number",
          description: "Profit margin on the transaction",
          nullable: true,
        },
      ],
      relationships: {
        location: {
          table: "locations",
          localField: "location_id",
          foreignField: "id",
          type: "belongs_to",
        },
        product: {
          table: "products",
          localField: "product_name",
          foreignField: "product_name",
          type: "belongs_to",
        },
      },
    };

    schema.tables.products = {
      name: "products",
      description: "Product catalog with detailed product information",
      primaryKey: "id",
      indexes: ["location_id", "product_name", "category", "brand_name"],
      fields: [
        {
          name: "id",
          type: "number",
          description: "Unique product identifier",
          nullable: false,
        },
        {
          name: "location_id",
          type: "number",
          description: "Location where product is available",
          nullable: false,
        },
        {
          name: "product_name",
          type: "string",
          description: "Name of the product",
          nullable: false,
        },
        {
          name: "category",
          type: "string",
          description: "Product category",
          nullable: true,
        },
        {
          name: "brand_name",
          type: "string",
          description: "Brand name of the product",
          nullable: true,
        },
        {
          name: "latest_price",
          type: "number",
          description: "Current price of the product",
          nullable: true,
          constraints: { min: 0 },
        },
        {
          name: "percentage_thc",
          type: "number",
          description: "THC percentage in the product",
          nullable: true,
          constraints: { min: 0, max: 100 },
        },
        {
          name: "percentage_cbd",
          type: "number",
          description: "CBD percentage in the product",
          nullable: true,
          constraints: { min: 0, max: 100 },
        },
      ],
      relationships: {
        location: {
          table: "locations",
          localField: "location_id",
          foreignField: "id",
          type: "belongs_to",
        },
        pos_transactions: {
          table: "pos_data",
          localField: "product_name",
          foreignField: "product_name",
          type: "has_many",
        },
      },
    };

    schema.tables.users = {
      name: "users",
      description: "Customer user profiles and data",
      primaryKey: "id",
      indexes: ["location_id", "external_id", "email"],
      fields: [
        {
          name: "id",
          type: "number",
          description: "Unique user identifier",
          nullable: false,
        },
        {
          name: "location_id",
          type: "number",
          description: "Location associated with the user",
          nullable: false,
        },
        {
          name: "external_id",
          type: "string",
          description: "External system identifier for the user",
          nullable: false,
        },
        {
          name: "email",
          type: "string",
          description: "User email address",
          nullable: true,
        },
        {
          name: "phone",
          type: "string",
          description: "User phone number",
          nullable: true,
        },
        {
          name: "data",
          type: "object",
          description: "Additional user data in JSON format",
          nullable: true,
        },
        {
          name: "created_at",
          type: "date",
          description: "When the user was created",
          nullable: false,
        },
      ],
      relationships: {
        location: {
          table: "locations",
          localField: "location_id",
          foreignField: "id",
          type: "belongs_to",
        },
      },
    };

    return schema;
  }

  /**
   * Get schema representation for AI context
   */
  getSchemaForAI(): string {
    if (!this.schema) {
      throw new Error("Schema not initialized. Call initialize() first.");
    }

    const schemaDescription = Object.entries(this.schema.tables)
      .map(([tableName, table]) => {
        const fieldsDescription = table.fields
          .map(
            (field) =>
              `  - ${field.name} (${field.type}): ${field.description}${
                field.constraints ? ` [Constraints: ${JSON.stringify(field.constraints)}]` : ""
              }`
          )
          .join("\n");

        const relationshipsDescription = Object.entries(table.relationships)
          .map(
            ([key, rel]) =>
              `  - ${key}: ${rel.type} relationship with ${rel.table}.${rel.foreignField}`
          )
          .join("\n");

        return `
Table: ${tableName}
Description: ${table.description}
Primary Key: ${table.primaryKey}
Fields:
${fieldsDescription}
Relationships:
${relationshipsDescription}
`;
      })
      .join("\n");

    return `Database Schema for Customer Segmentation:
${schemaDescription}

Available Join Patterns:
- pos_data ↔ products: Join on product_name
- pos_data ↔ locations: Join on location_id
- users ↔ locations: Join on location_id
- Cross-reference customers via customer_name in pos_data

Key Segmentation Fields:
- Customer behavior: gross_sales, net_sales, order_date frequency
- Demographics: birth_date (age calculation), customer_type
- Product preferences: master_category, product_name, brand preferences
- Transaction patterns: budtender_name, time-based patterns
- Profitability: inventory_profit, discount usage patterns
`;
  }

  /**
   * Run schema-aware customer segmentation analysis
   */
  async runSchemaAwareSegmentation(params: {
    location_id: number;
    start_date: string;
    end_date: string;
    segmentation_strategy?: "behavioral" | "demographic" | "value_based" | "hybrid";
    custom_rules?: RuleTree[];
  }): Promise<{
    segments: SchemaAwareCustomerSegment[];
    metadata: {
      totalCustomers: number;
      schemaVersion: string;
      generatedRules: number;
      dataQuality: number;
    };
  }> {
    if (!this.schema) {
      throw new Error("Schema not initialized. Call initialize() first.");
    }

    const { location_id, start_date, end_date, segmentation_strategy = "hybrid", custom_rules } = params;

    try {
      // Get sample data for rule generation context
      const sampleData = await this.getSampleData(location_id, start_date, end_date);

      // Create rule generation context
      const context: RuleGenerationContext = {
        schema: this.schema,
        availableFields: this.getAvailableFields(),
        sampleData,
        businessRules: this.getBusinessRules(),
        segmentationGoals: this.getSegmentationGoals(segmentation_strategy),
      };

      // Generate or use provided rules
      const rules = custom_rules || await this.generateSegmentationRules(context);

      // Execute segmentation with schema-aware rules
      const segments = await this.executeSchemaAwareSegmentation(location_id, start_date, end_date, rules);

      // Calculate metadata
      const totalCustomers = segments.reduce((sum, segment) => sum + (segment.customers?.length || 0), 0);
      const dataQuality = await this.calculateDataQuality(location_id, start_date, end_date);

      return {
        segments,
        metadata: {
          totalCustomers,
          schemaVersion: this.schema.version,
          generatedRules: rules.length,
          dataQuality,
        },
      };

    } catch (error) {
      logger.error("Error in schema-aware segmentation", { error, params });
      throw error;
    }
  }

  /**
   * Get sample data for rule generation context
   */
  private async getSampleData(locationId: number, startDate: string, endDate: string): Promise<Record<string, any>[]> {
    const queryParams = {
      table: "pos_data",
      columns: [
        "customer_name",
        "customer_type",
        "order_date",
        "gross_sales",
        "net_sales",
        "master_category",
        "product_name",
        "birth_date",
        "budtender_name",
        "inventory_profit"
      ],
      filters: {
        location_id: locationId,
        order_date: {
          gte: startDate,
          lte: endDate,
        },
        customer_name: {
          not: null,
        },
      },
      limit: 100, // Sample size for analysis
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      throw new Error(result.error);
    }

    return result.data.results || result.data || [];
  }

  /**
   * Get available fields for rule generation
   */
  private getAvailableFields(): string[] {
    if (!this.schema) return [];

    const fields: string[] = [];
    Object.values(this.schema.tables).forEach(table => {
      table.fields.forEach(field => {
        fields.push(`${table.name}.${field.name}`);
      });
    });

    return fields;
  }

  /**
   * Get business rules for segmentation
   */
  private getBusinessRules(): string[] {
    return [
      "High-value customers spend more than $500 per month",
      "Frequent customers visit more than 4 times per month",
      "VIP customers have both high spend and high frequency",
      "New customers have made their first purchase within 30 days",
      "At-risk customers haven't purchased in 60+ days",
      "Premium customers prefer products over $50",
      "Medical customers focus on CBD products",
      "Recreational customers prefer THC products",
    ];
  }

  /**
   * Get segmentation goals based on strategy
   */
  private getSegmentationGoals(strategy: string): string[] {
    const goals: Record<string, string[]> = {
      behavioral: [
        "Identify purchase frequency patterns",
        "Segment by spending behavior",
        "Find product preference clusters",
      ],
      demographic: [
        "Segment by age groups",
        "Identify customer type preferences",
        "Geographic clustering",
      ],
      value_based: [
        "Identify high-value customers",
        "Find profit-driving segments",
        "Loyalty-based segmentation",
      ],
      hybrid: [
        "Combine behavioral and demographic factors",
        "Multi-dimensional customer profiling",
        "Comprehensive segmentation approach",
      ],
    };

    return goals[strategy] || goals.hybrid;
  }

  /**
   * Generate segmentation rules based on context
   */
  private async generateSegmentationRules(context: RuleGenerationContext): Promise<RuleTree[]> {
    // Import the rule generator
    const { RelationshipAwareRuleGenerator } = await import("./RelationshipAwareRuleGenerator");
    const generator = new RelationshipAwareRuleGenerator(context.schema);

    // Generate relationship-aware rules
    const relationshipRules = generator.generateSegmentationRules(context);

    // Extract the rule trees
    return relationshipRules.map(r => r.rule);
  }

  /**
   * Execute schema-aware segmentation with generated rules
   */
  private async executeSchemaAwareSegmentation(
    locationId: number,
    startDate: string,
    endDate: string,
    rules: RuleTree[]
  ): Promise<SchemaAwareCustomerSegment[]> {
    const segments: SchemaAwareCustomerSegment[] = [];

    // For each rule, create a segment
    for (let i = 0; i < rules.length; i++) {
      const rule = rules[i];

      try {
        // Get customers matching this rule
        const customers = await this.getCustomersMatchingRule(locationId, startDate, endDate, rule);

        // Calculate segment metrics
        const metrics = this.calculateSegmentMetrics(customers);

        // Create segment
        const segment: SchemaAwareCustomerSegment = {
          id: rule.uuid,
          name: this.generateSegmentName(rule, i),
          description: this.generateSegmentDescription(rule),
          rules: rule,
          metadata: {
            createdAt: new Date(),
            updatedAt: new Date(),
            aiGenerated: true,
            confidence: 0.8, // TODO: Calculate based on rule complexity
            dataQuality: 0.9, // TODO: Calculate based on data completeness
          },
          customers,
          metrics,
        };

        segments.push(segment);

      } catch (error) {
        logger.error("Error processing segmentation rule", { error, rule });
      }
    }

    return segments;
  }

  /**
   * Get customers matching a specific rule
   */
  private async getCustomersMatchingRule(
    locationId: number,
    startDate: string,
    endDate: string,
    rule: RuleTree
  ): Promise<any[]> {
    // This is a simplified implementation
    // In a full implementation, you would convert the rule tree to SQL or use the rule engine

    const queryParams = {
      table: "pos_data",
      columns: [
        "customer_name",
        "SUM(gross_sales) as total_spend",
        "COUNT(*) as visit_count",
        "AVG(gross_sales) as avg_transaction",
        "MAX(order_date) as last_visit",
      ],
      filters: {
        location_id: locationId,
        order_date: {
          gte: startDate,
          lte: endDate,
        },
        customer_name: {
          not: null,
        },
      },
      group_by: ["customer_name"],
      order_by: [{ column: "total_spend", direction: "desc" }],
      limit: 100,
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      throw new Error(result.error);
    }

    // TODO: Apply rule filtering here
    // For now, return all customers (this would be replaced with rule engine evaluation)
    return result.data.results || result.data || [];
  }

  /**
   * Calculate metrics for a customer segment
   */
  private calculateSegmentMetrics(customers: any[]): {
    totalCustomers: number;
    totalSpend: number;
    avgTransactionValue: number;
    frequency: number;
  } {
    const totalCustomers = customers.length;
    const totalSpend = customers.reduce((sum, customer) => sum + parseFloat(customer.total_spend || 0), 0);
    const totalVisits = customers.reduce((sum, customer) => sum + parseInt(customer.visit_count || 0), 0);

    return {
      totalCustomers,
      totalSpend,
      avgTransactionValue: totalSpend / totalVisits || 0,
      frequency: totalVisits / totalCustomers || 0,
    };
  }

  /**
   * Generate a human-readable segment name
   */
  private generateSegmentName(rule: RuleTree, index: number): string {
    // Simple name generation based on rule structure
    if (rule.type === "wrapper" && rule.children) {
      const conditions = rule.children.length;
      return `Segment ${index + 1} (${conditions} conditions)`;
    }

    return `Segment ${index + 1}`;
  }

  /**
   * Generate a segment description based on the rule
   */
  private generateSegmentDescription(rule: RuleTree): string {
    // Simple description generation
    if (rule.type === "wrapper" && rule.children) {
      return `Customers matching ${rule.children.length} behavioral and demographic criteria`;
    }

    return "Customer segment based on schema-aware rules";
  }

  /**
   * Calculate data quality score
   */
  private async calculateDataQuality(locationId: number, startDate: string, endDate: string): Promise<number> {
    // Simple data quality calculation based on completeness
    const queryParams = {
      table: "pos_data",
      columns: [
        "COUNT(*) as total_records",
        "COUNT(customer_name) as records_with_customer",
        "COUNT(birth_date) as records_with_birthdate",
        "COUNT(product_name) as records_with_product",
      ],
      filters: {
        location_id: locationId,
        order_date: {
          gte: startDate,
          lte: endDate,
        },
      },
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      return 0.5; // Default quality score
    }

    const data = result.data.results?.[0] || result.data?.[0];
    if (!data) return 0.5;

    const totalRecords = parseInt(data.total_records || 0);
    if (totalRecords === 0) return 0;

    const customerCompleteness = parseInt(data.records_with_customer || 0) / totalRecords;
    const birthdateCompleteness = parseInt(data.records_with_birthdate || 0) / totalRecords;
    const productCompleteness = parseInt(data.records_with_product || 0) / totalRecords;

    // Weighted average of completeness scores
    return (customerCompleteness * 0.5 + birthdateCompleteness * 0.2 + productCompleteness * 0.3);
  }
}
