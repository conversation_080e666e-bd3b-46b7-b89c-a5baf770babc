import { Tool, <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext } from "./interfaces";
import { logger } from "../config/logger";
import { OpenAI } from "openai";
import { Knex } from "knex";

export class MarketInsightsTool implements Tool {
  name = "market_insights_generator";
  description =
    "Generates market insights for a location based on local data, competitors, and market conditions";

  parameters = [
    {
      name: "locationId",
      type: "number" as const,
      description: "The location ID to generate insights for",
      required: true,
    },
    {
      name: "insightCount",
      type: "number" as const,
      description: "Number of insights to generate (default: 4)",
      required: false,
    },
  ];

  private openai: OpenAI;
  private db: Knex;

  constructor(openai: OpenAI, db: Knex) {
    this.openai = openai;
    this.db = db;
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      const locationId = params.locationId as number;
      const insightCount = (params.insightCount as number) || 4;

      logger.info({
        message: "MarketInsightsTool.execute called",
        params,
        locationId,
        insightCount,
        context: ctx,
      });

      if (!locationId) {
        logger.error({
          message: "MarketInsightsTool: Missing locationId",
          params,
        });
        return {
          status: "error",
          error: "Location ID is required",
        };
      }

      logger.info({
        message: "Starting market insights generation",
        locationId,
        insightCount,
      });

      // Gather location data
      logger.info({
        message: "Gathering location data",
        locationId,
      });
      const locationData = await this.gatherLocationData(locationId);

      logger.info({
        message: "Location data gathered successfully",
        locationId,
        hasLocation: !!locationData.location,
        competitorCount: locationData.competitors?.length || 0,
        productCount: locationData.productCount,
        hasSalesData: !!locationData.salesData,
      });

      // Generate insights using AI
      logger.info({
        message: "Calling generateInsightsWithAI",
        locationId,
        insightCount,
      });
      const insights = await this.generateInsightsWithAI(
        locationData,
        insightCount
      );

      logger.info({
        message: "Insights generation completed",
        locationId,
        insightsGenerated: insights?.length || 0,
        insights,
      });

      return {
        status: "success",
        data: {
          insights,
          location: locationData.location,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error({
        message: "Error in MarketInsightsTool.execute",
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        params,
      });
      return {
        status: "error",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  private async gatherLocationData(locationId: number) {
    // Get location details
    const location = await this.db("locations")
      .where({ id: locationId })
      .select("id", "name", "city", "state", "retailer_id")
      .first();

    if (!location) {
      throw new Error(`Location ${locationId} not found`);
    }

    // Get competitors
    const competitors = await this.db("location_competitors")
      .where("location_id", locationId)
      .select("name", "distance", "competitor_place_id")
      .limit(5);

    // Get recent product data (if available)
    const productCount = await this.db("products")
      .where("location_id", locationId)
      .count("id as count")
      .first();

    // Get recent sales data (if available)
    let recentSalesData = null;
    try {
      const salesCount = await this.db("pos_data")
        .where("location_id", locationId)
        .where(
          "transaction_date",
          ">=",
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        )
        .count("id as count")
        .first();
      recentSalesData = salesCount;
    } catch (error) {
      // Sales data might not be available for all locations
      logger.debug("No sales data available for location", locationId);
    }

    return {
      location,
      competitors: competitors || [],
      productCount: productCount?.count || 0,
      salesData: recentSalesData,
    };
  }

  private async generateInsightsWithAI(
    locationData: any,
    insightCount: number
  ) {
    const { location, competitors, productCount, salesData } = locationData;

    const competitorInfo =
      competitors.length > 0
        ? `Competitors in the area: ${competitors
            .map(
              (c: any) =>
                `${c.name}${
                  c.distance
                    ? ` (${Math.round(c.distance * 10) / 10}mi away)`
                    : ""
                }`
            )
            .join(", ")}`
        : "No competitor data available";

    const locationName = `${location.city}${
      location.state ? `, ${location.state}` : ""
    }`;

    const prompt = `You are a cannabis market analyst. Generate ${insightCount} quick, actionable market insights for a dispensary.

Location: ${location.name} in ${locationName}
Product count: ${productCount}
${competitorInfo}
${
  salesData
    ? `Recent transaction activity: ${salesData.count} transactions in last 30 days`
    : ""
}

Generate insights in this EXACT JSON format:
{
  "insights": [
    {
      "title": "Brief title (max 25 characters)",
      "content": "Concise insight (max 80 characters)",
      "type": "market|trending|demographics|performance",
      "action": "View Details"
    }
  ]
}

Focus on:
- Local market conditions and opportunities
- Competitive positioning 
- Customer demographics and trends
- Seasonal patterns for ${location.state || "the area"}
- Performance insights based on available data

Make insights specific to ${locationName} and actionable. Use real data when available, market knowledge when not.
Each insight should be brief enough for a compact card display.`;

    try {
      logger.info({
        message: "Starting OpenAI chat completion for market insights",
        locationId: location.id,
        locationName,
        productCount,
        competitorCount: competitors.length,
        promptLength: prompt.length,
        model: "gpt-4",
        temperature: 0.7,
      });

      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content:
              "You are a cannabis market analyst. Always respond with valid JSON only.",
          },
          { role: "user", content: prompt },
        ],
        temperature: 0.7,
        response_format: { type: "json_object" },
      });

      logger.info({
        message: "OpenAI response received",
        locationId: location.id,
        responseId: response.id,
        model: response.model,
        usage: response.usage,
        choices: response.choices?.length || 0,
        hasContent: !!response.choices[0]?.message?.content,
        contentLength: response.choices[0]?.message?.content?.length || 0,
      });

      const rawContent = response.choices[0].message.content || "{}";
      logger.info({
        message: "Raw OpenAI response content",
        locationId: location.id,
        rawContent: rawContent.substring(0, 500), // Log first 500 chars
        fullContentLength: rawContent.length,
      });

      const result = JSON.parse(rawContent);

      logger.info({
        message: "Parsed OpenAI response",
        locationId: location.id,
        hasInsights: !!result.insights,
        isInsightsArray: Array.isArray(result.insights),
        insightsCount: result.insights?.length || 0,
        resultKeys: Object.keys(result),
      });

      if (!result.insights || !Array.isArray(result.insights)) {
        logger.error({
          message: "Invalid response format from AI",
          locationId: location.id,
          result,
          expectedFormat: "{ insights: [...] }",
        });
        throw new Error("Invalid response format from AI");
      }

      // Validate and clean up insights
      const validatedInsights = result.insights
        .filter(
          (insight: any) => insight.title && insight.content && insight.type
        )
        .slice(0, insightCount)
        .map((insight: any) => ({
          title: String(insight.title).substring(0, 25),
          content: String(insight.content).substring(0, 80),
          type: this.validateInsightType(insight.type),
          action: "View Details",
        }));

      logger.info({
        message: "Successfully generated AI insights",
        locationId: location.id,
        generatedCount: validatedInsights.length,
        requestedCount: insightCount,
        finalInsights: validatedInsights,
      });

      return validatedInsights;
    } catch (error) {
      logger.error({
        message: "Error calling OpenAI for insights",
        locationId: location.id,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
      });

      // Fallback to basic insights if AI fails
      return this.generateFallbackInsights(locationData, insightCount);
    }
  }

  private validateInsightType(
    type: string
  ): "market" | "trending" | "demographics" | "performance" {
    const validTypes = ["market", "trending", "demographics", "performance"];
    return validTypes.includes(type) ? (type as any) : "market";
  }

  private generateFallbackInsights(locationData: any, insightCount: number) {
    const { location, competitors } = locationData;
    const locationName = `${location.city}${
      location.state ? `, ${location.state}` : ""
    }`;

    const fallbackInsights = [
      {
        title: `${location.city} Market`,
        content: `Growing cannabis market in ${locationName} with strong potential`,
        type: "market" as const,
        action: "View Details",
      },
      {
        title: "Local Demographics",
        content: `Primary customer base: Adults 25-45 in ${locationName}`,
        type: "demographics" as const,
        action: "View Details",
      },
      {
        title: "Competition",
        content:
          competitors.length > 0
            ? `${competitors.length} competitors nearby, differentiation key`
            : `Limited competition in ${locationName}, opportunity to lead`,
        type: "market" as const,
        action: "View Details",
      },
      {
        title: "Seasonal Trends",
        content: `${
          location.state || "Local"
        } cannabis sales peak during holidays`,
        type: "performance" as const,
        action: "View Details",
      },
    ];

    return fallbackInsights.slice(0, insightCount);
  }
}
