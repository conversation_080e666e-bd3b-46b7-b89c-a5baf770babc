import { parse, Options } from "csv-parse";
import { Readable } from "stream";
import { FileStream } from "../storage/FileStream";
import { PosData } from "../pos/PosData";
import { User, UserParams } from "../users/User";
import { Product, ProductParams } from "../products/Product";
import { ProductMapper } from "../products/ProductMapper";
import { Review, ReviewParams } from "../reviews/Review";
import { Retailer, RetailerParams } from "../retailers/Retailer";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { CoaData } from "../coa/CoaData";

export interface NormalizedData {
  type: "pos" | "customer" | "product" | "review" | "retailer" | "coa";
  data: Array<
    | Partial<PosData>
    | UserParams
    | ProductParams
    | Partial<ReviewParams>
    | Partial<RetailerParams>
    | Partial<CoaData>
  >;
  errors: Array<{
    row: number;
    error: string;
  }>;
}

interface FieldMapping {
  source: string[];
  target: string;
  type: "string" | "number" | "date" | "boolean";
  required?: boolean;
  transform?: (value: any) => any;
}

const POS_FIELD_MAPPINGS: FieldMapping[] = [
  {
    source: ["Location Name", "location_name", "location name"],
    target: "location_name",
    type: "string",
  },
  {
    source: [
      "Master Category",
      "master_category",
      "category",
      "master category",
    ],
    target: "master_category",
    type: "string",
  },
  {
    source: ["Order Date", "order_date", "date", "order date"],
    target: "order_date",
    type: "date",
    required: true,
  },
  {
    source: ["Customer Type", "customer_type", "customer type"],
    target: "customer_type",
    type: "string",
  },
  {
    source: ["Budtender Name", "budtender_name", "employee", "budtender name"],
    target: "budtender_name",
    type: "string",
  },
  {
    source: ["Gross Sales", "gross_sales", "sales", "gross sales"],
    target: "gross_sales",
    type: "number",
  },
  {
    source: [
      "Returned Amount",
      "returned_amount",
      "returns",
      "returned amount",
    ],
    target: "returned_amount",
    type: "number",
  },
  {
    source: [
      "Discounted Amount",
      "discounted_amount",
      "discount",
      "discounted amount",
    ],
    target: "discounted_amount",
    type: "number",
  },
  {
    source: ["Loyalty as Discount", "loyalty_discount", "loyalty as discount"],
    target: "loyalty_as_discount",
    type: "number",
  },
  {
    source: ["Net Sales", "net_sales", "net sales"],
    target: "net_sales",
    type: "number",
  },
  {
    source: ["Inventory Cost", "cost"],
    target: "inventory_cost",
    type: "number",
  },
  {
    source: ["Inventory Profit", "profit"],
    target: "inventory_profit",
    type: "number",
  },
  {
    source: ["Loyalty as Payment", "loyalty_payment"],
    target: "loyalty_as_payment",
    type: "number",
  },
  { source: ["Tax Amount", "tax"], target: "tax_amount", type: "number" },
  {
    source: ["Invoice Total", "total"],
    target: "invoice_total",
    type: "number",
  },
  {
    source: ["Amount Paid in Cash", "cash_payment"],
    target: "amount_paid_in_cash",
    type: "number",
  },
  {
    source: ["Amount Paid in Debit", "debit_payment"],
    target: "amount_paid_in_debit",
    type: "number",
  },
  {
    source: ["Birth Date", "birth_date", "dob"],
    target: "birth_date",
    type: "date",
  },
  {
    source: ["Customer Name", "customer_name", "customer"],
    target: "customer_name",
    type: "string",
  },
  {
    source: ["Product Name", "product_name", "product"],
    target: "product_name",
    type: "string",
  },
  {
    source: ["Email", "email_address", "customer_email"],
    target: "email",
    type: "string",
  },
  {
    source: ["Phone", "phone_number", "mobile", "customer_phone"],
    target: "phone",
    type: "string",
  },
];

const CUSTOMER_FIELD_MAPPINGS: FieldMapping[] = [
  { source: ["Email", "email_address"], target: "email", type: "string" },
  {
    source: ["Phone", "phone_number", "mobile"],
    target: "phone",
    type: "string",
  },
  {
    source: ["First Name", "first_name", "firstname"],
    target: "data.first_name",
    type: "string",
  },
  {
    source: ["Last Name", "last_name", "lastname"],
    target: "data.last_name",
    type: "string",
  },
  {
    source: ["Full Name", "full_name", "name"],
    target: "data.full_name",
    type: "string",
  },
  {
    source: ["Birth Date", "birth_date", "dob"],
    target: "data.birth_date",
    type: "date",
  },
  {
    source: ["Customer Type", "customer_type", "type"],
    target: "data.customer_type",
    type: "string",
  },
  {
    source: ["External ID", "external_id", "id"],
    target: "external_id",
    type: "string",
    required: true,
  },
];

const PRODUCT_FIELD_MAPPINGS: FieldMapping[] = [
  {
    source: ["SKU", "Product SKU", "meta_sku"],
    target: "meta_sku",
    type: "string",
    required: true,
  },
  {
    source: ["Retailer ID", "Store ID", "retailer_id"],
    target: "retailer_id",
    type: "string",
    required: true,
  },
  {
    source: ["Raw Product Name", "Original Name", "raw_product_name"],
    target: "raw_product_name",
    type: "string",
    required: true,
  },
  {
    source: ["Product Name", "Name", "product_name", "name"],
    target: "product_name",
    type: "string",
    required: true,
  },
  {
    source: ["Medical", "Is Medical", "medical"],
    target: "medical",
    type: "boolean",
    required: true,
  },
  {
    source: ["Recreational", "Is Recreational", "recreational"],
    target: "recreational",
    type: "boolean",
    required: true,
  },
  {
    source: ["Cannabis SKU", "Cann SKU", "cann_sku_id"],
    target: "cann_sku_id",
    type: "string",
  },
  {
    source: ["Brand Name", "Brand", "brand_name"],
    target: "brand_name",
    type: "string",
  },
  {
    source: ["Brand ID", "brand_id"],
    target: "brand_id",
    type: "number",
  },
  {
    source: ["URL", "Product URL", "url"],
    target: "url",
    type: "string",
  },
  {
    source: ["Image URL", "Image", "image_url"],
    target: "image_url",
    type: "string",
  },
  {
    source: ["Images URLs", "image_urls", "images_urls"],
    target: "images_urls",
    type: "string",
  },
  {
    source: ["Raw Weight", "Original Weight", "raw_weight_string"],
    target: "raw_weight_string",
    type: "string",
  },
  {
    source: ["Display Weight", "Weight", "display_weight"],
    target: "display_weight",
    type: "string",
  },
  {
    source: ["Raw Category", "Original Category", "raw_product_category"],
    target: "raw_product_category",
    type: "string",
  },
  {
    source: ["Category", "Product Category", "category", "product_category"],
    target: "category",
    type: "string",
  },
  {
    source: ["Raw Subcategory", "Original Subcategory", "raw_subcategory"],
    target: "raw_subcategory",
    type: "string",
  },
  {
    source: [
      "Subcategory",
      "Product Subcategory",
      "subcategory",
      "product_subcategory",
    ],
    target: "subcategory",
    type: "string",
  },
  {
    source: ["Tags", "Product Tags", "product_tags"],
    target: "product_tags",
    type: "string",
    transform: (value: string) => value.split(",").map((t: string) => t.trim()),
  },
  {
    source: ["THC %", "THC Percentage", "percentage_thc"],
    target: "percentage_thc",
    type: "number",
  },
  {
    source: ["CBD %", "CBD Percentage", "percentage_cbd"],
    target: "percentage_cbd",
    type: "number",
  },
  {
    source: ["THC mg", "THC Content", "mg_thc"],
    target: "mg_thc",
    type: "number",
  },
  {
    source: ["CBD mg", "CBD Content", "mg_cbd"],
    target: "mg_cbd",
    type: "number",
  },
  {
    source: ["Quantity", "Items Per Package", "quantity_per_package"],
    target: "quantity_per_package",
    type: "number",
  },
  {
    source: ["Price", "Latest Price", "latest_price"],
    target: "latest_price",
    type: "number",
  },
  {
    source: ["Menu Provider", "Provider", "menu_provider"],
    target: "menu_provider",
    type: "string",
  },
  {
    source: ["Review Summary", "review_summary"],
    target: "review_summary",
    type: "string",
  },
  {
    source: ["Rating", "rating"],
    target: "rating",
    type: "number",
  },
  {
    source: ["Reviews Count", "reviews_count"],
    target: "reviews_count",
    type: "number",
  },
  {
    source: ["Product Description", "description", "product_description"],
    target: "product_description",
    type: "string",
  },
  {
    source: [
      "Product Short Description",
      "short_description",
      "product_short_description",
    ],
    target: "short_description",
    type: "string",
  },
  {
    source: ["THC", "thc"],
    target: "thc",
    type: "number",
  },
  {
    source: ["CBD", "cbd"],
    target: "cbd",
    type: "number",
  },
  {
    source: ["Variants", "variants"],
    target: "variants",
    type: "string",
  },
  {
    source: ["Enhancement Status", "enhancement_status"],
    target: "enhancement_status",
    type: "string",
  },
  {
    source: ["Mood", "mood"],
    target: "mood",
    type: "string",
    transform: (value: any) =>
      Array.isArray(value) ? value : typeof value === "string" ? [value] : null,
  },
  {
    source: ["Estimated CBD Percentage", "estimated_cbd_percentage"],
    target: "estimated_cbd_percentage",
    type: "string",
  },
  {
    source: ["Updated At", "updated_at"],
    target: "updated_at",
    type: "date",
  },
  {
    source: ["Estimated THC Percentage", "estimated_thc_percentage"],
    target: "estimated_thc_percentage",
    type: "string",
  },
  {
    source: ["Effects", "effects"],
    target: "effects",
    type: "string",
  },
  {
    source: ["Enhancement Error", "enhancement_error"],
    target: "enhancement_error",
    type: "string",
  },
  {
    source: ["ID", "id"],
    target: "id",
    type: "string",
  },
];

const REVIEW_FIELD_MAPPINGS: FieldMapping[] = [
  {
    source: ["Review ID", "review_id", "id"],
    target: "id",
    type: "string",
    required: true,
  },
  {
    source: ["Product ID", "product_id", "meta_sku"],
    target: "product_id",
    type: "string",
    required: true,
  },
  {
    source: ["Rating", "rating", "score"],
    target: "rating",
    type: "number",
    required: true,
  },
  {
    source: ["Review Text", "text", "content", "review_text"],
    target: "text",
    type: "string",
  },
  {
    source: ["Author", "author", "reviewer", "user_name"],
    target: "author",
    type: "string",
  },
  {
    source: ["Created Date", "date", "created_at", "posted_at"],
    target: "created_at",
    type: "date",
  },
  {
    source: ["Helpful Votes", "helpful_votes", "upvotes"],
    target: "helpful_votes",
    type: "number",
  },
  {
    source: ["Retailer ID", "retailer_id", "dispensary_id"],
    target: "retailer_id",
    type: "string",
  },
  {
    source: ["Title", "review_title", "title"],
    target: "title",
    type: "string",
  },
  {
    source: ["Verified Purchase", "verified", "verified_purchase"],
    target: "verified_purchase",
    type: "boolean",
  },
];

const RETAILER_FIELD_MAPPINGS: FieldMapping[] = [
  {
    source: ["Retailer ID", "retailer_id", "id", "dispensary_id"],
    target: "retailer_id",
    type: "string",
    required: true,
  },
  {
    source: ["Name", "retailer_name", "name", "dispensary_name"],
    target: "name",
    type: "string",
    required: true,
  },
  {
    source: ["Slug", "slug", "cann_dispensary_slug"],
    target: "slug",
    type: "string",
  },
  {
    source: ["Address", "address", "street_address"],
    target: "address",
    type: "string",
  },
  {
    source: ["City", "city"],
    target: "city",
    type: "string",
  },
  {
    source: ["State", "state", "province"],
    target: "state",
    type: "string",
  },
  {
    source: ["Zip Code", "zip_code", "zipCode", "postal_code"],
    target: "zip_code",
    type: "string",
  },
  {
    source: ["Country", "country"],
    target: "country",
    type: "string",
  },
  {
    source: ["Phone Number", "phoneNumber", "phone", "contact_phone"],
    target: "phone",
    type: "string",
  },
  {
    source: ["Email", "email", "contact_email"],
    target: "email",
    type: "string",
  },
  {
    source: ["Website URL", "webUrl", "website", "website_url"],
    target: "website_url",
    type: "string",
  },
  {
    source: ["Latitude", "latitude", "lat"],
    target: "latitude",
    type: "string",
  },
  {
    source: ["Longitude", "longitude", "long", "lng"],
    target: "longitude",
    type: "string",
  },
  {
    source: ["Rating", "rating"],
    target: "rating",
    type: "number",
  },
  {
    source: ["Reviews Count", "reviewsCount", "reviews_count"],
    target: "reviews_count",
    type: "number",
  },
  {
    source: ["Description", "description", "introBody"],
    target: "description",
    type: "string",
  },
  {
    source: ["Hours", "hours", "todaysHoursStr", "hours_of_operation"],
    target: "hours",
    type: "string",
  },
  {
    source: ["Open Now", "openNow", "is_open"],
    target: "is_open",
    type: "boolean",
  },
  {
    source: ["License Type", "licenseType", "license_type"],
    target: "license_type",
    type: "string",
  },
  {
    source: ["Medical", "serves_medical_users", "medical"],
    target: "serves_medical_users",
    type: "boolean",
  },
  {
    source: ["Recreational", "serves_recreational_users", "recreational"],
    target: "serves_recreational_users",
    type: "boolean",
  },
  {
    source: ["Services", "retailerServices", "services"],
    target: "services",
    type: "string",
    transform: (value: any) => {
      if (Array.isArray(value)) {
        return value;
      }
      if (typeof value === "string") {
        return value.split(",").map((s) => s.trim());
      }
      return null;
    },
  },
  {
    source: ["Online Ordering", "onlineOrdering", "online_ordering"],
    target: "online_ordering",
    type: "string",
    transform: (value: any) => {
      if (typeof value === "object" && value !== null) {
        return JSON.stringify(value);
      }
      if (typeof value === "boolean") {
        return String(value);
      }
      return value;
    },
  },
  {
    source: ["Avatar Image", "avatarImage", "avatar", "logo"],
    target: "avatar",
    type: "string",
    transform: (value: any) => {
      if (typeof value === "object" && value !== null) {
        return JSON.stringify(value);
      }
      return value;
    },
  },
  {
    source: ["Is Active", "is_active", "active"],
    target: "is_active",
    type: "boolean",
  },
  {
    source: ["Updated At", "updated_at"],
    target: "updated_at",
    type: "date",
  },
];

const COA_FIELD_MAPPINGS: FieldMapping[] = [
  {
    source: ["Product Name", "Name", "product_name"],
    target: "product_name",
    type: "string",
    required: true,
  },
  {
    source: ["Product Type", "Type", "product_type"],
    target: "product_type",
    type: "string",
    required: true,
  },
  {
    source: ["Batch Number", "Lot Number", "batch_number", "batch_id"],
    target: "batch_number",
    type: "string",
    required: true,
  },
  {
    source: ["Sample ID", "sample_id", "sample id"],
    target: "sample_id",
    type: "string",
    required: true,
  },
  {
    source: ["Production Date", "production_date", "production date"],
    target: "production_date",
    type: "date",
  },
  {
    source: ["Test Date", "test_date", "date", "test date"],
    target: "test_date",
    type: "date",
    required: true,
  },
  {
    source: ["Lab Name", "lab_name", "lab", "laboratory"],
    target: "lab_name",
    type: "string",
    required: true,
  },
  {
    source: ["Product ID", "SKU", "meta_sku", "product_id", "product_sku"],
    target: "product_id",
    type: "string",
    required: true,
  },
  {
    source: ["Lab License Number", "lab_license", "license"],
    target: "lab_license",
    type: "string",
  },
  {
    source: ["COA URL", "Certificate URL", "coa_url", "certificate link"],
    target: "coa_url",
    type: "string",
  },
  {
    source: ["THC", "D9-THC", "thc_percent", "THC %"],
    target: "thc_percent",
    type: "number",
  },
  {
    source: ["THCA", "thca_percent", "THCA %"],
    target: "thca_percent",
    type: "number",
  },
  {
    source: ["CBD", "cbd_percent", "CBD %"],
    target: "cbd_percent",
    type: "number",
  },
  {
    source: ["CBDA", "cbda_percent", "CBDA %"],
    target: "cbda_percent",
    type: "number",
  },
  {
    source: ["CBG", "cbg_percent", "CBG %"],
    target: "cbg_percent",
    type: "number",
  },
  {
    source: ["CBGA", "cbga_percent", "CBGA %"],
    target: "cbga_percent",
    type: "number",
  },
  {
    source: ["CBN", "cbn_percent", "CBN %"],
    target: "cbn_percent",
    type: "number",
  },
  {
    source: ["CBC", "cbc_percent", "CBC %"],
    target: "cbc_percent",
    type: "number",
  },
  {
    source: ["Total THC", "total_thc"],
    target: "total_thc",
    type: "number",
  },
  {
    source: ["Total CBD", "total_cbd"],
    target: "total_cbd",
    type: "number",
  },
  {
    source: ["Total Cannabinoids", "total_cannabinoids"],
    target: "total_cannabinoids",
    type: "number",
  },
  {
    source: ["Myrcene", "terpene_myrcene"],
    target: "terpene_myrcene",
    type: "number",
  },
  {
    source: ["Limonene", "terpene_limonene"],
    target: "terpene_limonene",
    type: "number",
  },
  {
    source: ["Caryophyllene", "terpene_caryophyllene"],
    target: "terpene_caryophyllene",
    type: "number",
  },
  {
    source: ["Linalool", "terpene_linalool"],
    target: "terpene_linalool",
    type: "number",
  },
  {
    source: ["Pinene", "terpene_pinene"],
    target: "terpene_pinene",
    type: "number",
  },
  {
    source: ["Humulene", "terpene_humulene"],
    target: "terpene_humulene",
    type: "number",
  },
  {
    source: ["Terpinolene", "terpene_terpinolene"],
    target: "terpene_terpinolene",
    type: "number",
  },
  {
    source: ["Total Terpenes", "total_terpenes"],
    target: "total_terpenes",
    type: "number",
  },
  {
    source: ["Butane", "solvent_butane"],
    target: "solvent_butane",
    type: "number",
  },
  {
    source: ["Propane", "solvent_propane"],
    target: "solvent_propane",
    type: "number",
  },
  {
    source: ["Ethanol", "solvent_ethanol"],
    target: "solvent_ethanol",
    type: "number",
  },
  {
    source: ["Solvents Pass/Fail", "solvents_pass"],
    target: "solvents_pass",
    type: "boolean",
  },
  {
    source: ["Lead", "metal_lead"],
    target: "metal_lead",
    type: "number",
  },
  {
    source: ["Mercury", "metal_mercury"],
    target: "metal_mercury",
    type: "number",
  },
  {
    source: ["Arsenic", "metal_arsenic"],
    target: "metal_arsenic",
    type: "number",
  },
  {
    source: ["Cadmium", "metal_cadmium"],
    target: "metal_cadmium",
    type: "number",
  },
  {
    source: ["Metals Pass/Fail", "metals_pass"],
    target: "metals_pass",
    type: "boolean",
  },
  {
    source: ["E. coli", "microbial_ecoli", "ecoli"],
    target: "microbial_ecoli",
    type: "boolean",
  },
  {
    source: ["Salmonella", "microbial_salmonella"],
    target: "microbial_salmonella",
    type: "boolean",
  },
  {
    source: ["Aspergillus", "microbial_aspergillus"],
    target: "microbial_aspergillus",
    type: "boolean",
  },
  {
    source: ["Total Yeast & Mold", "yeast_mold_count"],
    target: "yeast_mold_count",
    type: "number",
  },
  {
    source: ["Microbials Pass/Fail", "microbials_pass"],
    target: "microbials_pass",
    type: "boolean",
  },
  {
    source: ["Moisture Content", "moisture_content"],
    target: "moisture_content",
    type: "number",
  },
  {
    source: ["Water Activity", "water_activity"],
    target: "water_activity",
    type: "number",
  },
  {
    source: ["Overall Pass/Fail", "overall_pass", "passed"],
    target: "overall_pass",
    type: "boolean",
  },
];

export class DataNormalizationService {
  private static formatDate(dateStr: string | null): Date | null {
    if (!dateStr) return null;

    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return null;

      // Check if date is within acceptable MySQL range
      if (date < new Date("1000-01-01") || date > new Date("9999-12-31")) {
        return null;
      }

      return date;
    } catch {
      return null;
    }
  }

  private static parseFloat(value: string | null): number {
    if (!value) return 0;
    const num = Number(value.replace(/[^0-9.-]+/g, ""));
    return isNaN(num) ? 0 : num;
  }

  private static findMatchingField(
    header: string,
    mappings: FieldMapping[]
  ): FieldMapping | null {
    const normalizedHeader = header.toLowerCase();
    return (
      mappings.find((mapping) =>
        mapping.source.some((s) => s.toLowerCase() === normalizedHeader)
      ) || null
    );
  }

  private static transformValue(value: any, mapping: FieldMapping): any {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    // Use custom transform function if provided
    if (mapping.transform) {
      return mapping.transform(value);
    }

    switch (mapping.type) {
      case "number":
        return this.parseFloat(value);
      case "date":
        return this.formatDate(value);
      case "boolean":
        if (typeof value === "boolean") return value;
        if (typeof value === "string") {
          const normalizedValue = value.toLowerCase().trim();
          return (
            normalizedValue === "true" ||
            normalizedValue === "yes" ||
            normalizedValue === "1"
          );
        }
        return Boolean(value);
      default:
        // Handle objects and arrays
        if (typeof value === "object") {
          if (Array.isArray(value)) {
            return value; // Return array as is
          }
          return JSON.stringify(value); // Convert object to JSON string
        }
        return String(value).trim();
    }
  }

  private static async parseCSV(
    stream: FileStream
  ): Promise<{ headers: string[]; rows: any[] }> {
    const options: Options = {
      columns: true,
      skip_empty_lines: true,
      bom: true,
      relax_column_count: true,
    };

    const parser = stream.file.pipe(parse(options));
    const rows: any[] = [];
    const headers: string[] = [];
    let isFirstRow = true;

    for await (const row of parser) {
      if (isFirstRow) {
        headers.push(...Object.keys(row));
        isFirstRow = false;
      }
      rows.push(row);
    }

    return { headers, rows };
  }

  private static async generateAIMappings(
    headers: string[],
    targetType: "pos" | "customer" | "product" | "review" | "retailer" | "coa"
  ): Promise<FieldMapping[]> {
    try {
      const model = new ChatOpenAI({
        modelName: "gpt-4.1-mini",
        temperature: 0.2,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      const targetFields = {
        pos: POS_FIELD_MAPPINGS,
        customer: CUSTOMER_FIELD_MAPPINGS,
        product: PRODUCT_FIELD_MAPPINGS,
        review: REVIEW_FIELD_MAPPINGS,
        retailer: RETAILER_FIELD_MAPPINGS,
        coa: COA_FIELD_MAPPINGS,
      }[targetType];

      const prompt = `
        Analyze these CSV headers and map them to our standard fields.
        Headers: ${JSON.stringify(headers)}
        
        Target fields and their possible variations:
        ${JSON.stringify(targetFields, null, 2)}
        
        Return a JSON array of mappings in this format:
        [
          {
            "source": "actual_header_name",
            "target": "standard_field_name",
            "confidence": 0.0-1.0,
            "explanation": "Brief explanation of why this mapping was chosen"
          }
        ]
        
        Guidelines:
        1. Only include mappings with confidence > 0.7
        2. Use semantic similarity and common variations
        3. Consider common abbreviations and industry terms
        4. Account for different naming conventions (camelCase, snake_case, etc.)
        5. Look for contextual clues in the header names
        
        Return ONLY the JSON array, no other text.
      `;

      const response = await model.call([
        new SystemMessage(
          "You are a data mapping expert specializing in cannabis industry data. Return only valid JSON arrays."
        ),
        new HumanMessage(prompt),
      ]);

      let aiMappings;
      try {
        aiMappings = JSON.parse(
          typeof response.content === "string"
            ? response.content
            : JSON.stringify(response.content)
        );
      } catch (parseError) {
        console.error("Failed to parse AI response:", parseError);
        return []; // Return empty array if parsing fails
      }

      // Log the AI mappings for debugging
      console.log(
        "AI Generated Mappings:",
        JSON.stringify(aiMappings, null, 2)
      );

      // Convert AI mappings to FieldMapping format with validation
      return aiMappings
        .filter((m: any) => {
          const isValid =
            m.confidence > 0.7 &&
            typeof m.source === "string" &&
            typeof m.target === "string";

          if (!isValid) {
            console.warn("Invalid AI mapping:", m);
          }
          return isValid;
        })
        .map((m: any) => {
          const targetMapping = targetFields.find((f) => f.target === m.target);
          if (!targetMapping) {
            console.warn(`No target mapping found for: ${m.target}`);
            return null;
          }
          return {
            ...targetMapping,
            source: [m.source], // Override source with actual header
          };
        })
        .filter(Boolean);
    } catch (error) {
      console.error("Error generating AI mappings:", error);
      // Fallback to basic matching if AI fails
      const targetFields = {
        pos: POS_FIELD_MAPPINGS,
        customer: CUSTOMER_FIELD_MAPPINGS,
        product: PRODUCT_FIELD_MAPPINGS,
        review: REVIEW_FIELD_MAPPINGS,
        retailer: RETAILER_FIELD_MAPPINGS,
        coa: COA_FIELD_MAPPINGS,
      }[targetType];

      const fallbackMappings: FieldMapping[] = [];

      for (const header of headers) {
        const normalizedHeader = header
          .toLowerCase()
          .replace(/[^a-z0-9]/g, "_");

        // Try exact match first
        const exactMatch = targetFields.find((f) =>
          f.source.some((s) => s.toLowerCase() === normalizedHeader)
        );

        if (exactMatch) {
          fallbackMappings.push({
            ...exactMatch,
            source: [header], // Use original header
          });
          continue;
        }

        // Try partial match if no exact match found
        const partialMatch = targetFields.find((f) =>
          f.source.some(
            (s) =>
              normalizedHeader.includes(s.toLowerCase()) ||
              s.toLowerCase().includes(normalizedHeader)
          )
        );

        if (partialMatch) {
          fallbackMappings.push({
            ...partialMatch,
            source: [header], // Use original header
          });
        }
      }

      return fallbackMappings;
    }
  }

  private static detectDataType(
    headers: string[]
  ): "pos" | "customer" | "product" | "review" | "retailer" | "coa" | null {
    const normalizedHeaders = headers.map((h) => h.toLowerCase().trim());

    console.log("Normalized headers for detection:", normalizedHeaders);

    // For debugging - check if each header exists in our known mappings
    for (const header of normalizedHeaders) {
      // Check POS fields
      const posMatch = POS_FIELD_MAPPINGS.some((mapping) =>
        mapping.source.some((s) => s.toLowerCase() === header)
      );
      if (posMatch) {
        console.log(`Header "${header}" matches a known POS field`);
      }
    }

    // Force detection for POS data with these specific header combinations
    const posCriticalFields = [
      "order date",
      "gross sales",
      "net sales",
      "invoice total",
    ];
    const posCriticalMatches = posCriticalFields.filter((field) =>
      normalizedHeaders.some((h) => h === field || h.includes(field))
    );

    if (posCriticalMatches.length >= 2) {
      console.log(
        `Forced POS detection based on critical fields: ${posCriticalMatches.join(
          ", "
        )}`
      );
      return "pos";
    }

    // Define key identifier fields for each type
    const keyIdentifiers = {
      pos: [
        "order date",
        "sales",
        "invoice",
        "budtender",
        "location",
        "customer type",
        "product name",
      ],
      customer: ["email", "phone", "name", "customer_id", "address", "birth"],
      product: ["sku", "product", "category", "brand", "price", "inventory"],
      review: [
        "review",
        "rating",
        "author",
        "helpful",
        "verified",
        "review_text",
      ],
      retailer: [
        "retailer",
        "dispensary",
        "store",
        "license",
        "medical",
        "recreational",
        "address",
      ],
      coa: ["product_name", "product_type", "batch_number", "lab_name"],
    };

    // First try to identify by key fields - log the matching process
    for (const [type, identifiers] of Object.entries(keyIdentifiers)) {
      const matchingFields = identifiers.filter((id) =>
        normalizedHeaders.some((h) => h.includes(id))
      );

      console.log(`Matching ${type} fields:`, matchingFields);

      // If we have at least 3 matching fields or 30% of the identifiers, consider it a match
      if (
        matchingFields.length >= 3 ||
        matchingFields.length / identifiers.length >= 0.3
      ) {
        console.log(
          `Type detected via identifiers: ${type} (${matchingFields.length} matches)`
        );
        return type as
          | "pos"
          | "customer"
          | "product"
          | "review"
          | "retailer"
          | "coa";
      }
    }

    // If no key fields found, fall back to percentage matching
    const posFieldMatches = POS_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    const customerMatches = CUSTOMER_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    const productMatches = PRODUCT_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    const reviewMatches = REVIEW_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    const retailerMatches = RETAILER_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    const coaMatches = COA_FIELD_MAPPINGS.filter((mapping) =>
      mapping.source.some((s) =>
        normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
      )
    );

    console.log(
      `POS matches: ${posFieldMatches.length}/${POS_FIELD_MAPPINGS.length}`
    );
    console.log(
      `Customer matches: ${customerMatches.length}/${CUSTOMER_FIELD_MAPPINGS.length}`
    );
    console.log(
      `Product matches: ${productMatches.length}/${PRODUCT_FIELD_MAPPINGS.length}`
    );
    console.log(
      `Review matches: ${reviewMatches.length}/${REVIEW_FIELD_MAPPINGS.length}`
    );
    console.log(
      `Retailer matches: ${retailerMatches.length}/${RETAILER_FIELD_MAPPINGS.length}`
    );
    console.log(
      `COA matches: ${coaMatches.length}/${COA_FIELD_MAPPINGS.length}`
    );

    // Calculate match percentages with weighted required fields
    const calculateScore = (
      matches: FieldMapping[],
      mappings: FieldMapping[]
    ) => {
      if (mappings.length === 0) return 0;

      // Add special high score for POS detection if we have several POS-specific fields
      if (
        mappings === POS_FIELD_MAPPINGS &&
        normalizedHeaders.some((h) => h.includes("order date")) &&
        normalizedHeaders.some((h) => h.includes("sales"))
      ) {
        console.log(
          "Adding bonus score for POS detection based on critical fields"
        );
        return 0.8; // Highly likely to be POS data
      }

      const requiredFields = mappings.filter((m) => m.required).length || 1; // Avoid division by zero
      const requiredMatches = mappings
        .filter((m) => m.required)
        .filter((m) =>
          m.source.some((s) =>
            normalizedHeaders.some((h) => h.includes(s.toLowerCase()))
          )
        ).length;

      const score =
        (matches.length / mappings.length) * 0.6 + // Overall match score (60% weight)
        (requiredMatches / requiredFields) * 0.4; // Required fields score (40% weight)

      return score;
    };

    const scores = [
      {
        type: "pos",
        score: calculateScore(posFieldMatches, POS_FIELD_MAPPINGS),
      },
      {
        type: "customer",
        score: calculateScore(customerMatches, CUSTOMER_FIELD_MAPPINGS),
      },
      {
        type: "product",
        score: calculateScore(productMatches, PRODUCT_FIELD_MAPPINGS),
      },
      {
        type: "review",
        score: calculateScore(reviewMatches, REVIEW_FIELD_MAPPINGS),
      },
      {
        type: "retailer",
        score: calculateScore(retailerMatches, RETAILER_FIELD_MAPPINGS),
      },
      {
        type: "coa",
        score: calculateScore(coaMatches, COA_FIELD_MAPPINGS),
      },
    ];

    // Log scores for debugging
    console.log("Type detection scores:", scores);

    // Lower the threshold to 0.2 to be more lenient
    const validScores = scores.filter((m) => m.score >= 0.2);

    if (validScores.length === 0) {
      console.log("No valid type detected with score >= 0.2");
      // Special case: if we have a very specific POS indicator
      if (
        normalizedHeaders.some(
          (h) =>
            h.includes("order date") ||
            h.includes("invoice") ||
            (h.includes("sales") &&
              normalizedHeaders.some((h2) => h2.includes("customer")))
        )
      ) {
        console.log("Detected as POS data based on specific headers pattern");
        return "pos";
      }
      return null;
    }

    // Return the type with highest score
    const result = validScores.reduce((a, b) => (a.score > b.score ? a : b))
      .type as "pos" | "customer" | "product" | "review" | "retailer" | "coa";

    console.log(`Type detected via score: ${result}`);
    return result;
  }

  static async normalizeData(stream: FileStream): Promise<NormalizedData> {
    try {
      console.log("Starting normalizeData process");
      const { headers, rows } = await this.parseCSV(stream);
      console.log(
        `CSV parsed successfully. Found ${headers.length} headers and ${rows.length} rows`
      );

      const dataType = this.detectDataType(headers);
      console.log(`Data type detection result: ${dataType || "null"}`);

      if (!dataType) {
        console.error("Failed to detect data type. Headers:", headers);
        throw new Error(
          "Could not determine data type from headers. Please ensure your file contains either POS data, customer data, product data, review data, or retailer data fields."
        );
      }

      // Generate AI mappings for the actual headers
      console.log("Generating AI mappings...");
      const aiMappings = await this.generateAIMappings(headers, dataType);
      console.log(
        `AI mappings generation complete. Generated ${aiMappings.length} mappings`
      );

      // Log mapping results for debugging
      console.log("Data type detected:", dataType);
      console.log("Headers found:", headers);
      console.log("AI mappings generated:", aiMappings);

      const mappings = [
        ...(dataType === "pos" ? POS_FIELD_MAPPINGS : []),
        ...(dataType === "customer" ? CUSTOMER_FIELD_MAPPINGS : []),
        ...(dataType === "product" ? PRODUCT_FIELD_MAPPINGS : []),
        ...(dataType === "review" ? REVIEW_FIELD_MAPPINGS : []),
        ...(dataType === "retailer" ? RETAILER_FIELD_MAPPINGS : []),
        ...(dataType === "coa" ? COA_FIELD_MAPPINGS : []),
        ...aiMappings, // Add AI-generated mappings
      ];

      const normalizedData: NormalizedData = {
        type: dataType,
        data: [],
        errors: [],
      };

      // Track unmapped fields for debugging
      const unmappedFields = new Set(headers);

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const normalizedRow: any = {};
        let hasRequiredFields = true;

        // Process each header in the row
        for (const header of headers) {
          const mapping = this.findMatchingField(header, mappings);
          if (mapping) {
            unmappedFields.delete(header);
            const value = this.transformValue(row[header], mapping);

            // Handle nested fields (e.g., data.first_name)
            if (mapping.target.includes(".")) {
              const [parent, child] = mapping.target.split(".");
              normalizedRow[parent] = normalizedRow[parent] || {};
              normalizedRow[parent][child] = value;
            } else {
              normalizedRow[mapping.target] = value;
            }

            // Check required fields
            if (mapping.required && !value) {
              hasRequiredFields = false;
              normalizedData.errors.push({
                row: i + 2, // Add 2 to account for 1-based indexing and header row
                error: `Missing required field: ${mapping.target}`,
              });
            }
          }
        }

        if (hasRequiredFields) {
          normalizedData.data.push(normalizedRow);
        }
      }

      // Log any unmapped fields for debugging
      if (unmappedFields.size > 0) {
        console.warn("Unmapped fields:", Array.from(unmappedFields));
      }

      return normalizedData;
    } catch (error) {
      console.error("Error in normalizeData:", error);
      throw error;
    }
  }

  /**
   * Extract reviews from product data
   * @param productData Product data which may contain review information
   * @returns Normalized review data
   */
  static extractReviewsFromProducts(productData: any): NormalizedData {
    console.log("Extracting reviews from products data");

    const normalizedData: NormalizedData = {
      type: "review",
      data: [],
      errors: [],
    };

    let productsArray: any[] = [];

    // Normalize the input data structure
    if (Array.isArray(productData)) {
      productsArray = productData;
    } else if (productData.products && Array.isArray(productData.products)) {
      productsArray = productData.products;
    } else if (typeof productData === "object" && productData !== null) {
      productsArray = [productData];
    } else {
      normalizedData.errors.push({
        row: 0,
        error: "Invalid product data format for review extraction",
      });
      return normalizedData;
    }

    // Process each product
    for (let i = 0; i < productsArray.length; i++) {
      try {
        const product = productsArray[i];

        // Handle nested products structure
        if (product.products && Array.isArray(product.products)) {
          for (const nestedProduct of product.products) {
            this.processProductForReviews(
              nestedProduct,
              product.meta_sku || nestedProduct.meta_sku,
              normalizedData
            );
          }
        } else {
          // Direct product structure
          this.processProductForReviews(
            product,
            product.meta_sku,
            normalizedData
          );
        }
      } catch (error) {
        console.error(
          `Error extracting reviews from product at index ${i}:`,
          error
        );
        normalizedData.errors.push({
          row: i + 1,
          error: `Failed to extract reviews: ${
            error instanceof Error ? error.message : String(error)
          }`,
        });
      }
    }

    console.log(
      `Extracted ${normalizedData.data.length} reviews from products`
    );
    return normalizedData;
  }

  /**
   * Process a single product to extract review data
   */
  private static processProductForReviews(
    product: any,
    productId: string,
    normalizedData: NormalizedData
  ): void {
    // Check for typical review data structures

    // 1. Check for review_summary field
    if (product.review_summary && typeof product.review_summary === "string") {
      // Create a single review from the summary
      const reviewData: Partial<ReviewParams> = {
        product_id: productId,
        retailer_id: product.retailer_id?.toString(),
        text: product.review_summary,
        rating: product.rating || 0,
        // Use the current date as a fallback
        review_date: new Date(),
        // Store additional metadata in data field
        data: {
          source: "review_summary",
          product_name: product.product_name || product.name,
        },
      };

      normalizedData.data.push(reviewData);
    }

    // 2. Check for reviews array
    if (product.reviews && Array.isArray(product.reviews)) {
      for (const review of product.reviews) {
        try {
          // Map review fields
          const reviewData: Partial<ReviewParams> = {
            product_id: productId,
            retailer_id: product.retailer_id?.toString(),
          };

          // Map common review fields
          for (const mapping of REVIEW_FIELD_MAPPINGS) {
            const key = mapping.target;
            const sourceKeys = mapping.source;

            // Find first matching source key
            const matchingKey = sourceKeys.find(
              (s) =>
                review[s] !== undefined ||
                (s.toLowerCase() === "product_id" && productId)
            );

            if (matchingKey) {
              let value =
                matchingKey.toLowerCase() === "product_id"
                  ? productId
                  : review[matchingKey];

              // Apply transformation if needed
              if (mapping.transform) {
                value = mapping.transform(value);
              } else if (mapping.type === "boolean") {
                value = !!value;
              } else if (
                typeof value === "object" &&
                value !== null &&
                !Array.isArray(value)
              ) {
                value = JSON.stringify(value);
              }

              if (key === "created_at") {
                // Map to our review_date field
                const formatDate = this.formatDate(value);
                // Only set if we have a valid date (non-null)
                if (formatDate !== null) {
                  reviewData.review_date = formatDate;
                }
              } else {
                reviewData[key as keyof ReviewParams] = value;
              }
            }
          }

          // Ensure required fields
          if (!reviewData.product_id || reviewData.rating === undefined) {
            normalizedData.errors.push({
              row: normalizedData.data.length + 1,
              error: `Review missing required fields: ${
                !reviewData.product_id ? "product_id" : "rating"
              }`,
            });
            continue;
          }

          normalizedData.data.push(reviewData);
        } catch (reviewError) {
          console.error("Error processing review:", reviewError);
          normalizedData.errors.push({
            row: normalizedData.data.length + 1,
            error: `Failed to process review: ${
              reviewError instanceof Error
                ? reviewError.message
                : String(reviewError)
            }`,
          });
        }
      }
    }

    // 3. Check for effects field which may contain informal reviews/feedback
    if (
      product.effects &&
      typeof product.effects === "object" &&
      product.effects !== null
    ) {
      // Create a review from effects data
      const effectsText =
        typeof product.effects === "string"
          ? product.effects
          : JSON.stringify(product.effects);

      const reviewData: Partial<ReviewParams> = {
        product_id: productId,
        retailer_id: product.retailer_id?.toString(),
        text: effectsText,
        // Default rating if not provided
        rating: product.rating || 3.5,
        review_date: new Date(),
        data: {
          source: "effects",
          product_name: product.product_name || product.name,
          effects: product.effects,
          mood: product.mood,
        },
      };

      normalizedData.data.push(reviewData);
    }
  }

  /**
   * Normalize retailer data from various formats
   * @param retailerData Retailer data to normalize
   * @returns Normalized retailer data
   */
  static normalizeRetailerData(retailerData: any): NormalizedData {
    console.log("Normalizing retailer data");

    const normalizedData: NormalizedData = {
      type: "retailer",
      data: [],
      errors: [],
    };

    // Handle various input formats
    let retailersArray: any[] = [];
    if (Array.isArray(retailerData)) {
      retailersArray = retailerData;
    } else if (typeof retailerData === "object" && retailerData !== null) {
      if (
        retailerData.dispensaries &&
        Array.isArray(retailerData.dispensaries)
      ) {
        retailersArray = retailerData.dispensaries;
      } else if (
        retailerData.retailers &&
        Array.isArray(retailerData.retailers)
      ) {
        retailersArray = retailerData.retailers;
      } else {
        // Single retailer object
        retailersArray = [retailerData];
      }
    } else {
      normalizedData.errors.push({
        row: 0,
        error: "Invalid retailer data format",
      });
      return normalizedData;
    }

    console.log(`Processing ${retailersArray.length} retailers`);

    // Process each retailer
    for (let i = 0; i < retailersArray.length; i++) {
      try {
        const retailer = retailersArray[i];
        const normalizedRetailer: Partial<RetailerParams> = {};

        // Map retailer fields
        for (const mapping of RETAILER_FIELD_MAPPINGS) {
          const key = mapping.target;
          const sourceKeys = mapping.source;

          // Find first matching source key
          const matchingKey = sourceKeys.find((s) => retailer[s] !== undefined);

          if (matchingKey) {
            let value = retailer[matchingKey];

            // Apply transformation if needed
            if (mapping.transform) {
              value = mapping.transform(value);
            } else if (mapping.type === "boolean") {
              value = !!value;
            } else if (
              typeof value === "object" &&
              value !== null &&
              !Array.isArray(value)
            ) {
              value = JSON.stringify(value);
            }

            normalizedRetailer[key as keyof RetailerParams] = value;
          }
        }

        // Special handling for complex objects that might need transformation
        if (retailer.avatarImage && typeof retailer.avatarImage === "object") {
          normalizedRetailer.avatar = JSON.stringify(retailer.avatarImage);
        }

        if (
          retailer.onlineOrdering &&
          typeof retailer.onlineOrdering === "object"
        ) {
          normalizedRetailer.online_ordering = JSON.stringify(
            retailer.onlineOrdering
          );
        }

        // Ensure required fields
        if (!normalizedRetailer.retailer_id || !normalizedRetailer.name) {
          const missingFields = [];
          if (!normalizedRetailer.retailer_id) {
            missingFields.push("retailer_id");
          }
          if (!normalizedRetailer.name) missingFields.push("name");

          // If retailer_id is missing but we have id, use that
          if (!normalizedRetailer.retailer_id && retailer.id) {
            normalizedRetailer.retailer_id = retailer.id.toString();
            missingFields.splice(missingFields.indexOf("retailer_id"), 1);
          }

          // If name is missing but we have dispensary_name or store_name, use that
          if (!normalizedRetailer.name) {
            const nameFallback =
              retailer.dispensary_name || retailer.store_name;
            if (nameFallback) {
              normalizedRetailer.name = nameFallback;
              missingFields.splice(missingFields.indexOf("name"), 1);
            }
          }

          // If we still have missing required fields, log error and skip
          if (missingFields.length > 0) {
            normalizedData.errors.push({
              row: i + 1,
              error: `Retailer missing required fields: ${missingFields.join(
                ", "
              )}`,
            });
            continue;
          }
        }

        // Store any additional data in the data field
        const knownFields = new Set(
          RETAILER_FIELD_MAPPINGS.flatMap((m) => m.source)
        );
        const additionalData: Record<string, any> = {};

        for (const [key, value] of Object.entries(retailer)) {
          if (!knownFields.has(key)) {
            additionalData[key] = value;
          }
        }

        if (Object.keys(additionalData).length > 0) {
          normalizedRetailer.data = additionalData;
        }

        normalizedData.data.push(normalizedRetailer);
      } catch (error) {
        console.error(`Error normalizing retailer at index ${i}:`, error);
        normalizedData.errors.push({
          row: i + 1,
          error: `Failed to normalize retailer: ${
            error instanceof Error ? error.message : String(error)
          }`,
        });
      }
    }

    console.log(`Normalized ${normalizedData.data.length} retailers`);
    return normalizedData;
  }
}
