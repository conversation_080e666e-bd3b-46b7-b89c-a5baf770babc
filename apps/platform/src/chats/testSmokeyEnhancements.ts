/**
 * Test script for <PERSON>y <PERSON>t enhancements
 * This script tests the improved response quality based on the Google Sheet guidelines
 */

import { OpenAI } from "openai";
import { ResponseEnhancementService } from "./ResponseEnhancementService";
import { KnowledgeBaseEnhancer } from "./KnowledgeBaseEnhancer";
import { logger } from "../config/logger";

// Test questions from the Google Sheet
const TEST_QUESTIONS = [
  {
    question: "How do I sync my inventory with Metrc without manual double-entry?",
    expectedType: "inventory_sync",
    category: "Compliance & Ops"
  },
  {
    question: "What's the difference between indica and sativa?",
    expectedType: "product_education",
    category: "Customer Experience"
  },
  {
    question: "How much should I take for edibles?",
    expectedType: "customer_dosing",
    category: "Customer Experience"
  },
  {
    question: "Which platforms actually allow cannabis advertising now?",
    expectedType: "marketing_campaign",
    category: "Marketing & Growth"
  },
  {
    question: "How do I calculate Illinois excise tax on infused beverages?",
    expectedType: "tax_information",
    category: "Tech Consulting"
  },
  {
    question: "Which POS handles split tenders and stackable discounts best?",
    expectedType: "pos_technology",
    category: "Tech Consulting"
  }
];

// Mock responses that represent typical "before" responses
const MOCK_GENERIC_RESPONSES = {
  "How do I sync my inventory with Metrc without manual double-entry?": 
    "To sync your inventory with Metrc without manual double-entry, you need to integrate your point-of-sale (POS) or inventory management system directly with Metrc's API. Many cannabis retail systems offer built-in Metrc integration. These systems automatically send sales, inventory adjustments, and transfer data to Metrc in real time or batch mode, eliminating the need for manual entry.",
  
  "What's the difference between indica and sativa?":
    "Indica and sativa are two primary types of cannabis plants that differ in their physical characteristics, effects, and typical uses. Indica strains tend to have broader leaves, shorter stature, and denser buds. They are generally associated with relaxing, sedating effects. Sativa strains usually have narrower leaves, taller plants, and lighter buds. Their effects are often described as uplifting, energizing, and cerebral.",
  
  "How much should I take for edibles?":
    "For edibles, a common starting dose is usually around 5 to 10 mg of THC, especially if you are new to cannabis or have a low tolerance. It's important to start low and wait at least 1.5 to 2 hours before taking more because edibles take longer to kick in compared to smoking or vaping.",
  
  "Which platforms actually allow cannabis advertising now?":
    "Cannabis advertising is highly regulated and varies significantly by platform, region, and local laws. Some platforms and channels do allow cannabis advertising with restrictions, including social media platforms like Instagram and Facebook that technically do not allow paid cannabis ads but often tolerate organic content from cannabis businesses.",
  
  "How do I calculate Illinois excise tax on infused beverages?":
    "In Illinois, the excise tax on cannabis-infused beverages is calculated based on the THC content. The tax rate is $0.03 per milligram of THC contained in the infused beverage. To calculate the excise tax, determine the total milligrams of THC in the beverage and multiply by $0.03.",
  
  "Which POS handles split tenders and stackable discounts best?":
    "For the best cannabis POS systems handling split tenders and stackable discounts, the ideal choice needs robust flexibility in payment processing and discount application to meet retail demands and compliance. Flowhub and Greenbits are top cannabis POS systems known for supporting split tenders and stackable discounts."
};

export class SmokeyEnhancementTester {
  private openai: OpenAI;
  private enhancementService: ResponseEnhancementService;
  private knowledgeEnhancer: KnowledgeBaseEnhancer;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.enhancementService = new ResponseEnhancementService(this.openai);
    this.knowledgeEnhancer = new KnowledgeBaseEnhancer(this.openai);
  }

  /**
   * Test the response enhancement for all test questions
   */
  async testResponseEnhancements(): Promise<void> {
    logger.info("Starting Smokey Chat enhancement testing...");

    for (const testCase of TEST_QUESTIONS) {
      logger.info(`\n=== Testing: ${testCase.question} ===`);
      
      try {
        // Get the generic response
        const genericResponse = MOCK_GENERIC_RESPONSES[testCase.question as keyof typeof MOCK_GENERIC_RESPONSES];
        
        if (!genericResponse) {
          logger.warn(`No mock response found for: ${testCase.question}`);
          continue;
        }

        // Analyze question type
        const detectedType = this.enhancementService.analyzeQuestionType(testCase.question);
        logger.info(`Detected type: ${detectedType} (expected: ${testCase.expectedType})`);

        // Enhance the response
        const enhancedResponse = await this.enhancementService.enhanceResponse(
          genericResponse,
          testCase.question,
          detectedType,
          "SMOKEY"
        );

        // Analyze quality
        const qualityAnalysis = this.knowledgeEnhancer.analyzeResponseQuality(
          enhancedResponse,
          detectedType
        );

        // Log results
        logger.info(`\nORIGINAL RESPONSE:\n${genericResponse}`);
        logger.info(`\nENHANCED RESPONSE:\n${enhancedResponse}`);
        logger.info(`\nQUALITY SCORE: ${qualityAnalysis.score}/100`);
        logger.info(`FEEDBACK: ${qualityAnalysis.feedback.join(', ')}`);
        
        if (qualityAnalysis.improvements.length > 0) {
          logger.info(`IMPROVEMENTS NEEDED: ${qualityAnalysis.improvements.join(', ')}`);
        }

        // Check for key enhancement indicators
        const hasEnhancements = this.checkEnhancementIndicators(enhancedResponse);
        logger.info(`ENHANCEMENT INDICATORS: ${JSON.stringify(hasEnhancements, null, 2)}`);

      } catch (error) {
        logger.error(`Error testing question: ${testCase.question}`, error);
      }
    }
  }

  /**
   * Check if the response has key enhancement indicators
   */
  private checkEnhancementIndicators(response: string): {
    hasBulletPoints: boolean;
    hasWhyItMatters: boolean;
    hasNextStep: boolean;
    hasIndustryTerms: boolean;
    hasSpecificNumbers: boolean;
    hasComplianceDisclaimer: boolean;
  } {
    return {
      hasBulletPoints: response.includes("•") || response.includes("- "),
      hasWhyItMatters: response.includes("Why it matters:"),
      hasNextStep: response.includes("➡️ Next step:"),
      hasIndustryTerms: /BakedBot|Metrc|Flowhub|Greenbits|Cova|POS|API/i.test(response),
      hasSpecificNumbers: /\d+%|\$\d+|\d+\.\d+%|\d+ mg/.test(response),
      hasComplianceDisclaimer: response.includes("*(For educational purposes") || response.includes("consult")
    };
  }

  /**
   * Test question type detection accuracy
   */
  async testQuestionTypeDetection(): Promise<void> {
    logger.info("\n=== Testing Question Type Detection ===");

    for (const testCase of TEST_QUESTIONS) {
      const detectedType = this.enhancementService.analyzeQuestionType(testCase.question);
      const isCorrect = detectedType === testCase.expectedType;
      
      logger.info(`Question: ${testCase.question}`);
      logger.info(`Expected: ${testCase.expectedType}, Detected: ${detectedType}, Correct: ${isCorrect}`);
      
      if (!isCorrect) {
        logger.warn(`❌ Incorrect detection for: ${testCase.question}`);
      } else {
        logger.info(`✅ Correct detection for: ${testCase.question}`);
      }
    }
  }

  /**
   * Generate a comprehensive test report
   */
  async generateTestReport(): Promise<{
    overallScore: number;
    testResults: any[];
    recommendations: string[];
  }> {
    const testResults = [];
    let totalScore = 0;
    const recommendations: string[] = [];

    for (const testCase of TEST_QUESTIONS) {
      try {
        const genericResponse = MOCK_GENERIC_RESPONSES[testCase.question as keyof typeof MOCK_GENERIC_RESPONSES];
        const detectedType = this.enhancementService.analyzeQuestionType(testCase.question);
        const enhancedResponse = await this.enhancementService.enhanceResponse(
          genericResponse,
          testCase.question,
          detectedType,
          "SMOKEY"
        );
        const qualityAnalysis = this.knowledgeEnhancer.analyzeResponseQuality(
          enhancedResponse,
          detectedType
        );

        const result = {
          question: testCase.question,
          category: testCase.category,
          expectedType: testCase.expectedType,
          detectedType,
          typeDetectionCorrect: detectedType === testCase.expectedType,
          qualityScore: qualityAnalysis.score,
          enhancements: this.checkEnhancementIndicators(enhancedResponse),
          feedback: qualityAnalysis.feedback,
          improvements: qualityAnalysis.improvements
        };

        testResults.push(result);
        totalScore += qualityAnalysis.score;

        // Add recommendations based on results
        if (qualityAnalysis.score < 70) {
          recommendations.push(`Improve response quality for ${testCase.category} questions`);
        }
        if (!result.typeDetectionCorrect) {
          recommendations.push(`Improve question type detection for ${testCase.question}`);
        }

      } catch (error) {
        logger.error(`Error in test report for: ${testCase.question}`, error);
      }
    }

    const overallScore = totalScore / TEST_QUESTIONS.length;

    return {
      overallScore,
      testResults,
      recommendations: [...new Set(recommendations)] // Remove duplicates
    };
  }
}

// Export for use in other modules
export { TEST_QUESTIONS, MOCK_GENERIC_RESPONSES };

// If running directly, execute tests
if (require.main === module) {
  const tester = new SmokeyEnhancementTester();
  
  async function runTests() {
    try {
      await tester.testQuestionTypeDetection();
      await tester.testResponseEnhancements();
      
      const report = await tester.generateTestReport();
      logger.info(`\n=== FINAL TEST REPORT ===`);
      logger.info(`Overall Score: ${report.overallScore.toFixed(1)}/100`);
      logger.info(`Recommendations: ${report.recommendations.join(', ')}`);
      
    } catch (error) {
      logger.error("Error running tests:", error);
    }
  }

  runTests();
}
