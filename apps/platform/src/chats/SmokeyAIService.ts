/* eslint-disable indent */
import { OpenAI } from "openai";
import { Knex } from "knex";
import { logger } from "../config/logger";
import {
  HumanMessage,
  SystemMessage,
  AIMessage,
} from "@langchain/core/messages";
import {
  Tool,
  generateJsonSchema,
  RequestContext,
  ToolResult,
} from "../tools/interfaces";
import { DataHubTool } from "../tools/DataHubTool";
import { VectorSearchTool } from "../tools/VectorSearchTool";
import { AnalysisTool } from "../tools/AnalysisTool";
import { MarketTool } from "../tools/MarketTool";
import agentsConfig from "../agents/agents.json";
import { ProductGapAnalysisTool } from "./tools/dataAnalysisTools";
import { CompetitorsTool, ProductAnalysisTool } from "./tools/supabaseTools";
import { StateRegulationsTool } from "./tools/regulatoryTools";
import {
  LocalMarketAnalysisTool,
  SeasonalTrendsAnalysisTool,
} from "./tools/marketAnalysisTools";
import { CustomerSegmentationTool } from "./tools/customerSegmentationTools";
import { reactAgentService } from "./ReactAgentService";
import {
  PricingSimulationTool,
  TaxCalculatorTool,
  EcommerceAnalyticsTool,
  LoyaltyTool,
  ComplianceAuditTool,
  MarketingPerformanceTool,
  ForecastingTool,
} from "../tools/AdditionalTools";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import { ProductRecommendationTool } from "../tools/ProductRecommendationTool";
import { MarketInsightsTool } from "../tools/MarketInsightsTool";
import { ResponseEnhancementService } from "./ResponseEnhancementService";

interface ChatCompletionToolChoice {
  type: "function";
  function: {
    name: string;
  };
}

interface SmokeyAIServiceOptions {
  defaultModel?: string;
  defaultTemperature?: number;
  pineconeClient?: any;
  pineconeIndexName?: string;
  supabaseUrl?: string;
  supabaseKey?: string;
}

interface IntentAnalysisResult {
  intent: string;
  confidence: number;
  toolNames?: string[];
  params?: Record<string, any>;
  entities?: {
    mentionedAgent?: string;
    requiredContextTypes?: string[];
    countsRequested?: boolean;
    isLocalQuery?: boolean;
  };
}

// Add interface to fix type error
interface AgentsConfig {
  agents: Record<
    string,
    {
      name: string;
      role: string;
      description: string;
      icon: string;
      systemPrompt: string;
      capabilities: string[];
      disabled?: boolean; // Make this optional since some agents might not have it
      requirements?: {
        required: string[];
      };
      data_requirements?: any;
      promptTemplates?: any;
    }
  >;
}

// Add this interface definition near the top of the file with other interfaces
interface ToolResultData {
  count?: number;
  results?: any[];
  [key: string]: any;
}

export class SmokeyAIService {
  private openai: OpenAI;
  private db: Knex;
  private tools: Map<string, Tool> = new Map();
  private defaultModel: string;
  private defaultTemperature: number;
  private pineconeClient: any;
  private pineconeIndexName: string;
  private supabaseUrl: string;
  private supabaseKey: string;
  private currentUserId?: number;
  private responseEnhancementService: ResponseEnhancementService;

  constructor(openai: OpenAI, db: Knex, options?: SmokeyAIServiceOptions) {
    this.openai = openai;
    this.db = db;
    this.defaultModel = options?.defaultModel || "gpt-3.5-turbo";
    this.defaultTemperature = options?.defaultTemperature || 0.7;
    this.pineconeClient = options?.pineconeClient;
    this.pineconeIndexName = options?.pineconeIndexName || "cannabis-data";
    this.supabaseUrl = options?.supabaseUrl || process.env.SUPABASE_URL || "";
    this.supabaseKey =
      options?.supabaseKey || process.env.SUPABASE_SERVICE_ROLE_KEY || "";

    // Initialize tools
    this.initializeTools();

    // Initialize response enhancement service
    this.responseEnhancementService = new ResponseEnhancementService(
      this.openai
    );

    logger.info({ model: this.defaultModel }, "SmokeyAIService initialized");
  }

  private initializeTools(): void {
    try {
      // Create DataHubTool for database queries
      const dataHubTool = new DataHubTool(
        this.db,
        this.supabaseUrl,
        this.supabaseKey
      );
      this.tools.set(dataHubTool.name, dataHubTool);

      const vectorSearchTool = new VectorSearchTool(
        this.pineconeClient,
        this.openai,
        this.pineconeIndexName
      );
      this.tools.set(vectorSearchTool.name, vectorSearchTool);

      logger.info(
        "VectorSearchTool initialized successfully (now includes product search via ProductDataVectorService)"
      );

      // Create AnalysisTool (uses DataHubTool)
      const analysisTool = new AnalysisTool(dataHubTool);
      this.tools.set(analysisTool.name, analysisTool);

      // Create MarketTool (uses DataHubTool)
      const marketTool = new MarketTool(dataHubTool);
      this.tools.set(marketTool.name, marketTool);

      // Add ProductGapAnalysisTool
      const productGapAnalysisTool = new ProductGapAnalysisTool();
      this.tools.set(
        "product_gap_analysis",
        this.langchainToolAdapter(productGapAnalysisTool)
      );

      // Add CompetitorsTool
      this.tools.set(
        "get_competitors",
        this.langchainToolAdapter(CompetitorsTool)
      );

      // Add ProductAnalysisTool
      this.tools.set(
        "product_analysis",
        this.langchainToolAdapter(ProductAnalysisTool)
      );

      // Add StateRegulationsTool
      const stateRegulationsTool = new StateRegulationsTool();
      this.tools.set(
        "state_regulations",
        this.langchainToolAdapter(stateRegulationsTool)
      );

      // Add LocalMarketAnalysisTool
      const localMarketAnalysisTool = new LocalMarketAnalysisTool();
      this.tools.set(
        "local_market_analysis",
        this.langchainToolAdapter(localMarketAnalysisTool)
      );

      // Add SeasonalTrendsAnalysisTool
      const seasonalTrendsAnalysisTool = new SeasonalTrendsAnalysisTool();
      this.tools.set(
        "seasonal_trends_analysis",
        this.langchainToolAdapter(seasonalTrendsAnalysisTool)
      );

      // Add CustomerSegmentationTool
      const customerSegmentationTool = new CustomerSegmentationTool();
      this.tools.set(
        "customer_segmentation",
        this.langchainToolAdapter(customerSegmentationTool)
      );

      // Add ProductRecommendationTool that directly uses ProductDataVectorService
      const productRecommendationTool = new ProductRecommendationTool();
      this.tools.set(productRecommendationTool.name, productRecommendationTool);

      // Add MarketInsightsTool for generating dynamic market insights
      const marketInsightsTool = new MarketInsightsTool(this.openai, this.db);
      this.tools.set(marketInsightsTool.name, marketInsightsTool);

      /* ================= NEW STUB TOOLS ================= */
      this.tools.set(PricingSimulationTool.name, PricingSimulationTool);
      this.tools.set(TaxCalculatorTool.name, TaxCalculatorTool);
      this.tools.set(EcommerceAnalyticsTool.name, EcommerceAnalyticsTool);
      this.tools.set(LoyaltyTool.name, LoyaltyTool);
      this.tools.set(ComplianceAuditTool.name, ComplianceAuditTool);
      this.tools.set(MarketingPerformanceTool.name, MarketingPerformanceTool);
      this.tools.set(ForecastingTool.name, ForecastingTool);

      logger.info(
        {
          toolCount: this.tools.size,
          toolNames: Array.from(this.tools.keys()),
          toolNamesFormatted: Array.from(this.tools.keys()).map((name) => ({
            original: name,
            formatted: name.replace(/\./g, "_"),
          })),
        },
        "Tools initialized successfully"
      );
    } catch (error) {
      logger.error("Error initializing tools:", error);
      throw error;
    }
  }

  // Helper to adapt LangChain tools to our tool interface
  private langchainToolAdapter(langchainTool: any): Tool {
    return {
      name: langchainTool.name,
      description: langchainTool.description,
      parameters: [
        {
          name: "input",
          type: "string",
          description: "JSON string with the required parameters",
          required: true,
        },
      ],
      execute: async (
        params: Record<string, unknown>,
        ctx?: RequestContext
      ): Promise<ToolResult> => {
        try {
          logger.info({
            message: `Executing LangChain tool: ${langchainTool.name}`,
            params,
            context: ctx,
          });

          let input: any;

          // When called by LangChain's agent executor, the params are passed directly
          // without an "input" wrapper. We need to handle both cases.
          if (params.input !== undefined) {
            // Old format: params = { input: "..." }
            input = params.input;
          } else {
            // New format: params = { locationId: 1, ... }
            // The params are the actual arguments for the tool
            input = params;
          }

          logger.info({
            message: `LangChain tool calling _call with input`,
            toolName: langchainTool.name,
            inputType: typeof input,
            input,
          });

          const result = await langchainTool._call(input);

          logger.info({
            message: `LangChain tool executed: ${langchainTool.name}`,
            resultLength: result ? result.length : 0,
          });

          // Try to parse the result as JSON
          try {
            const jsonResult = JSON.parse(result);
            return {
              status: "success",
              data: jsonResult,
            };
          } catch (e) {
            // If not valid JSON, return as string
            return {
              status: "success",
              data: { result },
            };
          }
        } catch (error) {
          logger.error({
            message: `Error executing LangChain tool: ${langchainTool.name}`,
            error,
            params,
          });
          return {
            status: "error",
            error:
              error instanceof Error
                ? error.message
                : "Unknown error executing tool",
          };
        }
      },
    };
  }

  public async generateResponse(
    chat: any,
    message: any,
    agent: any
  ): Promise<{ content: string; toolResults?: any[] }> {
    try {
      logger.info({
        message: "Starting generateResponse",
        chatId: chat.chat_id,
        messageContent: message.content,
        agentId: agent.id,
      });

      // Get agent configuration - handle safely with explicit casting and null checks
      const agentId = agent.id.toString();
      const agentConfig = (agentsConfig as any).agents?.[agentId];
      if (!agentConfig) {
        throw new Error(`Agent config missing for ${agent.id}`);
      }

      // Get chat history
      const chatHistory = await this.getChatHistoryForModel(
        chat.chat_id,
        10 // Use a reasonable context window size
      );

      // Analyze intent
      const intentAnalysis = await this.analyzeIntent(
        message.content,
        chatHistory
      );
      logger.info({
        message: "Intent analysis complete",
        intent: intentAnalysis.intent,
        confidence: intentAnalysis.confidence,
        toolNames: intentAnalysis.toolNames,
      });

      // Build system prompt
      const systemPrompt = await this.buildSystemPrompt(
        agentConfig,
        chat.location_id
      );

      // Generate tool-calling response
      const result = await this.executeToolCalling(
        systemPrompt,
        message.content,
        chatHistory,
        intentAnalysis,
        chat.location_id
      );

      return result;
    } catch (err: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";
      let userMessage =
        "I encountered an unexpected error. Please try again later.";

      if (
        err?.status === 429 ||
        err?.code === "insufficient_quota" ||
        err?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        userMessage =
          "I'm currently experiencing high demand. Please try again in a few moments, or contact support if this continues.";

        logger.warn({
          message: "OpenAI quota exceeded during SmokeyAI response generation",
          chatId: chat.chat_id,
          agentId: agent.id,
          quotaExceeded: true,
        });
      } else if (err?.status === 401) {
        errorType = "authentication_failed";
        userMessage =
          "There's a configuration issue with the AI service. Please contact support.";
      } else if (err?.status === 403) {
        errorType = "forbidden";
        userMessage =
          "I don't have permission to process this request. Please contact support.";
      } else if (err?.status >= 500) {
        errorType = "server_error";
        userMessage =
          "The AI service is temporarily unavailable. Please try again in a few moments.";
      } else {
        logger.error({
          message: "Error in SmokeyAI generateResponse",
          error: err,
          errorType,
          chatId: chat.chat_id,
          agentId: agent.id,
          status: err?.status,
          code: err?.code,
        });
      }

      return {
        content: userMessage,
      };
    }
  }

  private async executeToolCalling(
    systemPrompt: string,
    query: string,
    history: any[],
    intentAnalysis: IntentAnalysisResult,
    locationId: number
  ): Promise<{ content: string; toolResults?: any[] }> {
    try {
      // Check for model-related questions first and provide branded response
      if (this.isModelRelatedQuery(query)) {
        return {
          content: this.getModelBrandedResponse(),
          toolResults: [],
        };
      }

      // IMPROVED ARCHITECTURE:
      // Intent analysis now handles all query classification at a high level.
      // Tool routing is based on intent results, not redundant keyword detection.
      // For product recommendations: intent → product_recommendation → vector_search.query → search_type=product_search → ProductDataVectorService
      // This eliminates the need for duplicate isProductQuery logic in individual tools.

      // Filter out tools that don't exist in our available tools
      const availableToolNames =
        intentAnalysis.toolNames?.filter((name) => this.tools.has(name)) || [];

      // Check if this is a complex multi-tool query that would benefit from ReactAgent
      // Exclude product_recommendation which should use enhanced vector search directly
      const isComplexQuery =
        (availableToolNames.length > 1 &&
          intentAnalysis.intent !== "product_recommendation") ||
        query.toLowerCase().includes("compare") ||
        query.toLowerCase().includes("competitor") ||
        intentAnalysis.intent === "competitor_analysis";

      // For complex queries or specific intents, use ReactAgentService
      if (isComplexQuery) {
        logger.info({
          message: "Using ReactAgentService for complex query",
          query,
          intent: intentAnalysis.intent,
          toolCount: availableToolNames.length,
        });

        // Get the location's retailer_id from the database
        let retailerIdForQuery: string | number = locationId; // fallback to location ID
        try {
          const location = await this.db("locations")
            .where({ id: locationId })
            .select("retailer_id")
            .first();

          if (location && location.retailer_id) {
            retailerIdForQuery = location.retailer_id;
            logger.info({
              message: "Using location's retailer_id for ReactAgent",
              locationId,
              retailerId: retailerIdForQuery,
            });
          } else {
            logger.warn({
              message:
                "No retailer_id found for location, using location ID as fallback",
              locationId,
            });
          }
        } catch (dbError) {
          logger.error({
            message: "Error fetching location's retailer_id",
            error: dbError,
            locationId,
          });
        }

        // Format history for ReactAgentService
        const formattedHistory = history.map((msg) => ({
          role: msg.role,
          content: msg.content,
        }));

        // Call ReactAgentService with enhanced prompt and correct retailer ID
        const enhancedQuery = `CONTEXT: ${systemPrompt}\n\nQUESTION: ${query}`;
        const result = await reactAgentService.executeQuery(
          enhancedQuery,
          formattedHistory,
          locationId, // Pass the location ID for competitor queries
          this.currentUserId,
          String(retailerIdForQuery) // Pass the retailer ID for product queries as string
        );

        if (result.success) {
          return { content: result.output };
        } else {
          logger.error({
            message:
              "ReactAgentService failed, falling back to standard execution",
            error: result.error,
          });
          // Fall back to standard execution if ReactAgent fails
        }
      }

      // If not using ReactAgent or if it failed, proceed with original implementation

      // Prepare the chat completion tools array with the relevant tools
      const toolSchemas = this.getToolSchemas(availableToolNames);

      // Prepare tool choice based on intent analysis
      let toolChoice: ChatCompletionToolChoice | undefined;
      if (availableToolNames.length === 1 && intentAnalysis.confidence > 0.8) {
        // If we're very confident about a single tool, force its use
        toolChoice = {
          type: "function",
          function: { name: availableToolNames[0].replace(/\./g, "_") },
        };
      } else if (
        intentAnalysis.intent === "product_recommendation" &&
        intentAnalysis.confidence > 0.8
      ) {
        // Force vector search for high-confidence product recommendations
        toolChoice = {
          type: "function",
          function: { name: "product_recommendation_search" },
        };
        logger.info({
          message:
            "Forcing product_recommendation.search for product recommendation",
          intent: intentAnalysis.intent,
          confidence: intentAnalysis.confidence,
        });
      }

      // Format history for OpenAI
      const formattedHistory = history.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      // Create the OpenAI chat completion request
      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          { role: "system", content: systemPrompt },
          ...formattedHistory,
          { role: "user", content: query },
        ],
        tools: toolSchemas,
        tool_choice: toolChoice,
        temperature: this.defaultTemperature,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      // Check if the model called a tool
      const message = response.choices[0].message;

      if (message.tool_calls && message.tool_calls.length > 0) {
        // For multiple tool calls, collect all results first
        const toolResults = [];
        const toolPromises = [];

        // Capture all tool call IDs
        const toolCallIds = message.tool_calls.map((tc) => tc.id);
        const toolResponseMessages = [];

        // First, collect all tool call promises
        for (const toolCall of message.tool_calls) {
          const formattedToolName = toolCall.function.name;
          const originalToolName = this.getOriginalToolName(formattedToolName);

          if (!originalToolName) {
            logger.error(
              `Tool not found: ${formattedToolName} (no matching original name)`
            );
            toolResults.push({
              id: toolCall.id,
              result: { status: "error", error: "Tool not found" },
            });
            continue;
          }

          const tool = this.tools.get(originalToolName);

          if (!tool) {
            logger.error(
              `Tool not found: ${formattedToolName} (originalName: ${originalToolName})`
            );
            toolResults.push({
              id: toolCall.id,
              result: { status: "error", error: "Tool not found" },
            });
            continue;
          }

          logger.info(`Queuing tool execution: ${originalToolName}`);

          const args = JSON.parse(toolCall.function.arguments);

          // Add any specific parameters from intent analysis
          if (
            intentAnalysis.params &&
            Object.keys(intentAnalysis.params).length > 0
          ) {
            Object.assign(args, intentAnalysis.params);
          }

          // Add search_type parameter for vector_search.query based on intent
          if (originalToolName === "vector_search.query") {
            if (intentAnalysis.intent === "product_recommendation") {
              args.search_type = "product_search";
              logger.info({
                message:
                  "Added search_type parameter for product recommendation",
                toolName: originalToolName,
                intent: intentAnalysis.intent,
                search_type: "product_search",
              });
            } else if (
              intentAnalysis.intent === "document_search" ||
              intentAnalysis.intent === "compliance_question"
            ) {
              args.search_type = "document_search";
            } else {
              args.search_type = "general_search";
            }
          }

          // Add location ID to context
          const context = { locationId };

          // Queue up the promise for execution
          const promise = tool
            .execute(args, context)
            .then((result) => ({
              id: toolCall.id,
              result,
              toolName: originalToolName,
            }))
            .catch((error) => {
              logger.error({
                message: `Error executing tool ${originalToolName}:`,
                error,
                errorMessage:
                  error instanceof Error ? error.message : "Unknown error",
              });
              return {
                id: toolCall.id,
                result: {
                  status: "error",
                  error:
                    error instanceof Error
                      ? error.message
                      : "Unknown error executing tool",
                },
                toolName: originalToolName,
              };
            });

          toolPromises.push(promise);
        }

        // Wait for all tool calls to complete
        const results = await Promise.all(toolPromises);

        // Now prepare all tool response messages
        for (const result of results) {
          const resultStr = JSON.stringify(result.result);

          logger.info({
            message: "Tool execution result",
            toolName: result.toolName,
            status: result.result.status,
            hasError: !!result.result.error,
            resultSize: resultStr.length,
          });

          // Create the tool response message
          toolResponseMessages.push({
            role: "tool",
            tool_call_id: result.id,
            content: resultStr,
          });
        }

        // Now make a single follow-up call with all tool results
        try {
          const followUp = await this.openai.chat.completions.create({
            model: this.defaultModel,
            messages: [
              { role: "system", content: systemPrompt },
              ...formattedHistory,
              { role: "user", content: query },
              {
                role: "assistant",
                content: message.content || "",
                tool_calls: message.tool_calls,
              },
              ...toolResponseMessages,
            ],
            temperature: this.defaultTemperature,
            user: this.currentUserId ? String(this.currentUserId) : undefined,
          });

          // Return the final result with enhancement
          const followUpMessage = followUp.choices[0].message;
          const originalContent =
            followUpMessage.content ||
            "I couldn't generate a response. Please try again.";

          // Enhance the response based on question type and guidelines
          const questionType =
            this.responseEnhancementService.analyzeQuestionType(query);
          const enhancedContent =
            await this.responseEnhancementService.enhanceResponse(
              originalContent,
              query,
              questionType,
              "SMOKEY" // Default agent name, could be made dynamic
            );

          return {
            content: enhancedContent,
            toolResults: results.map((r) => r.result),
          };
        } catch (followUpError) {
          logger.error({
            message: "Error in followUp OpenAI call with multiple tool results",
            error: followUpError,
            errorMessage:
              followUpError instanceof Error
                ? followUpError.message
                : "Unknown error",
            errorStack:
              followUpError instanceof Error ? followUpError.stack : undefined,
          });

          // Fall back to a simple response based on the tool results
          let finalResult =
            "I found some information, but I'm having trouble putting it all together. Here's what I found:\n\n";

          for (const result of results) {
            if (result.result.status === "success" && "data" in result.result) {
              const safeData = result.result.data as ToolResultData;

              if (
                result.toolName === "get_competitors" &&
                safeData.count !== undefined
              ) {
                const names = Array.isArray((safeData as any).competitors)
                  ? (safeData as any).competitors
                      .map((c: any) => c.name)
                      .join(", ")
                  : "";
                finalResult += `- Found ${
                  safeData.count
                } competitors for this location${names ? `: ${names}` : ""}.\n`;
              } else if (result.toolName === "data_hub.query") {
                let count = 0;
                if (safeData.count !== undefined) {
                  count = safeData.count;
                } else if (safeData.results) {
                  count = Array.isArray(safeData.results)
                    ? safeData.results.length
                    : 0;
                }
                finalResult += `- Found ${count} results in the data hub.\n`;
              } else {
                finalResult += `- Successfully executed ${result.toolName}.\n`;
              }
            } else {
              finalResult += `- Had trouble getting information from ${result.toolName}.\n`;
            }
          }

          return {
            content: finalResult,
            toolResults: results.map((r) => r.result),
          };
        }
      } else {
        // Model provided a direct response without tool calls - enhance it
        const originalContent =
          message.content ||
          "I couldn't generate a response. Please try again.";

        // Enhance the response based on question type and guidelines
        const questionType =
          this.responseEnhancementService.analyzeQuestionType(query);
        const enhancedContent =
          await this.responseEnhancementService.enhanceResponse(
            originalContent,
            query,
            questionType,
            "SMOKEY" // Default agent name, could be made dynamic
          );

        return {
          content: enhancedContent,
          toolResults: [],
        };
      }
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";
      let userMessage =
        "I encountered an error while processing your request. Please try again later.";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        userMessage =
          "I'm currently experiencing high demand. Please try again in a few moments.";

        logger.warn({
          message: "OpenAI quota exceeded during tool calling execution",
          quotaExceeded: true,
          query,
        });
      } else if (error?.status === 401) {
        errorType = "authentication_failed";
        userMessage =
          "There's a configuration issue with the AI service. Please contact support.";
      } else if (error?.status === 403) {
        errorType = "forbidden";
        userMessage =
          "I don't have permission to process this request. Please contact support.";
      } else if (error?.status >= 500) {
        errorType = "server_error";
        userMessage =
          "The AI service is temporarily unavailable. Please try again in a few moments.";
      } else {
        logger.error({
          message: "Error in executeToolCalling",
          error,
          errorType,
          status: error?.status,
          code: error?.code,
          query,
        });
      }

      return {
        content: userMessage,
        toolResults: [],
      };
    }
  }

  private getToolSchemas(toolNames?: string[]): any[] {
    if (!toolNames || toolNames.length === 0) {
      // If no specific tools are requested, return all available tools
      return Array.from(this.tools.values()).map((tool) => ({
        type: "function",
        function: {
          ...generateJsonSchema(tool),
          // Replace dots with underscores in tool names for OpenAI compatibility
          name: tool.name.replace(/\./g, "_"),
        },
      }));
    }

    // Return schemas only for tools that exist in our available tools
    return toolNames
      .filter((name) => this.tools.has(name))
      .map((name) => {
        const tool = this.tools.get(name)!;
        return {
          type: "function",
          function: {
            ...generateJsonSchema(tool),
            // Replace dots with underscores in tool names for OpenAI compatibility
            name: tool.name.replace(/\./g, "_"),
          },
        };
      });
  }

  // Add a new helper method to find the original tool name from a formatted name
  private getOriginalToolName(formattedName: string): string | undefined {
    // Create a map of formatted names to original names
    const nameMap = new Map<string, string>();

    // Define tool name aliases to handle variations in naming
    const toolAliases: Record<string, string[]> = {
      product_gap_analysis: [
        "analyze_product_gaps",
        "product_gaps",
        "gap_analysis",
      ],
      get_competitors: ["competitors", "fetch_competitors", "competitor_data"],
      product_analysis: ["analyze_products", "product_info", "product_data"],
    };

    // Populate the map with all tool names and their aliases
    for (const originalName of this.tools.keys()) {
      // Standard formatted name (dots to underscores)
      const formattedToolName = originalName.replace(/\./g, "_");
      nameMap.set(formattedToolName, originalName);

      // Add aliases if they exist for this tool
      const aliases = toolAliases[originalName] || [];
      for (const alias of aliases) {
        nameMap.set(alias, originalName);
      }
    }

    // Look up the original name
    return nameMap.get(formattedName);
  }

  public async analyzeIntent(
    message: string,
    history: any[] = []
  ): Promise<IntentAnalysisResult> {
    try {
      // Use OpenAI to analyze the intent of the message
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: `You are an intent classifier for a cannabis dispensary assistant.

Classify the user query and determine which tools would be most helpful to answer it.

IMPORTANT: Distinguish between LOCAL INVENTORY questions vs MARKET/COMPETITOR questions:

LOCAL INVENTORY QUESTIONS (use local DB tools):
- Questions about "our" products, inventory, sales
- Questions about "what we have", "what we sell", "what's in stock"
- Questions about internal business data, performance, customers
- Keywords: "our", "we", "us", "my", "this location", "in store", "available here"
- Examples: "What's our most expensive product?", "How many products do we have?", "What are our sales?"

MARKET/COMPETITOR QUESTIONS (use Supabase tools):
- Questions about competitors, market analysis, external retailers
- Questions about "what's available in the market", "competitor prices"
- Keywords: "competitor", "market", "other dispensaries", "compared to", "vs", "against"
- Examples: "What do competitors charge?", "Market analysis", "Compare with other stores"

Available tools:
- data_hub.query: For LOCAL database queries (sales, inventory, customers, internal data)
- vector_search.query: For searching documents, regulations, strain information
- analysis.run: For running analytics on LOCAL sales, customers, and products
- market.fetch: For competitor pricing, market trends, and tax rates (EXTERNAL data)
- product_gap_analysis: For identifying product gaps compared to competitors
- get_competitors: For retrieving competitor information for a location
- product_analysis: For analyzing MARKET product pricing and availability (EXTERNAL data)
- retailer_products: For querying EXTERNAL retailer product data
- state_regulations: For state-specific cannabis regulations and compliance information
- local_market_analysis: For insights about local cannabis markets by state or city
- seasonal_trends_analysis: For seasonal trends in cannabis sales and inventory planning
- customer_segmentation: For customer segment analysis based on demographics and behavior

Also identify any of these entities:
1. mentionedAgent: If the user directly mentions an agent by name
2. requiredContextTypes: Types of context needed (sales_data, inventory, customers, compliance, etc.)
3. countsRequested: Boolean flag if the user is asking for counts (like "how many X" questions)
4. isLocalQuery: Boolean flag if this is about local inventory/business data vs market data

IMPORTANT: Pay careful attention to count-related questions. Set countsRequested to true if:
- The question asks about counts, quantities, or totals (e.g., "how many products")
- The question asks for a number of items (e.g., "number of customers")
- The question asks about inventory quantity (e.g., "total inventory")
- The query uses words like "count", "total", "quantity", "number of", etc.

Response format (JSON):
{
  "intent": "intent_category", 
  "confidence": 0.0 to 1.0,
  "toolNames": ["tool_name1", "tool_name2"],
  "entities": {
    "mentionedAgent": "agent_name_if_mentioned",
    "requiredContextTypes": ["context_type1", "context_type2"],
    "countsRequested": true/false,
    "isLocalQuery": true/false
  }
}

Intent categories: 
- inventory_query: Questions about local inventory, products we have
- product_recommendation: Product suggestions based on customer needs
- sales_analysis: Analysis of local sales data
- customer_analysis: Analysis of local customer data
- competitor_analysis: Analysis of competitor/market data
- market_trends: Market-wide trends and analysis
- product_gap_analysis: Comparing local vs market product offerings
- compliance_question: Regulatory and compliance questions
- marketing_campaign: Marketing and promotional campaigns
- tax_information: Tax-related queries
- general_inquiry: General questions
- directed_message: Messages directed to specific agents
- image_generation: Requests to generate images
- seasonal_planning: Seasonal inventory and sales planning
- customer_segmentation: Customer segment analysis
- regulatory_compliance: Compliance and regulatory questions`,
          },
          ...history.slice(-3).map((m) => ({
            role: m.role,
            content: m.content,
          })),
          {
            role: "user",
            content: message,
          },
        ],
        temperature: 0.3,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const result = JSON.parse(completion.choices[0].message.content || "{}");

      // Map specific intents to recommended tools
      const toolMappings: Record<string, string[]> = {
        inventory_query: ["data_hub.query", "analysis.run"], // Use LOCAL DB tools for inventory
        product_recommendation: [
          "product_recommendation.search",
          "data_hub.query",
        ], // Use direct ProductDataVectorService
        sales_analysis: ["analysis.run", "data_hub.query"], // Use LOCAL DB tools for sales
        customer_analysis: ["analysis.run", "data_hub.query"], // Use LOCAL DB tools for customers
        product_gap_analysis: ["product_gap_analysis", "get_competitors"],
        competitor_analysis: [
          "market.fetch",
          "get_competitors",
          "product_analysis",
          "retailer_products",
        ],
        market_analysis: [
          "market.fetch",
          "product_analysis",
          "get_competitors",
        ],
        financial_analysis: [
          "margin_analysis",
          "revenue_forecasting",
          "cost_optimization",
          "financial_dashboard",
        ],
        operational_query: ["analysis.run", "data_hub.query"],
        compliance_query: ["vector_search.query", "data_hub.query"],
        document_search: ["vector_search.query"],
        strain_information: [
          "vector_search.query",
          "product_recommendation.search",
        ],
        price_analysis: ["product_analysis", "market.fetch"],
        performance_metrics: ["analysis.run", "data_hub.query"],
      };

      // Add special parameters for specific intent types
      const intentParams: Record<string, Record<string, any>> = {
        inventory_query: {
          count_only: result.entities?.countsRequested === true,
        },
        product_recommendation: {
          use_semantic_search: true,
          include_metadata: true,
          extract_products: true,
          recommendation_context: true,
          count_only: result.entities?.countsRequested === true,
        },
        competitor_analysis: {
          query_type: "competitor_products",
          competitor_context: true,
          count_only: result.entities?.countsRequested === true,
        },
      };

      // Check if this is a query about competitors' products
      if (
        message.toLowerCase().includes("competitor") &&
        message.toLowerCase().includes("product") &&
        (message.toLowerCase().includes("how many") ||
          message.toLowerCase().includes("count"))
      ) {
        // This is specifically asking about competitor products
        result.intent = "competitor_analysis";
        result.toolNames = ["data_hub.query"];
        result.params = {
          query_type: "competitor_products",
          competitor_context: true,
          count_only: true,
        };
        result.entities = {
          ...(result.entities || {}),
          countsRequested: true,
        };
      }

      // Override tool selection based on local vs market query classification
      if (result.entities?.isLocalQuery === true) {
        // Force local tools for local queries regardless of other intent classification
        if (
          result.intent === "product_analysis" ||
          result.intent === "inventory_query"
        ) {
          logger.info({
            message: "Overriding to use local DB tools for local query",
            originalIntent: result.intent,
            originalTools: result.toolNames,
            query: message,
          });
          result.intent = "inventory_query";
          result.toolNames = ["data_hub.query", "analysis.run"];
          result.params = {
            ...(result.params || {}),
            query_type: "local_inventory",
            use_local_db: true,
          };
        }
      } else if (result.entities?.isLocalQuery === false) {
        // Force market tools for market queries
        if (result.intent === "inventory_query") {
          logger.info({
            message: "Overriding to use market tools for market query",
            originalIntent: result.intent,
            originalTools: result.toolNames,
            query: message,
          });
          result.intent = "competitor_analysis";
          result.toolNames = [
            "market.fetch",
            "product_analysis",
            "retailer_products",
          ];
        }
      }

      // If we have a specific intent with tool mappings, add those tools to the result
      if (result.intent && toolMappings[result.intent]) {
        result.toolNames = [
          ...new Set([
            ...(result.toolNames || []),
            ...toolMappings[result.intent],
          ]),
        ];
      }

      // Add any specific parameters for this intent type
      if (result.intent && intentParams[result.intent]) {
        result.params = {
          ...(result.params || {}),
          ...intentParams[result.intent],
        };
      }

      return {
        intent: result.intent || "general_inquiry",
        confidence: result.confidence || 0.5,
        toolNames: result.toolNames || [],
        params: result.params,
        entities: {
          mentionedAgent: result.entities?.mentionedAgent || undefined,
          requiredContextTypes: result.entities?.requiredContextTypes || [],
          countsRequested: result.entities?.countsRequested || false,
          isLocalQuery: result.entities?.isLocalQuery || false,
        },
      };
    } catch (error) {
      logger.error("Error analyzing intent:", error);
      // Return a default result
      return {
        intent: "general_inquiry",
        confidence: 0.5,
        toolNames: [],
        entities: {
          requiredContextTypes: [],
          countsRequested: false,
          isLocalQuery: false,
        },
      };
    }
  }

  private async getChatHistoryForModel(
    chatId: string | number,
    limit: number
  ): Promise<any[]> {
    const id = typeof chatId === "number" ? chatId.toString() : chatId;
    const msgs = await this.db("messages")
      .where("chat_id", id)
      .orderBy("timestamp", "desc")
      .limit(limit);
    return msgs.reverse().map((m) => ({
      role: m.role === "user" ? "user" : "assistant",
      content: m.content,
    }));
  }

  private async buildSystemPrompt(
    agentConfig: any,
    locationId?: number
  ): Promise<string> {
    try {
      // Get competitors for this location
      const competitors = locationId
        ? await this.db("location_competitors")
            .where({ location_id: locationId })
            .select("name", "competitor_place_id")
        : [];

      // Format competitor info
      const compString =
        competitors.length > 0
          ? `Competitors for this location: ${competitors
              .map((c) => c.name)
              .join(", ")}`
          : "No competitor data available for this location.";

      // Get location info
      const location = locationId
        ? await this.db("locations").where({ id: locationId }).first()
        : null;

      const locationInfo = location
        ? `Location: id: ${location.id}, name: ${location.name}, city: ${location.city}, state: ${location.state}`
        : "No location information available.";

      // Build the system prompt using agent config and context
      const base =
        agentConfig.systemPrompt ||
        `You are ${agentConfig.name}, a helpful cannabis dispensary assistant.`;

      return `${base}

${locationInfo}
${compString}

Your name is ${agentConfig.name}.
Your role is ${agentConfig.role}.
Capabilities: ${agentConfig.capabilities.join(", ")}

Guidelines:
1. Be conversational but professional and direct.
2. Provide specific, data-driven answers when you have the data.
3. For any cannabis-related advice, focus on education and harm reduction.
4. Always stay compliant with local regulations.
5. Use dollar amounts when discussing prices.
6. If you use data from tools, briefly explain where the data comes from.
7. Keep responses concise and focused on the question asked.

RESPONSE FORMATTING GUIDELINES:
- Use bullet points (•) for lists and key information
- Include "Why it matters:" explanations for technical recommendations
- Add "➡️ Next step:" actionable recommendations when appropriate
- Use specific numbers, percentages, and dollar figures when available
- Include compliance disclaimers: "*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*"
- Reference specific tools and integrations (BakedBot, Metrc, POS systems) when relevant
- Structure responses with clear sections and technical details
- Avoid generic responses - be specific to cannabis industry context

IMPORTANT - Tool Usage:
- The system automatically routes your requests to the appropriate tools based on intent analysis
- For LOCAL business data (inventory, sales, customers): data_hub.query and analysis.run tools are used
- For EXTERNAL market data (competitors, market analysis): market.fetch and product_analysis tools are used  
- For PRODUCT RECOMMENDATIONS: vector_search.query automatically uses ProductDataVectorService for comprehensive product data
- For DOCUMENT/EDUCATION searches: vector_search.query uses general knowledge base

Product Recommendations:
- When asked for "best", "top", or other subjective product recommendations without specific criteria, assume "best" means "best-selling" or "highest-rated". State your assumption clearly in your response. For example: "When you ask for the best products, I'll show you our top-rated items. If you're looking for something else, like best value or highest potency, just let me know!"
- When providing product recommendations, the system automatically searches using advanced semantic matching
- Results include comprehensive product data: prices, images, SKUs, ratings, effects, and recommendation reasons
- Always mention that recommendations are based on general cannabis knowledge and customers should consult with budtenders
- Focus on explaining why specific products match the customer's needs`;
    } catch (error) {
      logger.error("Error building system prompt:", error);
      return `You are ${agentConfig.name}, a cannabis dispensary assistant. Answer questions professionally and accurately.`;
    }
  }

  public async getAvailableAgents(locationId: number): Promise<any[]> {
    try {
      // This would be replaced with actual availability logic
      // For now, return all agents from the config
      const agents = Object.entries(agentsConfig.agents).map(
        ([id, agent]: [string, any]) => ({
          id,
          name: agent.name,
          role: agent.role,
          description: agent.description,
          icon: agent.icon || "🤖",
          capabilities: agent.capabilities,
          disabled: agent.disabled || false,
          metadata: {},
        })
      );

      return agents.filter((a) => !a.disabled);
    } catch (error) {
      logger.error("Error getting available agents:", error);
      return [this.getDefaultAgent()];
    }
  }

  public getDefaultAgent(): any {
    return {
      id: "1", // SMOKEY
      name: "SMOKEY",
      role: "AI Budtender & Customer Experience",
      description:
        "Front-line customer interaction and product recommendations specialist",
      icon: "🌿",
      capabilities: [
        "Real-time product recommendations",
        "Educational content delivery",
      ],
      disabled: false,
      metadata: {},
    };
  }

  public async generateTitle(messages: any[]): Promise<string> {
    try {
      logger.info({
        message: "Generating chat title",
        messagesCount: messages?.length || 0,
        hasMessages: !!messages,
        firstMessage: messages?.[0],
      });

      // Handle edge cases
      if (!messages || messages.length === 0) {
        logger.warn("No messages provided for title generation");
        return "Chat Conversation";
      }

      // Filter messages that have content and map them safely
      const messageContents = messages
        .slice(0, 5)
        .filter((m) => m && typeof m.content === "string" && m.content.trim())
        .map((m) => m.content.trim());

      if (messageContents.length === 0) {
        logger.warn("No valid message content found for title generation");
        return "Chat Conversation";
      }

      const combinedContent = messageContents.join("\n");

      logger.info({
        message: "Sending title generation request to OpenAI",
        contentLength: combinedContent.length,
        messageCount: messageContents.length,
      });

      const c = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content:
              "Generate a concise chat title. less than 6 words. Use the most relevant information from the conversation.",
          },
          {
            role: "user",
            content: combinedContent,
          },
        ],
        temperature: 0.3,
        max_tokens: 50,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const generatedTitle =
        c.choices[0]?.message.content ?? "Chat Conversation";

      logger.info({
        message: "Successfully generated chat title",
        generatedTitle,
      });

      return generatedTitle;
    } catch (err: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";
      let userFriendlyMessage = "Chat Conversation";

      if (err?.status === 429) {
        errorType = "quota_exceeded";
        userFriendlyMessage = "Chat Conversation"; // Still return fallback, but log appropriately
      } else if (err?.status === 401) {
        errorType = "authentication_failed";
      } else if (err?.status === 403) {
        errorType = "forbidden";
      } else if (err?.status >= 500) {
        errorType = "server_error";
      } else if (err?.code === "insufficient_quota") {
        errorType = "quota_exceeded";
      } else if (err?.message?.includes("quota")) {
        errorType = "quota_exceeded";
      }

      logger.error({
        message: "Error generating chat title",
        errorType,
        error: err,
        errorMessage: err instanceof Error ? err.message : String(err),
        status: err?.status,
        code: err?.code,
        type: err?.type,
        messagesProvided: !!messages,
        messagesCount: messages?.length || 0,
      });

      // For quota errors, we might want to implement retry logic or fallback behavior
      if (errorType === "quota_exceeded") {
        logger.warn({
          message:
            "OpenAI quota exceeded for title generation - using fallback title",
          chatTitleFallback: true,
        });
      }

      return userFriendlyMessage;
    }
  }

  public async cleanup(): Promise<void> {
    try {
      // Close database connections if needed
      logger.info("SmokeyAIService cleaned up successfully");
    } catch (error) {
      logger.error("Error during cleanup:", error);
    }
  }

  public async selectAgent(
    intent: IntentAnalysisResult,
    chatId: number,
    availableAgents: any[]
  ): Promise<any> {
    try {
      // If there's a mentioned agent, try to find it in available agents
      if (
        intent.intent === "directed_message" &&
        intent.entities?.mentionedAgent
      ) {
        const mentionedName = intent.entities.mentionedAgent.toLowerCase();
        const matchedAgent = availableAgents.find(
          (a) => a.name.toLowerCase() === mentionedName
        );
        if (matchedAgent) {
          logger.info({
            message: "Agent selected based on @mention",
            mentioned_agent: mentionedName,
            selected_agent: matchedAgent.name,
          });
          return matchedAgent;
        }
      }

      // Otherwise, select the most appropriate agent for the intent
      // Map intents to actual agent role keywords that match agents.json
      const intentAgentMapping: Record<string, string[]> = {
        sales_analysis: ["Business Intelligence", "Strategy"], // POPS
        inventory_query: ["Business Intelligence", "Strategy"], // POPS
        compliance_question: ["Compliance", "Security"], // DEEBO
        marketing_campaign: ["Marketing", "Automation"], // CRAIG
        customer_analysis: ["Customer Relations"], // MRS. PARKER
        product_recommendation: ["Budtender", "Customer Experience"], // SMOKEY
        image_generation: ["Marketing", "Automation"], // CRAIG
        market_trends: ["Market Intelligence"], // EZAL
        competitor_analysis: ["Market Intelligence"], // EZAL
        financial_analysis: ["Financial Analytics"], // MONEY MIKE
        general_inquiry: ["Budtender", "Customer Experience"], // SMOKEY (default)
      };

      const relevantRoles = intentAgentMapping[intent.intent] || [
        "Customer Experience", // Default to SMOKEY
      ];

      // Find the first agent with a matching role
      const matchedAgent = availableAgents.find((a) =>
        relevantRoles.some((role) => a.role.includes(role))
      );

      if (matchedAgent) {
        logger.info({
          message: "Agent selected based on intent analysis",
          intent: intent.intent,
          selected_agent: matchedAgent.name,
          agent_role: matchedAgent.role,
          confidence: intent.confidence,
        });
        return matchedAgent;
      }

      // Return default agent (first available) if no better match found
      logger.info({
        message: "No agent match found, using default agent",
        intent: intent.intent,
        default_agent: availableAgents[0]?.name,
      });
      return availableAgents[0]; // Fallback to first agent
    } catch (error) {
      logger.error("Error selecting agent:", error);
      return availableAgents[0]; // Fallback to first agent
    }
  }

  public setCurrentUserId(userId: number): void {
    this.currentUserId = userId;
    logger.info({ userId }, "Set current user ID");
  }

  public async generateChatSummary(messages: any[]): Promise<string> {
    try {
      const formatted = messages.map((m) => ({
        role: m.role,
        content: m.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content:
              "You are an expert at summarizing conversations. Create a concise summary of the chat that captures the main topics and conclusions.",
          },
          ...formatted,
        ],
        temperature: 0.5,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      return response.choices[0].message.content || "No summary generated.";
    } catch (error) {
      logger.error("Error generating chat summary:", error);
      return "Failed to generate summary.";
    }
  }

  public async extractTopics(messages: any[]): Promise<string[]> {
    try {
      const formatted = messages.map((m) => ({
        role: m.role,
        content: m.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content:
              'Extract the main topics discussed in this conversation. Return a JSON array of topic strings. Example: ["Inventory management", "Customer retention"]',
          },
          ...formatted,
        ],
        temperature: 0.3,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const content = response.choices[0].message.content;
      const parsed = JSON.parse(content || "{}");
      return Array.isArray(parsed.topics) ? parsed.topics : [];
    } catch (error) {
      logger.error("Error extracting topics:", error);
      return [];
    }
  }

  public async extractChatMetrics(messages: any[]): Promise<{
    clv: number | null;
    roi: number | null;
    retention: number | null;
  }> {
    try {
      const formatted = messages.map((m) => ({
        role: m.role,
        content: m.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content:
              "Analyze this conversation and extract the following metrics if they are discussed (use null if not mentioned): Customer Lifetime Value (clv), Campaign ROI (roi), and Retention Rate (retention). Return a JSON object with these metrics.",
          },
          ...formatted,
        ],
        temperature: 0.2,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const content = response.choices[0].message.content;
      const parsed = JSON.parse(content || "{}");

      return {
        clv: typeof parsed.clv === "number" ? parsed.clv : null,
        roi: typeof parsed.roi === "number" ? parsed.roi : null,
        retention:
          typeof parsed.retention === "number" ? parsed.retention : null,
      };
    } catch (error) {
      logger.error("Error extracting chat metrics:", error);
      return { clv: null, roi: null, retention: null };
    }
  }

  public async analyzeChatSegment(messages: any[]): Promise<any[]> {
    try {
      const formatted = messages.map((m) => ({
        role: m.role,
        content: m.content,
      }));

      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: `Analyze this conversation segment and identify key insights. For each insight, include:
1. title - short descriptive title
2. description - detailed explanation
3. type - one of: customer_needs, product_feedback, operational_issue, competitive_insight, market_trend
4. impact - one of: high, medium, low
5. actionItems - array of suggested action items

Return a JSON array of insight objects.`,
          },
          ...formatted,
        ],
        temperature: 0.4,
        response_format: { type: "json_object" },
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const content = response.choices[0].message.content;
      const parsed = JSON.parse(content || "{}");
      return Array.isArray(parsed.insights) ? parsed.insights : [];
    } catch (error) {
      logger.error("Error analyzing chat segment:", error);
      return [];
    }
  }

  public getOpenAI(): OpenAI {
    return this.openai;
  }

  public getTools(): Map<string, Tool> {
    return this.tools;
  }

  public async generateSmokeyResponse(
    systemPrompt: string,
    userMessage: string,
    chatHistory: any[] = []
  ): Promise<string> {
    try {
      logger.info({
        message: "Generating direct SMOKEY response",
        userMessageLength: userMessage.length,
        historyLength: chatHistory.length,
      });

      // Format history for OpenAI
      const formattedHistory = chatHistory.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      // Create the OpenAI chat completion request - direct to SMOKEY without tool calling
      const response = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          { role: "system", content: systemPrompt },
          ...formattedHistory,
          { role: "user", content: userMessage },
        ],
        temperature: this.defaultTemperature,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      const content =
        response.choices[0].message.content ||
        "I'm sorry, I couldn't generate a response. Please try again.";

      // Enhance the response based on question type and guidelines
      const questionType =
        this.responseEnhancementService.analyzeQuestionType(userMessage);
      const enhancedContent =
        await this.responseEnhancementService.enhanceResponse(
          content,
          userMessage,
          questionType,
          "SMOKEY"
        );

      logger.info({
        message: "Successfully generated direct SMOKEY response",
        originalLength: content.length,
        enhancedLength: enhancedContent.length,
      });

      return enhancedContent;
    } catch (error: any) {
      // Enhanced error handling for different OpenAI API errors
      let errorType = "unknown";
      let userMessage =
        "I encountered an error while processing your request. Please try again later.";

      if (
        error?.status === 429 ||
        error?.code === "insufficient_quota" ||
        error?.message?.includes("quota")
      ) {
        errorType = "quota_exceeded";
        userMessage =
          "I'm currently experiencing high demand. Please try again in a few moments.";

        logger.warn({
          message: "OpenAI quota exceeded during SMOKEY response generation",
          quotaExceeded: true,
        });
      } else if (error?.status === 401) {
        errorType = "authentication_failed";
        userMessage =
          "There's a configuration issue with the AI service. Please contact support.";
      } else if (error?.status === 403) {
        errorType = "forbidden";
        userMessage =
          "I don't have permission to process this request. Please contact support.";
      } else if (error?.status >= 500) {
        errorType = "server_error";
        userMessage =
          "The AI service is temporarily unavailable. Please try again in a few moments.";
      } else {
        logger.error({
          message: "Error in generateSmokeyResponse",
          error,
          errorType,
          status: error?.status,
          code: error?.code,
        });
      }

      return userMessage;
    }
  }

  public async generateMarketInsights(
    locationId: number,
    insightCount: number = 5
  ): Promise<any[]> {
    try {
      logger.info({
        message: "Generating market insights using SmokeyAIService",
        locationId,
        insightCount,
      });

      // Gather location data
      const location = await this.db("locations")
        .where("id", locationId)
        .select("id", "name", "city", "state", "retailer_id")
        .first();

      if (!location) {
        throw new Error(`Location ${locationId} not found`);
      }

      // Get competitors
      const competitors = await this.db("location_competitors")
        .where("location_id", locationId)
        .select("name", "competitor_place_id")
        .limit(5);

      // Get detailed product and financial data
      const productCount = await this.db("products")
        .where("location_id", locationId)
        .count("id as count")
        .first();

      // Get product pricing analytics
      const productPricing = await this.db("products")
        .where("location_id", locationId)
        .select(
          this.db.raw("AVG(latest_price) as avg_price"),
          this.db.raw("MIN(latest_price) as min_price"),
          this.db.raw("MAX(latest_price) as max_price"),
          this.db.raw("COUNT(DISTINCT category) as category_count")
        )
        .first();

      // Get recent sales data with financial metrics (if available)
      let recentSalesData = null;
      let salesMetrics = null;
      try {
        const salesCount = await this.db("pos_data")
          .where("location_id", locationId)
          .where(
            "transaction_date",
            ">=",
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          )
          .count("id as count")
          .first();
        recentSalesData = salesCount;

        // Get sales revenue and averages
        const salesRevenue = await this.db("pos_data")
          .where("location_id", locationId)
          .where(
            "transaction_date",
            ">=",
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          )
          .select(
            this.db.raw("SUM(total_amount) as total_revenue"),
            this.db.raw("AVG(total_amount) as avg_transaction"),
            this.db.raw("COUNT(DISTINCT customer_id) as unique_customers")
          )
          .first();
        salesMetrics = salesRevenue;
      } catch (error) {
        logger.debug("No sales data available for location", locationId);
      }

      // Get top-selling categories with revenue data
      let topCategories: Array<{
        category: string;
        category_revenue: number;
        transaction_count: number;
      }> = [];
      try {
        topCategories = await this.db("pos_data")
          .join("products", "pos_data.product_id", "products.id")
          .where("pos_data.location_id", locationId)
          .where(
            "transaction_date",
            ">=",
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          )
          .groupBy("products.category")
          .select(
            "products.category",
            this.db.raw("SUM(pos_data.total_amount) as category_revenue"),
            this.db.raw("COUNT(*) as transaction_count")
          )
          .orderBy("category_revenue", "desc")
          .limit(3);
      } catch (error) {
        logger.debug(
          "No category sales data available for location",
          locationId
        );
      }

      const competitorInfo =
        competitors.length > 0
          ? `Competitors in the area: ${competitors
              .map((c: any) => c.name)
              .join(", ")}`
          : "No competitor data available";

      const locationName = `${location.city}${
        location.state ? `, ${location.state}` : ""
      }`;

      // Build detailed financial context
      const pricingInfo = productPricing
        ? `Product pricing: $${Math.round(
            productPricing.avg_price || 0
          )} avg, $${Math.round(productPricing.min_price || 0)}-$${Math.round(
            productPricing.max_price || 0
          )} range, ${productPricing.category_count || 0} categories`
        : "Limited pricing data available";

      const revenueInfo = salesMetrics
        ? `Revenue metrics: $${Math.round(
            salesMetrics.total_revenue || 0
          )} total (30d), $${Math.round(
            salesMetrics.avg_transaction || 0
          )} avg transaction, ${
            salesMetrics.unique_customers || 0
          } unique customers`
        : "";

      const categoryInfo =
        topCategories.length > 0
          ? `Top categories by revenue: ${topCategories
              .map(
                (cat) =>
                  `${cat.category} ($${Math.round(cat.category_revenue || 0)})`
              )
              .join(", ")}`
          : "";

      const prompt = `You are a cannabis market analyst. Generate ${insightCount} quick, actionable market insights for a dispensary.

LOCATION DATA:
- Location: ${location.name} in ${locationName}
- Product inventory: ${productCount?.count || 0} products
- ${pricingInfo}
- ${competitorInfo}

FINANCIAL DATA:
${
  recentSalesData
    ? `- Transaction volume: ${recentSalesData.count} transactions (last 30 days)`
    : ""
}
${revenueInfo ? `- ${revenueInfo}` : ""}
${categoryInfo ? `- ${categoryInfo}` : ""}

Generate insights in this EXACT JSON format:
{
  "insights": [
    {
      "title": "Brief title (max 25 characters)",
      "content": "Concise insight with NUMBERS (max 80 characters)",
      "type": "market|trending|demographics|performance",
      "action": "View Details"
    }
  ]
}

REQUIREMENTS:
- Include specific numbers, percentages, or dollar amounts in each insight
- Use actual financial data when available (revenue, pricing, transaction counts)
- Focus on actionable metrics like conversion rates, pricing opportunities, revenue trends
- Make insights data-driven and specific to ${locationName}
- Examples: "15% price increase opportunity", "$2.3K monthly revenue gap", "Top category: Flower (67% sales)"

Focus areas with NUMERICAL data:
- Revenue performance and growth opportunities (use actual $$ amounts)
- Pricing strategy vs market averages (use price comparisons)
- Category performance and trends (use revenue percentages)
- Customer behavior metrics (use transaction/customer data)
- Competitive positioning (use pricing/volume comparisons)

Each insight MUST include a specific number, percentage, or dollar figure.`;

      logger.info({
        message: "Calling OpenAI for market insights with financial data",
        locationId,
        locationName,
        productCount: productCount?.count || 0,
        competitorCount: competitors.length,
        avgPrice: productPricing?.avg_price || 0,
        totalRevenue: salesMetrics?.total_revenue || 0,
        transactionCount: recentSalesData?.count || 0,
        topCategoriesCount: topCategories.length,
      });

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content:
              "You are a cannabis market analyst. Always respond with valid JSON only.",
          },
          { role: "user", content: prompt },
        ],
        temperature: 0.7,
        user: this.currentUserId ? String(this.currentUserId) : undefined,
      });

      logger.info({
        message: "OpenAI response received for market insights",
        locationId,
        responseId: response.id,
        model: response.model,
        usage: response.usage,
      });

      const rawContent = response.choices[0].message.content || "{}";
      const result = JSON.parse(rawContent);

      if (!result.insights || !Array.isArray(result.insights)) {
        throw new Error("Invalid response format from AI");
      }

      // Validate and clean up insights
      const validatedInsights = result.insights
        .filter(
          (insight: any) => insight.title && insight.content && insight.type
        )
        .slice(0, insightCount)
        .map((insight: any) => ({
          title: String(insight.title).substring(0, 25),
          content: String(insight.content).substring(0, 80),
          type: this.validateInsightType(insight.type),
          action: "View Details",
        }));

      logger.info({
        message: "Successfully generated market insights",
        locationId,
        generatedCount: validatedInsights.length,
        requestedCount: insightCount,
      });

      return validatedInsights;
    } catch (error) {
      logger.error({
        message: "Error generating market insights",
        locationId,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      });

      // Return fallback insights on error
      return [
        {
          title: "Market Analysis",
          content: "Analyzing your local market conditions...",
          type: "market",
          action: "Refresh",
        },
        {
          title: "Growth Opportunity",
          content: "Exploring expansion possibilities in your area",
          type: "performance",
          action: "View Details",
        },
        {
          title: "Customer Trends",
          content: "Understanding local customer preferences",
          type: "demographics",
          action: "View Details",
        },
        {
          title: "Competitive Edge",
          content: "Identifying differentiation strategies",
          type: "market",
          action: "View Details",
        },
        {
          title: "Revenue Insights",
          content: "Analyzing revenue patterns and opportunities",
          type: "performance",
          action: "View Details",
        },
      ].slice(0, insightCount);
    }
  }

  private validateInsightType(
    type: string
  ): "market" | "trending" | "demographics" | "performance" {
    const validTypes = ["market", "trending", "demographics", "performance"];
    return validTypes.includes(type) ? (type as any) : "market";
  }

  private isModelRelatedQuery(query: string): boolean {
    // Detect questions about the AI model, training, architecture, etc.
    const modelKeywords = [
      /what\s+(ai\s+)?model\s+(are\s+you|do\s+you)\s+using/i,
      /which\s+(ai\s+)?model\s+(are\s+you|do\s+you)\s+use/i,
      /what\s+(language\s+)?model\s+(are\s+you|is\s+this)/i,
      /are\s+you\s+(gpt|chatgpt|claude|llama|gemini)/i,
      /what\s+(version\s+of\s+)?(gpt|openai|claude|anthropic)/i,
      /what\s+(ai\s+)?system\s+(are\s+you|do\s+you)\s+running/i,
      /what\s+(training\s+)?data\s+(do\s+you|were\s+you)/i,
      /how\s+(were\s+you|are\s+you)\s+trained/i,
      /what\s+technology\s+(powers|runs)\s+you/i,
      /what\s+(ai\s+)?engine\s+(are\s+you|do\s+you)\s+use/i,
      /what\s+kind\s+of\s+(ai|model|system)\s+are\s+you/i,
      /tell\s+me\s+about\s+your\s+(model|training|ai)/i,
    ];

    return modelKeywords.some((pattern) => pattern.test(query));
  }

  private getModelBrandedResponse(): string {
    return `I'm powered by **BakedBot's custom cannabis-specialized AI models** that have been specifically trained and fine-tuned for the cannabis industry.

**What makes our models special:**
• 🌿 **Cannabis-specific training**: Trained on extensive cannabis industry data, regulations, and best practices
• 📊 **Industry expertise**: Specialized knowledge in dispensary operations, compliance, product knowledge, and market trends  
• 🔬 **Product intelligence**: Deep understanding of cannabinoids, terpenes, strains, and effects
• 📈 **Business focus**: Optimized for cannabis retail, marketing, compliance, and growth strategies

**Our AI stack includes:**
• Custom language models fine-tuned for cannabis terminology and context
• Specialized product recommendation engines trained on cannabis consumer preferences
• Compliance-aware responses that understand state-by-state regulations
• Industry-specific integrations with POS systems, Metrc, and cannabis data sources

This isn't generic AI—it's cannabis intelligence built specifically for dispensaries like yours. That's why I can provide accurate strain recommendations, compliance guidance, and industry-specific insights that generic AI models simply can't match.

Ready to see what cannabis-specialized AI can do for your business? 🚀`;
  }
}
