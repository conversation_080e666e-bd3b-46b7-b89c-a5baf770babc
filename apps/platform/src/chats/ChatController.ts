/* eslint-disable indent */
import Router from "@koa/router";
import { ChatService } from "./ChatService";
import { SmokeyAIService } from "./SmokeyAIService";
import { checkIntegrationStatus } from "../integrations";
import { logger } from "../config/logger";
import { locationRoleMiddleware } from "../locations/LocationService";
import { JSONSchemaType, validate } from "../core/validate";
import { Message, Agent, Chat, AgentsConfig } from "./models/types";
import * as agentsConfigJson from "../agents/agents.json";
import {
  DefaultContext,
  DefaultState,
  Middleware,
  Next,
  ParameterizedContext,
} from "koa";
import App from "../app";
import { crewAIService } from "../insights/CrewAIService";
import { ImageGenerationService } from "../insights/ImageGenerationService";
import { imageDownloadService } from "../storage/ImageDownloadService";
import { AgentBasedInsightService } from "../insights/AgentBasedInsightService";
import * as fs from "fs";
import * as path from "path";
import * as crypto from "crypto";
import * as util from "util";
import { getAvailableAgents } from "../integrations/IntegrationService";

interface SendMessageParams {
  message: string;
  agent_id: string;
  chat_id?: string | null;
}

const sendMessageSchema: JSONSchemaType<SendMessageParams> = {
  type: "object",
  required: ["message", "agent_id"],
  properties: {
    message: { type: "string" },
    agent_id: { type: "string" },
    chat_id: { type: "string", nullable: true },
  },
  additionalProperties: false,
};

interface LocationState {
  location: { id: number };
  locationRole: string;
  scope: string[];
  app: App;
}

type AppState = DefaultState & LocationState;
type AppDefaultContext = DefaultContext & { state: AppState };

// Helper function to convert DB Chat to API Chat
function convertToChat(dbChat: any): Chat {
  return {
    id: dbChat.id.toString(),
    chat_id: dbChat.chat_id,
    name: dbChat.name,
    created_at: dbChat.created_at,
    updated_at: dbChat.updated_at,
    location_id: dbChat.location_id.toString(),
    agent_ids: dbChat.agent_ids,
    status: dbChat.status,
    metadata: dbChat.metadata || {},
    agents: dbChat.agents?.map((agent: any) => ({
      id: agent.id,
      name: agent.name,
      role: agent.role,
      description: agent.description,
      icon: agent.icon || "🤖",
      capabilities: agent.capabilities,
      disabled: agent.disabled,
      metadata: agent.metadata || {},
    })),
  };
}

// Helper functions for integration and agent checks
async function getIntegrationStatus(
  app: App,
  locationId: string
): Promise<Record<string, { connected: boolean; status?: string }>> {
  try {
    if (!app.db) {
      throw new Error("Database is not initialized");
    }
    const integrations = await checkIntegrationStatus(app.db, locationId);

    // Convert to expected format to fix type error
    const result: Record<string, { connected: boolean; status?: string }> = {};

    Object.entries(integrations).forEach(([key, value]) => {
      if (value) {
        // Basic property with connected status
        const integration: { connected: boolean; status?: string } = {
          connected: !!value.connected,
        };

        // Safely check for status property
        const valueAny = value as any;
        if (
          Object.prototype.hasOwnProperty.call(valueAny, "status") &&
          valueAny.status
        ) {
          integration.status = String(valueAny.status);
        }

        result[key] = integration;
      } else {
        result[key] = { connected: false };
      }
    });

    return result;
  } catch (error) {
    logger.error("Error checking integration status:", error);
    return {};
  }
}

async function isAgentUnlocked(
  agentId: string,
  locationId: string,
  integrations: Record<string, { connected: boolean; status?: string }>
): Promise<{ unlocked: boolean; missingRequirements: string[] }> {
  const config = agentsConfigJson as unknown as AgentsConfig;
  const agentConfig = config.agents[agentId];

  if (!agentConfig || agentConfig.disabled) {
    return { unlocked: false, missingRequirements: ["Agent is disabled"] };
  }

  // Check for required integrations
  const missingRequirements =
    agentConfig.requirements?.required
      ?.filter((req) => !integrations[req]?.connected)
      .map((req) => `Missing ${req} integration`) || [];

  return {
    unlocked: missingRequirements.length === 0,
    missingRequirements,
  };
}

function getAgentCapabilities(
  agent: Agent,
  integrations: Record<string, { connected: boolean; status?: string }>
): string[] {
  // Filter out capabilities that require unconnected integrations
  const config = agentsConfigJson as unknown as AgentsConfig;
  const agentConfig = config.agents[agent.id.toString()];

  if (!agentConfig) return agent.capabilities;

  // For now, return all capabilities
  return agent.capabilities;
}

// Move helper functions to the top
async function getAgentById(
  app: App,
  id: string,
  locationId: string
): Promise<
  (Agent & { unlocked: boolean; missingRequirements: string[] }) | undefined
> {
  const config = agentsConfigJson as unknown as AgentsConfig;
  const agentConfig = config.agents[id];
  if (!agentConfig || agentConfig.disabled) return undefined;

  const integrationStatus = await getIntegrationStatus(app, locationId);
  const { unlocked, missingRequirements } = await isAgentUnlocked(
    id,
    locationId,
    integrationStatus
  );

  return {
    id,
    name: agentConfig.name,
    role: agentConfig.role,
    description: agentConfig.description,
    icon: agentConfig.icon || "🤖",
    capabilities: getAgentCapabilities(
      {
        id,
        name: agentConfig.name,
        role: agentConfig.role,
        description: agentConfig.description,
        icon: agentConfig.icon || "🤖",
        capabilities: agentConfig.capabilities,
        disabled: agentConfig.disabled,
      },
      integrationStatus
    ),
    disabled: false,
    metadata: {},
    unlocked,
    missingRequirements,
  };
}

const mkdirp = util.promisify(fs.mkdir);
const writeFile = util.promisify(fs.writeFile);
const fsAccess = util.promisify(fs.access);

export class ChatController {
  private chatService?: ChatService;
  private chatAIService?: SmokeyAIService;
  private readonly app: App;

  constructor(app: App) {
    this.app = app;
  }

  private ensureServices() {
    if (!this.chatService || !this.chatAIService) {
      if (!this.app.db) {
        throw new Error("Database is not initialized");
      }
      const wss = this.app.getWebSocketServer();
      if (!wss) {
        throw new Error("WebSocket server is not initialized");
      }
      this.chatService = new ChatService(this.app.db, wss);
      this.chatAIService = this.app.chatAIService;
    }
  }

  async listChats(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const chats = await this.chatService!.listChats(ctx.state.location.id);
    ctx.body = chats.map((chat) => convertToChat(chat));
  }

  async createChat(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const payload = validate<SendMessageParams>(
      sendMessageSchema,
      ctx.request.body
    );
    const agent = await getAgentById(
      this.app,
      payload.agent_id,
      ctx.state.location.id.toString()
    );

    // Always create a chat, even if the agent is not available
    // We'll use a fallback agent or general agent for unavailable agents
    let selectedAgent = agent;
    if (!agent || !agent.unlocked) {
      // Use a fallback general agent configuration
      selectedAgent = {
        id: "general",
        name: "BakedBot Assistant",
        role: "General Assistant",
        description: "General purpose assistant for BakedBot",
        icon: "🤖",
        capabilities: ["general_assistance", "help", "guidance"],
        disabled: false,
        metadata: {},
        unlocked: true,
        missingRequirements: [],
      };
    }

    const chat = await this.chatService!.createChat(
      ctx.state.location.id,
      "New Chat",
      selectedAgent
    );
    ctx.body = convertToChat(chat);
  }

  async getChat(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const chat = await this.chatService!.getChat(
      ctx.params.id,
      ctx.state.location.id
    );
    if (!chat || chat.location_id !== ctx.state.location.id) {
      ctx.throw(404, "Chat not found");
    }
    ctx.body = convertToChat(chat);
  }

  async updateChat(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const chat = await this.chatService!.getChat(
      ctx.params.id,
      ctx.state.location.id
    );
    if (!chat || chat.location_id !== ctx.state.location.id) {
      ctx.throw(404, "Chat not found");
    }

    // First rename the chat
    await this.chatService!.renameChat(
      ctx.params.id,
      ctx.request.body.name,
      ctx.state.location.id
    );

    // Then mark the chat as manually renamed in metadata
    const chatMetadata = chat.metadata || {};
    const newMetadata: Record<string, any> = {
      ...chatMetadata,
      wasRenamed: true,
    };
    await this.chatService!.updateChatMetadata(chat.chat_id, newMetadata);

    const updatedChat = await this.chatService!.getChat(
      ctx.params.id,
      ctx.state.location.id
    );
    ctx.body = convertToChat(updatedChat!);
  }

  async deleteChat(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const chat = await this.chatService!.getChat(
      ctx.params.id,
      ctx.state.location.id
    );
    if (!chat || chat.location_id !== ctx.state.location.id) {
      ctx.throw(404, "Chat not found");
    }

    await this.chatService!.deleteChat(ctx.params.id, ctx.state.location.id);
    ctx.status = 204;
  }

  async archiveChat(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const chat = await this.chatService!.getChat(
      ctx.params.id,
      ctx.state.location.id
    );
    if (!chat || chat.location_id !== ctx.state.location.id) {
      ctx.throw(404, "Chat not found");
    }

    await this.chatService!.archiveChat(ctx.params.id, ctx.state.location.id);
    ctx.status = 204;
  }

  async getMessages(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const { before, limit = "50" } = ctx.query;
    const beforeDate = before ? new Date(before as string) : undefined;
    const limitNum = parseInt(limit as string, 10);

    const messages = await this.chatService!.getMessages(
      ctx.params.id,
      limitNum,
      beforeDate
    );
    ctx.body = messages;
  }

  async sendMessage(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();
    const payload = validate<SendMessageParams>(
      sendMessageSchema,
      ctx.request.body
    );

    // Get or create chat
    let chat;
    if (payload.chat_id) {
      chat = await this.chatService!.getChat(
        payload.chat_id,
        ctx.state.location.id
      );
      if (!chat) {
        ctx.throw(404, "Chat not found");
      }
    } else {
      const agent = await getAgentById(
        this.app,
        payload.agent_id,
        ctx.state.location.id.toString()
      );

      // Use fallback agent if the requested agent is not available
      let selectedAgent = agent;
      if (!agent || !agent.unlocked) {
        selectedAgent = {
          id: "general",
          name: "BakedBot Assistant",
          role: "General Assistant",
          description: "General purpose assistant for BakedBot",
          icon: "🤖",
          capabilities: ["general_assistance", "help", "guidance"],
          disabled: false,
          metadata: {},
          unlocked: true,
          missingRequirements: [],
        };
      }

      chat = await this.chatService!.createChat(
        ctx.state.location.id,
        "New Chat",
        selectedAgent
      );
    }

    // Pass user ID to chat service if available
    if (
      ctx.state.admin &&
      ctx.state.admin.id &&
      typeof this.chatAIService!.setCurrentUserId === "function"
    ) {
      // Set the admin ID for this request
      this.chatAIService!.setCurrentUserId(ctx.state.admin.id);
    }

    // Create user message
    const userMessage: Omit<Message, "id"> = {
      content: payload.message,
      role: "user",
      timestamp: new Date(),
      chat_id: chat.chat_id,
    };

    // Enhanced intent analysis: determines both intent and required context types
    const intent = await this.chatAIService!.analyzeIntent(payload.message);

    // Add intent information to user message metadata
    userMessage.metadata = {
      intent: intent.intent,
      confidence: intent.confidence,
    };

    // Add mention information if detected
    if (
      intent.intent === "directed_message" &&
      intent.entities?.mentionedAgent
    ) {
      userMessage.metadata.mentionedAgent = intent.entities?.mentionedAgent;
    }

    // Add requiredContextTypes to message metadata for future reference
    if (
      intent.entities?.requiredContextTypes &&
      intent.entities?.requiredContextTypes.length > 0
    ) {
      userMessage.metadata.requiredContextTypes =
        intent.entities?.requiredContextTypes;

      logger.info({
        message: "Context types required for this query",
        requiredContextTypes: intent.entities?.requiredContextTypes,
        user_message: payload.message,
      });
    }

    const createdMessage = await this.chatService!.createMessage(userMessage);

    // Get all available agents for this location
    const availableAgents = await this.chatService!.getAvailableAgents(
      ctx.state.location.id
    );

    // Filter to only include agents that are unlocked and available
    const enrichedAgents = await Promise.all(
      availableAgents.map(async (agent) => {
        const integrationStatus = await getIntegrationStatus(
          this.app,
          ctx.state.location.id.toString()
        );
        const { unlocked, missingRequirements } = await isAgentUnlocked(
          agent.id.toString(),
          ctx.state.location.id.toString(),
          integrationStatus
        );
        return { ...agent, unlocked, missingRequirements };
      })
    );

    const unlockedAgents = enrichedAgents.filter((agent) => agent.unlocked);

    // Select the best agent for this message based on @mentions first, then intent analysis
    // Default to requested agent if no better match is found
    let selectedAgent;

    // Only try to route if we have multiple agents
    if (unlockedAgents.length > 1) {
      try {
        selectedAgent = await this.chatAIService!.selectAgent(
          intent,
          typeof chat.id === "string" ? parseInt(chat.id) : chat.id,
          unlockedAgents
        );

        // Log appropriate message based on how agent was selected
        if (
          intent.intent === "directed_message" &&
          intent.entities?.mentionedAgent
        ) {
          logger.info({
            message: "Agent selected based on @mention",
            mentioned_agent: intent.entities?.mentionedAgent,
            selected_agent: selectedAgent.name,
            original_agent_id: payload.agent_id,
            required_contexts: intent.entities?.requiredContextTypes || [],
          });
        } else {
          logger.info({
            message: "Agent auto-selected based on intent",
            intent: intent.intent,
            selected_agent: selectedAgent.name,
            original_agent_id: payload.agent_id,
            required_contexts: intent.entities?.requiredContextTypes || [],
          });
          console.log("$$$ Agent selected based on intent", selectedAgent);
        }
      } catch (error) {
        logger.error(
          "Error in agent selection, falling back to requested agent",
          error
        );
      }
    }

    // If no agent was selected or selection failed, use the originally requested agent
    // Store the original requested agent info for context
    let originalRequestedAgent = null;
    if (!selectedAgent) {
      originalRequestedAgent = await getAgentById(
        this.app,
        payload.agent_id,
        ctx.state.location.id.toString()
      );

      if (!originalRequestedAgent || !originalRequestedAgent.unlocked) {
        // Use fallback agent instead of throwing error
        selectedAgent = {
          id: "general",
          name: "BakedBot Assistant",
          role: "General Assistant",
          description: "General purpose assistant for BakedBot",
          icon: "🤖",
          capabilities: ["general_assistance", "help", "guidance"],
          disabled: false,
          metadata: {},
          unlocked: true,
          missingRequirements: [],
        };
      } else {
        selectedAgent = originalRequestedAgent;
      }
    }

    // Check if this is a marketing campaign request
    let isCampaignRequest = false;
    let campaignPlan = null;

    // Check if this is an image generation request
    let isImageRequest = false;
    let generatedImageUrl = null;

    // Detect marketing campaign requests
    if (intent.intent === "marketing_campaign") {
      try {
        logger.info({
          message: "Marketing campaign request detected, generating plan",
          chat_id: chat.id,
          user_query: payload.message,
        });

        // Generate automation plan using CrewAI
        campaignPlan = await crewAIService.handleRequest(
          ctx.state.location.id,
          payload.message
        );

        isCampaignRequest = true;
      } catch (error) {
        logger.error({
          message: "Error generating marketing campaign plan",
          error,
          chat_id: chat.id,
        });
        // Continue with normal response generation if plan creation fails
      }
    } else if (intent.intent === "image_generation") {
      try {
        logger.info({
          message: "Image generation request detected",
          chat_id: chat.id,
          user_query: payload.message,
        });

        // Generate image using ImageGenerationService
        const imageService = new ImageGenerationService();
        generatedImageUrl = await imageService.generateImage(payload.message, {
          quality: "hd",
          size: "1024x1024",
          style: "vivid",
          high_fidelity: true,
          enhance_for_marketing: false, // Don't enhance user's image requests
          provider: "ideogram",
        });

        // Download and store the image permanently since API-provided URLs are short-lived
        try {
          // Use a descriptive title for the image
          const imageTitle = `Chat image - ${new Date().toISOString()}`;
          // Get user ID for folder structure, fallback to 'anonymous' if not available
          const userId = ctx.state.admin?.id || "anonymous";
          const folderPath = `chatimages/${userId}`;
          const storedImage = await imageDownloadService.downloadAndStoreImage(
            ctx.state.location.id,
            generatedImageUrl,
            imageTitle,
            folderPath
          );
          logger.info({
            message: "Stored generated image",
            temp_url: generatedImageUrl,
            stored_url: storedImage.url,
          });
          // Update the URL to the stored image's URL
          generatedImageUrl = storedImage.url;
          logger.info({
            message: "Image stored permanently",
            image_id: storedImage.uuid,
            url: generatedImageUrl,
          });
        } catch (storageError) {
          logger.error({
            message: "Failed to store image permanently, using temporary URL",
            error: storageError,
          });
          // Continue with the original URL as fallback
        }

        isImageRequest = true;
      } catch (error) {
        logger.error({
          message: "Error generating image",
          error,
          chat_id: chat.id,
        });
        // Continue with normal response generation if image creation fails
      }
    }

    let responseContent: string;
    let toolResults: any[] = [];

    // Check if we're using a fallback agent due to unavailable requested agent
    const isUsingFallbackAgent =
      selectedAgent.id === "general" && originalRequestedAgent;

    if (isUsingFallbackAgent && originalRequestedAgent) {
      // Log this event for tracking
      logger.info({
        message: "Used fallback agent due to unavailable requested agent",
        requested_agent: payload.agent_id,
        agent_name: originalRequestedAgent.name || payload.agent_id,
        missing_requirements: originalRequestedAgent.missingRequirements,
        chat_id: chat.id,
      });

      // Generate a personalized response explaining what's needed to unlock the agent
      responseContent = await this.generatePersonalizedFallbackResponse(
        originalRequestedAgent,
        ctx.state.location,
        payload.message
      );
    } else if (isCampaignRequest && campaignPlan) {
      // Generate a response introducing the plan
      const aiResult = await this.chatAIService!.generateResponse(
        chat,
        createdMessage,
        selectedAgent
      );
      responseContent = aiResult.content;
      toolResults = aiResult.toolResults || [];

      // We'll add the plan to the message metadata
    } else if (isImageRequest && generatedImageUrl) {
      // For image generation requests, create a special response with the image URL
      responseContent = `I've created an image based on your request. Here it is:\n\n![Generated Image](${generatedImageUrl})`;
    } else {
      // Generate AI response using the selected agent (standard approach)
      const aiResult = await this.chatAIService!.generateResponse(
        chat,
        createdMessage,
        selectedAgent
      );
      responseContent = aiResult.content;
      toolResults = aiResult.toolResults || [];
    }

    // Create and store AI message with correct metadata from the selected agent
    const aiMessage: Omit<Message, "id"> = {
      content: responseContent,
      role: "assistant",
      timestamp: new Date(),
      chat_id: chat.chat_id,
      agent_id: selectedAgent.id,
      metadata: {
        agent_name: selectedAgent.name,
        agent_role: selectedAgent.role,
        selected_by:
          intent.intent === "directed_message" ? "mention" : "intent",
        intent: intent.intent,
        requiredContextTypes: intent.entities?.requiredContextTypes || [],
        // Add information about fallback agent usage
        ...(isUsingFallbackAgent &&
          originalRequestedAgent && {
            originalRequestedAgent: {
              id: originalRequestedAgent.id,
              name: originalRequestedAgent.name,
              missingRequirements: originalRequestedAgent.missingRequirements,
            },
            isFallbackResponse: true,
          }),
      },
    };

    // Add plan data to message if available
    if (isCampaignRequest && campaignPlan) {
      aiMessage.metadata = {
        ...aiMessage.metadata,
        data: {
          type: "plan",
          plan: campaignPlan,
        },
      };
    }

    // Add image data to message if available
    if (isImageRequest && generatedImageUrl) {
      aiMessage.metadata = {
        ...aiMessage.metadata,
        data: {
          type: "image",
          images: [
            {
              url: generatedImageUrl,
              prompt: payload.message,
            },
          ],
        },
      };
    }

    // Add product recommendations to metadata if this is a product recommendation intent
    if (intent.intent === "product_recommendation") {
      try {
        // First, try to extract product recommendations from tool results
        let productRecommendations =
          this.extractProductRecommendationsFromToolResults(toolResults);

        // If no tool results, fall back to extracting from response text
        if (!productRecommendations || productRecommendations.length === 0) {
          productRecommendations =
            this.extractProductRecommendationsFromResponse(
              responseContent,
              intent
            );
        }

        if (productRecommendations && productRecommendations.length > 0) {
          const existingMetadata = aiMessage.metadata || {};
          const existingData = (existingMetadata.data as any) || {};

          aiMessage.metadata = {
            ...existingMetadata,
            data: {
              ...existingData,
              type: existingData.type || "product_recommendations",
              products: productRecommendations,
            },
          };

          logger.info({
            message: "Added product recommendations to message metadata",
            productCount: productRecommendations.length,
            chatId: payload.chat_id,
            source:
              productRecommendations[0]?.metadata?.source_type || "unknown",
          });
        }
      } catch (error) {
        logger.error("Error extracting product recommendations:", error);
        // Don't fail the entire response if product extraction fails
      }
    }

    const aiResponse = await this.chatService!.createMessage(aiMessage);

    // Make sure the agent is included in the chat object
    if (chat && (!chat.agents || chat.agents.length === 0) && selectedAgent) {
      chat.agents = [selectedAgent];
    }

    // Get or update chat message count in metadata
    const chatMetadata = chat.metadata || {};
    const messageCount = ((chatMetadata.messageCount as number) || 0) + 2; // Add 2 for user message and AI response
    const wasRenamed = chatMetadata.wasRenamed === true;

    // Update chat metadata with new message count
    const newMetadata: Record<string, any> = {
      messageCount,
      wasRenamed,
    };

    await this.chatService!.updateChatMetadata(chat.chat_id, newMetadata);

    // Generate chat name after first AI response (when message count is 2)
    // or regenerate every 5 messages if not manually renamed
    if (messageCount === 2 || (messageCount % 5 === 0 && !wasRenamed)) {
      try {
        // Get all messages for this chat
        const messages = await this.chatService!.getMessages(chat.chat_id);

        // Generate title based on messages
        const generatedTitle = await this.chatAIService!.generateTitle(
          messages
        );

        // Update chat name
        await this.chatService!.renameChat(
          chat.chat_id,
          generatedTitle,
          ctx.state.location.id
        );

        // Update the chat object for response
        chat.name = generatedTitle;
      } catch (error: any) {
        // Enhanced error handling for title generation
        let errorType = "unknown";

        if (
          error?.status === 429 ||
          error?.code === "insufficient_quota" ||
          error?.message?.includes("quota")
        ) {
          errorType = "quota_exceeded";
          logger.warn({
            message:
              "OpenAI quota exceeded during chat title generation - chat will keep default name",
            chatId: chat.chat_id,
            messageCount,
            quotaExceeded: true,
          });
        } else {
          logger.error({
            message: "Error generating chat title",
            errorType,
            error,
            chatId: chat.chat_id,
            messageCount,
          });
        }
      }
    }

    // Return both messages
    ctx.body = {
      chat: convertToChat(chat),
      messages: [createdMessage, aiResponse],
    };
  }

  async getAvailableAgents(
    ctx: ParameterizedContext<AppState, AppDefaultContext>
  ) {
    logger.info({ message: "Fetching available agents" });
    this.ensureServices();

    // Use the unified approach from IntegrationService
    const agentResults = await getAvailableAgents(
      this.app.db!,
      ctx.state.location.id.toString()
    );

    ctx.body = agentResults;
  }

  async updateChatMetadata(
    ctx: ParameterizedContext<AppState, AppDefaultContext>
  ) {
    this.ensureServices();
    const { id } = ctx.params;
    const metadata = ctx.request.body;

    if (!id || typeof metadata !== "object") {
      ctx.throw(400, "Invalid chat ID or metadata");
    }

    const chat = await this.chatService!.getChat(id, ctx.state.location.id);
    if (!chat) {
      ctx.throw(404, "Chat not found");
    }

    await this.chatService!.updateChatMetadata(id, metadata);
    ctx.status = 200;
    ctx.body = { success: true };
  }

  async generatePlanFromChat(
    ctx: ParameterizedContext<AppState, AppDefaultContext>
  ) {
    this.ensureServices();

    const { message, chat_id } = ctx.request.body;

    if (!message) {
      ctx.throw(400, "Message is required");
    }

    try {
      // Generate the automation plan using the location ID and message
      const plan = await crewAIService.handleRequest(
        ctx.state.location.id,
        message
      );

      ctx.body = {
        success: true,
        plan,
        message: "Automation plan generated successfully",
      };
    } catch (error) {
      logger.error({
        message: "Error generating automation plan from chat",
        error,
        chat_id,
        user_query: message,
      });

      ctx.status = 500;
      ctx.body = {
        success: false,
        message: "Failed to generate automation plan",
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async uploadFile(ctx: ParameterizedContext<AppState, AppDefaultContext>) {
    this.ensureServices();

    if (!this.app.db) {
      throw new Error("Database is not initialized");
    }

    try {
      // Get the uploaded file from the request
      const file = ctx.request.files?.file;

      if (!file) {
        ctx.status = 400;
        ctx.body = { error: "No file uploaded" };
        return;
      }

      // Extract location_id and chat_id from the request
      const locationId = ctx.request.body?.location_id || ctx.state.location.id;
      const chatId = ctx.request.body?.chat_id;

      // Validate chat belongs to location if chat_id is provided
      if (chatId) {
        const chat = await this.chatService!.getChat(chatId, locationId);
        if (!chat) {
          ctx.status = 404;
          ctx.body = { error: "Chat not found" };
          return;
        }
      }

      // Generate a unique filename
      const uniqueId = crypto.randomUUID();

      // Extract file information
      const originalFile = Array.isArray(file) ? file[0] : file;
      const originalName = originalFile.originalFilename || uniqueId;
      const contentType = originalFile.mimetype;
      const fileSize = originalFile.size;

      // Read the file content
      const fileContent = fs.readFileSync(originalFile.filepath);

      // Define upload directory structure
      const uploadBaseDir = path.join(process.cwd(), "uploads");
      const locationDir = path.join(uploadBaseDir, locationId.toString());
      const chatDir = chatId ? path.join(locationDir, chatId) : locationDir;

      // Create directory if it doesn't exist
      try {
        await fsAccess(chatDir);
      } catch (error) {
        await mkdirp(chatDir, { recursive: true });
      }

      // Generate a safe filename
      const fileExtension = path.extname(originalName);
      const safeFileName = `${uniqueId}${fileExtension}`;
      const filePath = path.join(chatDir, safeFileName);

      // Save the file
      await writeFile(filePath, fileContent);

      // Save file metadata to database - let the database assign the ID
      const fileMetadata = {
        original_name: originalName,
        content_type: contentType,
        size: fileSize,
        path: filePath,
        location_id: locationId,
        chat_id: chatId || null,
        uploaded_at: new Date(),
        uploaded_by: ctx.state.user?.id || null,
      };

      // Insert the record first
      await this.app.db("chat_attachments").insert(fileMetadata);

      // Get the last inserted ID using MySQL's LAST_INSERT_ID()
      const result = await this.app.db.raw("SELECT LAST_INSERT_ID() as id");
      // Handle different response formats from different MySQL drivers
      let insertedId;
      if (Array.isArray(result) && result.length > 0) {
        // Format: [[{id: 1}], ...]
        insertedId = result[0][0]?.id;
      } else if (result && typeof result === "object") {
        // Format: [{id: 1}, ...] or {id: 1}
        insertedId = Array.isArray(result) ? result[0]?.id : result.id;
      }

      if (!insertedId) {
        throw new Error("Failed to retrieve the last inserted ID");
      }

      // Generate a URL for the uploaded file using the database-assigned ID
      const fileUrl = `/api/admin/locations/${locationId}/chats/uploads/${insertedId}`;

      ctx.status = 200;
      ctx.body = {
        id: insertedId,
        name: originalName,
        type: contentType,
        size: fileSize,
        url: fileUrl,
      };
    } catch (error: any) {
      console.error("File upload error:", error);
      ctx.status = 500;
      ctx.body = { error: "File upload failed", details: error.message };
    }
  }

  async getUploadedFile(
    ctx: ParameterizedContext<AppState, AppDefaultContext>
  ) {
    this.ensureServices();

    if (!this.app.db) {
      throw new Error("Database is not initialized");
    }

    try {
      const fileId = parseInt(ctx.params.id, 10);

      if (isNaN(fileId)) {
        ctx.status = 400;
        ctx.body = { error: "Invalid file ID" };
        return;
      }

      // Fetch file metadata from database
      const fileMetadata = await this.app
        .db("chat_attachments")
        .where({ id: fileId })
        .first();

      if (!fileMetadata) {
        ctx.status = 404;
        ctx.body = { error: "File not found" };
        return;
      }

      // Check if user has access to the location
      if (fileMetadata.location_id !== ctx.state.location.id) {
        ctx.status = 403;
        ctx.body = { error: "Access denied" };
        return;
      }

      // Serve the file
      ctx.set("Content-Type", fileMetadata.content_type);
      ctx.set(
        "Content-Disposition",
        `inline; filename="${fileMetadata.original_name}"`
      );
      ctx.body = fs.createReadStream(fileMetadata.path);
    } catch (error: any) {
      console.error("File serving error:", error);
      ctx.status = 500;
      ctx.body = { error: "Failed to serve file", details: error.message };
    }
  }

  async getChatInsights(
    ctx: ParameterizedContext<AppState, AppDefaultContext>
  ) {
    this.ensureServices();

    try {
      const { timeframe = "30d" } = ctx.query;
      const locationId = ctx.state.location.id;

      logger.info({
        message: "Generating AI-powered market insights using SmokeyAIService",
        locationId,
        timeframe,
      });

      // Use the SmokeyAIService generateMarketInsights method directly
      const insights = await this.chatAIService!.generateMarketInsights(
        locationId,
        5
      );

      logger.info({
        message: "Successfully generated AI market insights",
        locationId,
        insightCount: insights.length,
        insightTypes: insights.map((i: any) => i.type),
      });

      ctx.body = insights;
    } catch (error) {
      logger.error({
        message: "Error generating AI chat insights",
        error: error instanceof Error ? error.message : String(error),
        locationId: ctx.state.location.id,
      });

      // Return fallback insights on error
      ctx.body = [
        {
          title: "Market Analysis",
          content: "Analyzing your local market conditions...",
          type: "market" as const,
          action: "Refresh",
        },
        {
          title: "Growth Opportunity",
          content: "Exploring expansion possibilities in your area",
          type: "performance" as const,
          action: "View Details",
        },
        {
          title: "Customer Trends",
          content: "Understanding local customer preferences",
          type: "demographics" as const,
          action: "View Details",
        },
        {
          title: "Competitive Edge",
          content: "Identifying differentiation strategies",
          type: "market" as const,
          action: "View Details",
        },
        {
          title: "Revenue Insights",
          content: "Analyzing revenue patterns and opportunities",
          type: "performance" as const,
          action: "View Details",
        },
      ];
    }
  }

  private async generatePersonalizedFallbackResponse(
    originalRequestedAgent: any,
    location: any,
    userMessage: string
  ): Promise<string> {
    // Check for model-related questions first and provide branded response
    if (this.isModelRelatedQuery(userMessage)) {
      return this.getModelBrandedResponse();
    }

    // Get location name, defaulting to "Your Business" if not available
    const locationName = location.name || "Your Business";

    // Map missing requirements to user-friendly actions
    const getActionableRequirements = (requirements: string[]) => {
      const actions: string[] = [];
      const hasGenericRequirement = requirements.some(
        (req) => req.includes("Missing") && req.includes("integration")
      );

      if (
        hasGenericRequirement ||
        requirements.some((req) => req.toLowerCase().includes("pos"))
      ) {
        actions.push(
          "📊 Connect your POS / ecommerce feed (menu, pricing, sell-thru)"
        );
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("customer") ||
            req.toLowerCase().includes("user")
        )
      ) {
        actions.push("👥 Upload customer data (profiles, purchase history)");
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("product") ||
            req.toLowerCase().includes("catalog")
        )
      ) {
        actions.push(
          "🛍️ Import your product catalog (descriptions, categories, pricing)"
        );
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("marketing") ||
            req.toLowerCase().includes("campaign")
        )
      ) {
        actions.push(
          "📈 Upload recent marketing data (email, SMS, social, ad performance)"
        );
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("lab") ||
            req.toLowerCase().includes("coa")
        )
      ) {
        actions.push("🧪 Upload lab results and COAs for compliance tracking");
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("competitor") ||
            req.toLowerCase().includes("market")
        )
      ) {
        actions.push("📊 Enable competitive intelligence data feeds");
      }

      if (
        requirements.some(
          (req) =>
            req.toLowerCase().includes("financial") ||
            req.toLowerCase().includes("budget")
        )
      ) {
        actions.push("💰 Connect financial data (revenue, costs, budgets)");
      }

      // If no specific requirements mapped, add generic data connection
      if (actions.length === 0) {
        actions.push(
          "📊 Connect your data integrations in Settings → Data Sources"
        );
      }

      return actions;
    };

    // Get agent-specific greeting and capabilities
    const getAgentPersonality = (agentName: string) => {
      switch (agentName.toLowerCase()) {
        case "craig":
          return {
            greeting:
              "Hey there! I'm Craig — your marketing automation specialist.",
            expertise:
              "campaign optimization, audience targeting, and growth strategies",
            unlockBenefits: [
              "🎯 Auto-draft high-converting campaigns in seconds",
              "📈 Surface hidden conversion opportunities",
              "🚀 A/B test content variations automatically",
              "📊 Track ROI across all marketing channels",
            ],
          };
        case "pops":
          return {
            greeting:
              "What's good! I'm Pops — your business intelligence guru.",
            expertise:
              "sales analytics, performance tracking, and strategic insights",
            unlockBenefits: [
              "📊 Surface hidden sales trends & breakout SKUs",
              "💡 Identify your most profitable customer segments",
              "📈 Predict inventory needs before stockouts",
              "🎯 Optimize pricing for maximum profitability",
            ],
          };
        case "deebo":
          return {
            greeting: "Yo! I'm Deebo — your compliance guardian.",
            expertise:
              "regulatory compliance, risk management, and quality control",
            unlockBenefits: [
              "⚠️ Flag compliance risks before regulators do",
              "📋 Auto-generate required regulatory reports",
              "🔍 Monitor product quality and lab results",
              "✅ Ensure all documentation meets state requirements",
            ],
          };
        case "ezal":
          return {
            greeting: "Hey! I'm Ezal — your market intelligence specialist.",
            expertise:
              "competitive analysis, market trends, and pricing optimization",
            unlockBenefits: [
              "🏆 Benchmark you vs. nearby competitors",
              "💰 Optimize pricing for market positioning",
              "📊 Track emerging market trends and opportunities",
              "🎯 Identify gaps in competitor offerings",
            ],
          };
        case "money mike":
          return {
            greeting: "What's up! I'm Money Mike — your financial strategist.",
            expertise:
              "financial analysis, profit optimization, and cost management",
            unlockBenefits: [
              "💰 Maximize profit margins across all products",
              "📊 Forecast cash flow and budget planning",
              "💡 Identify cost-saving opportunities",
              "📈 Track financial KPIs in real-time",
            ],
          };
        case "mrs. parker":
        case "mrs parker":
          return {
            greeting:
              "Hello! I'm Mrs. Parker — your customer relations specialist.",
            expertise:
              "customer loyalty, VIP management, and relationship building",
            unlockBenefits: [
              "👑 Identify and nurture your VIP customers",
              "💝 Design personalized loyalty programs",
              "📞 Proactive customer service insights",
              "🎯 Increase customer lifetime value",
            ],
          };
        default:
          return {
            greeting: "Hey! I'm Smokey — your on-call growth strategist.",
            expertise:
              "campaign ideas, competitive intel, compliance questions, and growth scenarios",
            unlockBenefits: [
              "📊 Surface hidden sales trends & breakout SKUs",
              "🎯 Auto-draft high-converting campaigns in seconds",
              "⚠️ Flag compliance risks before regulators do",
              "🏆 Benchmark you vs. nearby competitors",
            ],
          };
      }
    };

    const agentPersonality = getAgentPersonality(originalRequestedAgent.name);
    const actionableRequirements = getActionableRequirements(
      originalRequestedAgent.missingRequirements || []
    );

    // Create the personalized response
    const response = `${agentPersonality.greeting}

I specialize in ${agentPersonality.expertise} for ${locationName}.

**Want data-driven answers?** Connect these data sources and I'll pull live insights straight from your stack:

${actionableRequirements.map((action) => `• ${action}`).join("\n")}

**Why bother?** Because once we're synced I can:

${agentPersonality.unlockBenefits.map((benefit) => `${benefit}`).join("\n")}

**No data yet? No worries.** I can still help with strategy, brainstorm ideas, or break down the latest market shifts — just type your question below.

Ready to unlock the full playbook? Let's make ${locationName} the dispensary everyone benchmarks against! 🚀

---

*In the meantime, I'm routing your question to our general assistant who can help with basic inquiries.*`;

    return response;
  }

  private isModelRelatedQuery(query: string): boolean {
    // Detect questions about the AI model, training, architecture, etc.
    const modelKeywords = [
      /what\s+(ai\s+)?model\s+(are\s+you|do\s+you)\s+using/i,
      /which\s+(ai\s+)?model\s+(are\s+you|do\s+you)\s+use/i,
      /what\s+(language\s+)?model\s+(are\s+you|is\s+this)/i,
      /are\s+you\s+(gpt|chatgpt|claude|llama|gemini)/i,
      /what\s+(version\s+of\s+)?(gpt|openai|claude|anthropic)/i,
      /what\s+(ai\s+)?system\s+(are\s+you|do\s+you)\s+running/i,
      /what\s+(training\s+)?data\s+(do\s+you|were\s+you)/i,
      /how\s+(were\s+you|are\s+you)\s+trained/i,
      /what\s+technology\s+(powers|runs)\s+you/i,
      /what\s+(ai\s+)?engine\s+(are\s+you|do\s+you)\s+use/i,
      /what\s+kind\s+of\s+(ai|model|system)\s+are\s+you/i,
      /tell\s+me\s+about\s+your\s+(model|training|ai)/i,
    ];

    return modelKeywords.some((pattern) => pattern.test(query));
  }

  private getModelBrandedResponse(): string {
    return `I'm powered by **BakedBot's custom cannabis-specialized AI models** that have been specifically trained and fine-tuned for the cannabis industry.

**What makes our models special:**
• 🌿 **Cannabis-specific training**: Trained on extensive cannabis industry data, regulations, and best practices
• 📊 **Industry expertise**: Specialized knowledge in dispensary operations, compliance, product knowledge, and market trends  
• 🔬 **Product intelligence**: Deep understanding of cannabinoids, terpenes, strains, and effects
• 📈 **Business focus**: Optimized for cannabis retail, marketing, compliance, and growth strategies

**Our AI stack includes:**
• Custom language models fine-tuned for cannabis terminology and context
• Specialized product recommendation engines trained on cannabis consumer preferences
• Compliance-aware responses that understand state-by-state regulations
• Industry-specific integrations with POS systems, Metrc, and cannabis data sources

This isn't generic AI—it's cannabis intelligence built specifically for dispensaries like yours. That's why I can provide accurate strain recommendations, compliance guidance, and industry-specific insights that generic AI models simply can't match.

Ready to see what cannabis-specialized AI can do for your business? 🚀`;
  }

  private extractProductRecommendationsFromResponse(
    responseContent: string,
    intent: any
  ): any[] {
    try {
      // First, try to extract from tool response if this was a product recommendation
      // The ProductRecommendationTool should have returned structured data
      // For now, we'll fall back to parsing the text response

      // Look for product names mentioned in the response
      const productNames = this.extractProductNamesFromText(responseContent);

      if (productNames.length === 0) {
        return [];
      }

      // Format products as recommendation objects with basic info
      // Note: In the future, we should get this data directly from the tool response
      const recommendations = productNames.map((name) => ({
        product_name: name,
        source: "ai_suggestion",
        description: `Product mentioned in AI response`,
        recommendation_reason: "Recommended by AI based on your query",
        // Add placeholder metadata that would come from ProductDataVectorService
        metadata: {
          source_type: "extracted_from_response",
        },
      }));

      return recommendations;
    } catch (error) {
      logger.error(
        "Error extracting product recommendations from response:",
        error
      );
      return [];
    }
  }

  private extractProductNamesFromText(text: string): string[] {
    // Extract product names mentioned in bullet points or lists
    const bulletPointRegex = /[-•]\s*([A-Z][^.!?\n]+)/g;
    const productNames: string[] = [];
    let match;

    while ((match = bulletPointRegex.exec(text)) !== null) {
      const productName = match[1].trim();

      // Filter out obvious non-product text
      if (
        !productName.toLowerCase().includes("these strains") &&
        !productName.toLowerCase().includes("popular cannabis") &&
        !productName.toLowerCase().includes("cannabis strains") &&
        productName.length > 3 &&
        productName.length < 50
      ) {
        productNames.push(productName);
      }
    }

    return productNames.slice(0, 10); // Limit to 10 products
  }

  private extractProductRecommendationsFromToolResults(
    toolResults: any[]
  ): any[] {
    try {
      if (!toolResults || toolResults.length === 0) {
        logger.info("No tool results provided for product extraction");
        return [];
      }

      logger.info({
        message: "Debugging tool results for product extraction",
        toolResultsCount: toolResults.length,
        toolResults: toolResults.map((tr, index) => ({
          index,
          status: tr.status,
          hasData: !!tr.data,
          dataKeys: tr.data ? Object.keys(tr.data) : [],
          queryInfo: tr.data?.query_info,
          searchType: tr.data?.query_info?.search_type,
          resultsCount: tr.data?.results?.length || 0,
        })),
      });

      // Look for ProductRecommendationTool results OR enhanced VectorSearchTool results
      for (const toolResult of toolResults) {
        // Check if this is a successful product recommendation result
        if (
          toolResult.status === "success" &&
          toolResult.data &&
          toolResult.data.recommendations &&
          Array.isArray(toolResult.data.recommendations)
        ) {
          logger.info({
            message: "Found product recommendations in tool results",
            count: toolResult.data.recommendations.length,
            query_info: toolResult.data.query_info,
          });

          // Return the recommendations with full metadata from ProductDataVectorService
          return toolResult.data.recommendations.map((rec: any) => ({
            // Core product information
            product_name: rec.product_name,
            meta_sku: rec.meta_sku,
            retailer_id: rec.retailer_id,
            brand_name: rec.brand_name,
            category: rec.category,
            subcategory: rec.subcategory,

            // Pricing and availability
            price: rec.price,
            latest_price: rec.price, // Alias for compatibility

            // Cannabinoid content
            thc_percentage: rec.thc_percentage,
            cbd_percentage: rec.cbd_percentage,

            // Product details
            description: rec.description,
            image_url: rec.image_url,

            // User experience data
            rating: rec.rating,
            reviews_count: rec.reviews_count,

            // Product attributes
            tags: rec.tags,
            mood: rec.mood,

            // Medical/recreational availability
            medical: rec.medical,
            recreational: rec.recreational,

            // Recommendation context
            recommendation_reason: rec.recommendation_reason,
            score: rec.score,

            // Source information
            source: "product_recommendation_tool",

            // Include all metadata for frontend flexibility
            metadata: {
              ...rec.metadata,
              source_type: "product_recommendation_tool",
              tool_query_info: toolResult.data.query_info,
            },
          }));
        }

        // Check if this is an enhanced vector_search.query result with product data
        if (
          toolResult.status === "success" &&
          toolResult.data &&
          toolResult.data.results &&
          Array.isArray(toolResult.data.results) &&
          toolResult.data.query_info?.search_type === "product_search"
        ) {
          logger.info({
            message: "Found product data in enhanced vector search results",
            count: toolResult.data.results.length,
            query_info: toolResult.data.query_info,
          });

          // Return the products with full metadata from ProductDataVectorService
          return toolResult.data.results.map((result: any) => ({
            // Use product_data if available, otherwise fallback to metadata
            product_name:
              result.product_data?.product_name ||
              result.metadata?.product_name,
            meta_sku:
              result.product_data?.meta_sku || result.metadata?.meta_sku,
            retailer_id:
              result.product_data?.retailer_id || result.metadata?.retailer_id,
            brand_name:
              result.product_data?.brand_name || result.metadata?.brand_name,
            category:
              result.product_data?.category || result.metadata?.category,
            subcategory:
              result.product_data?.subcategory || result.metadata?.subcategory,

            // Pricing and availability
            price: result.product_data?.price || result.metadata?.latest_price,
            latest_price:
              result.product_data?.price || result.metadata?.latest_price,

            // Cannabinoid content
            thc_percentage: result.product_data?.thc || result.metadata?.thc,
            cbd_percentage: result.product_data?.cbd || result.metadata?.cbd,

            // Product details
            description:
              result.product_data?.description || result.metadata?.description,
            image_url:
              result.product_data?.image_url || result.metadata?.image_url,

            // User experience data
            rating: result.product_data?.rating || result.metadata?.rating,
            reviews_count:
              result.product_data?.reviews_count ||
              result.metadata?.reviews_count,

            // Product attributes
            tags: result.product_data?.tags || result.metadata?.tags,
            mood: result.product_data?.mood || result.metadata?.mood,

            // Medical/recreational availability
            medical: result.product_data?.medical || result.metadata?.medical,
            recreational:
              result.product_data?.recreational ||
              result.metadata?.recreational,

            // Recommendation context
            recommendation_reason: `Found via semantic search (${Math.round(
              result.score * 100
            )}% match)`,
            score: result.score,

            // Source information
            source: "enhanced_vector_search",

            // Include all metadata for frontend flexibility
            metadata: {
              ...result.metadata,
              source_type: "enhanced_vector_search",
              tool_query_info: toolResult.data.query_info,
              product_data: result.product_data,
            },
          }));
        }
      }

      logger.warn({
        message:
          "No product data found in tool results - falling back to text extraction",
        toolResultsStructure: toolResults.map((tr) => ({
          status: tr.status,
          hasData: !!tr.data,
          hasResults: !!tr.data?.results,
          hasQueryInfo: !!tr.data?.query_info,
          searchType: tr.data?.query_info?.search_type,
        })),
      });

      return [];
    } catch (error) {
      logger.error(
        "Error extracting product recommendations from tool results:",
        error
      );
      return [];
    }
  }

  // Setup routes for the controller
  setupRoutes(router: Router) {
    router.get("/", this.listChats.bind(this));
    router.post("/", this.createChat.bind(this));
    router.get("/agents", this.getAvailableAgents.bind(this));
    router.get("/insights", this.getChatInsights.bind(this));
    router.get("/:id", this.getChat.bind(this));
    router.put("/:id", this.updateChat.bind(this));
    router.delete("/:id", this.deleteChat.bind(this));
    router.post("/:id/archive", this.archiveChat.bind(this));
    router.get("/:id/messages", this.getMessages.bind(this));
    router.post("/messages", this.sendMessage.bind(this));
    router.put("/:id/metadata", this.updateChatMetadata.bind(this));
    router.post("/generate-plan", this.generatePlanFromChat.bind(this));

    // Add new routes for file uploads
    router.post("/uploads", this.uploadFile.bind(this));
    router.get("/uploads/:id", this.getUploadedFile.bind(this));
  }
}

// Setup routes
const router = new Router({
  prefix: "/chats",
});

export function setupChatRoutes(app: App) {
  const controller = new ChatController(app);

  // Add location role middleware with minimum role of "support"
  router.use(locationRoleMiddleware("support"));

  // Routes
  controller.setupRoutes(router);

  return router;
}
