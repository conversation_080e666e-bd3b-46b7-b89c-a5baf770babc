/**
 * @swagger
 * tags:
 *   name: Public Order
 *   description: |
 *     Public order viewing endpoints that require no authentication.
 *     Orders are accessed via secure hash instead of order ID.
 */

import Router from "@koa/router";
import { Context } from "koa";
import { logger } from "../config/logger";
import { OrderHashService } from "./OrderHashService";
import { RequestError } from "../core/errors";
import App from "../app";

const router = new Router({
  prefix: "/public/orders",
});

/**
 * @swagger
 * /public/orders/{hash}:
 *   get:
 *     summary: View Order by Hash (No Auth Required)
 *     description: |
 *       Retrieves order details using a secure hash instead of order ID.
 *       This endpoint requires no authentication and is intended for customers
 *       to view their orders via links sent in email notifications.
 *     tags: [Public Order]
 *     parameters:
 *       - in: path
 *         name: hash
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[a-f0-9]{32}$'
 *         description: Secure 32-character hash for the order
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, html]
 *           default: html
 *         description: Response format (JSON for API access, HTML for browser viewing)
 *     responses:
 *       200:
 *         description: Order details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Order'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/OrderItem'
 *           text/html:
 *             schema:
 *               type: string
 *               description: HTML page displaying order details
 *       400:
 *         description: Invalid hash format
 *       404:
 *         description: Order not found
 *       500:
 *         description: Internal server error
 */
router.get("/:hash", async (ctx: Context) => {
  try {
    const hash = ctx.params.hash;
    const format = (ctx.query.format as string) || "html";

    // Validate hash format
    if (!OrderHashService.isValidHash(hash)) {
      logger.warn({
        message: "Invalid order hash format",
        hash: hash.substring(0, 8) + "...",
        ip: ctx.ip,
      });
      throw new RequestError("Invalid order hash format", 400);
    }

    // Find order by hash
    const order = await App.main
      .db("orders")
      .leftJoin("users", "orders.user_id", "users.id")
      .leftJoin("locations", "orders.location_id", "locations.id")
      .where("orders.order_hash", hash)
      .select(
        "orders.*",
        "users.email as user_email",
        "users.phone as user_phone",
        "users.data as user_data",
        "locations.name as location_name",
        "locations.data as location_data"
      )
      .first();

    if (!order) {
      logger.warn({
        message: "Order not found with hash",
        hash: hash.substring(0, 8) + "...",
        ip: ctx.ip,
      });

      if (format === "json") {
        ctx.status = 404;
        ctx.body = { error: "Order not found" };
        return;
      } else {
        ctx.status = 404;
        ctx.type = "text/html";
        ctx.body = generateNotFoundHtml();
        return;
      }
    }

    // Get order items
    const items = await App.main
      .db("order_items")
      .where("order_id", order.id)
      .select("*");

    // Parse and prepare numeric fields
    const parsedItems = items.map((item: any) => {
      // Parse product_data JSON if it's a string
      let productData = null;
      try {
        productData =
          typeof item.product_data === "string"
            ? JSON.parse(item.product_data)
            : item.product_data;
      } catch (e) {
        productData = item.product_data;
      }

      return {
        ...item,
        // Parse numeric fields that might come as strings from DB
        id: parseInt(item.id) || 0,
        order_id: parseInt(item.order_id) || 0,
        quantity: parseInt(item.quantity) || 0,
        unit_price: parseFloat(item.unit_price) || 0,
        total_price: parseFloat(item.total_price) || 0,
        // Parse JSON fields
        product_data: productData,
      };
    });

    // Parse JSON fields that might come as strings from DB
    let shippingAddress = null;
    let billingAddress = null;
    let userData = null;
    let locationData = null;

    try {
      shippingAddress =
        typeof order.shipping_address === "string"
          ? JSON.parse(order.shipping_address)
          : order.shipping_address;
    } catch (e) {
      shippingAddress = order.shipping_address;
    }

    try {
      billingAddress =
        typeof order.billing_address === "string"
          ? JSON.parse(order.billing_address)
          : order.billing_address;
    } catch (e) {
      billingAddress = order.billing_address;
    }

    try {
      userData =
        typeof order.user_data === "string"
          ? JSON.parse(order.user_data)
          : order.user_data;
    } catch (e) {
      userData = order.user_data;
    }

    try {
      locationData =
        typeof order.location_data === "string"
          ? JSON.parse(order.location_data)
          : order.location_data;
    } catch (e) {
      locationData = order.location_data;
    }

    // Prepare order data
    const orderData = {
      ...order,
      // Parse numeric fields that might come as strings from DB
      id: parseInt(order.id) || 0,
      user_id: parseInt(order.user_id) || 0,
      location_id: parseInt(order.location_id) || 0,
      total_amount: parseFloat(order.total_amount) || 0,
      // Parse JSON fields
      shipping_address: shippingAddress,
      billing_address: billingAddress,
      user: {
        email: order.user_email,
        phone: order.user_phone,
        first_name: userData?.first_name,
        last_name: userData?.last_name,
      },
      location: {
        name: order.location_name,
        data: locationData,
      },
      items: parsedItems,
      // Remove joined fields
      user_email: undefined,
      user_phone: undefined,
      user_data: undefined,
      location_name: undefined,
      location_data: undefined,
    };

    logger.info({
      message: "Public order viewed",
      order_id: order.id,
      location_id: order.location_id,
      format,
      ip: ctx.ip,
    });

    if (format === "json") {
      ctx.body = orderData;
    } else {
      ctx.type = "text/html";
      ctx.body = generateOrderHtml(orderData);
    }
  } catch (error) {
    logger.error({
      message: "Error in public order view",
      error: error instanceof Error ? error.message : "Unknown error",
      hash: ctx.params.hash?.substring(0, 8) + "...",
      ip: ctx.ip,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 400;
      if ((ctx.query.format as string) === "json") {
        ctx.body = { error: error.message };
      } else {
        ctx.type = "text/html";
        ctx.body = generateErrorHtml(error.message);
      }
    } else {
      ctx.status = 500;
      if ((ctx.query.format as string) === "json") {
        ctx.body = { error: "An error occurred while retrieving the order" };
      } else {
        ctx.type = "text/html";
        ctx.body = generateErrorHtml(
          "An error occurred while retrieving the order"
        );
      }
    }
  }
});

// HTML generation functions
function generateOrderHtml(order: any): string {
  const customerName =
    `${order.user?.first_name || ""} ${order.user?.last_name || ""}`.trim() ||
    "Customer";
  const locationName = order.location?.name || "Store";

  // Ensure total_amount is a number (in case it comes from DB as string)
  const totalAmount = parseFloat(order.total_amount) || 0;

  // Calculate totals
  let subtotal = 0;
  const itemsHtml = order.items
    .map((item: any) => {
      // Ensure unit_price is a number (in case it comes from DB as string)
      const unitPrice = parseFloat(item.unit_price) || 0;
      const quantity = parseInt(item.quantity) || 0;
      const itemTotal = unitPrice * quantity;
      subtotal += itemTotal;
      return `
        <tr>
          <td style="padding: 12px; border-bottom: 1px solid #e5e7eb;">
            <div style="font-weight: 600; color: #111827;">${
              item.product_data?.product_name || "Product"
            }</div>
            ${
              item.product_data?.weight
                ? `<div style="font-size: 14px; color: #6b7280;">${item.product_data.weight}</div>`
                : ""
            }
          </td>
          <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: center;">${quantity}</td>
          <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: right;">$${unitPrice.toFixed(
            2
          )}</td>
          <td style="padding: 12px; border-bottom: 1px solid #e5e7eb; text-align: right; font-weight: 600;">$${itemTotal.toFixed(
            2
          )}</td>
        </tr>
      `;
    })
    .join("");

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order #${order.id} - ${locationName}</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f9fafb;
        }
        .container {
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        .header {
          background: #6366f1;
          color: white;
          padding: 24px;
          text-align: center;
        }
        .content {
          padding: 24px;
        }
        .order-info {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 30px;
        }
        .info-section h3 {
          margin: 0 0 8px 0;
          color: #374151;
          font-size: 16px;
        }
        .info-section p {
          margin: 4px 0;
          color: #6b7280;
        }
        .status {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 600;
          text-transform: capitalize;
        }
        .status.pending { background: #fef3c7; color: #92400e; }
        .status.processing { background: #dbeafe; color: #1e40af; }
        .status.completed { background: #d1fae5; color: #047857; }
        .status.cancelled { background: #fee2e2; color: #dc2626; }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }
        th {
          background: #f9fafb;
          padding: 12px;
          text-align: left;
          font-weight: 600;
          color: #374151;
          border-bottom: 2px solid #e5e7eb;
        }
        th:last-child, td:last-child {
          text-align: right;
        }
        .total-row {
          background: #f9fafb;
          font-weight: 600;
        }
        .total-row td {
          padding: 16px 12px;
          border-top: 2px solid #e5e7eb;
        }
        .address {
          background: #f9fafb;
          padding: 16px;
          border-radius: 6px;
          margin-top: 16px;
        }
        @media (max-width: 640px) {
          .order-info {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          body {
            padding: 10px;
          }
          th, td {
            padding: 8px;
            font-size: 14px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1 style="margin: 0; font-size: 28px;">Order #${order.id}</h1>
          <p style="margin: 8px 0 0 0; opacity: 0.9;">${locationName}</p>
        </div>
        
        <div class="content">
          <div class="order-info">
            <div class="info-section">
              <h3>Order Details</h3>
              <p><strong>Status:</strong> <span class="status ${
                order.status
              }">${order.status}</span></p>
              <p><strong>Order Date:</strong> ${new Date(
                order.created_at
              ).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}</p>
              <p><strong>Total:</strong> $${totalAmount.toFixed(2)}</p>
            </div>
            
            <div class="info-section">
              <h3>Customer Information</h3>
              <p><strong>Name:</strong> ${customerName}</p>
              ${
                order.user?.email
                  ? `<p><strong>Email:</strong> ${order.user.email}</p>`
                  : ""
              }
              ${
                order.user?.phone
                  ? `<p><strong>Phone:</strong> ${order.user.phone}</p>`
                  : ""
              }
            </div>
          </div>

          <h3>Order Items</h3>
          <table>
            <thead>
              <tr>
                <th>Product</th>
                <th style="text-align: center;">Quantity</th>
                <th>Price</th>
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr class="total-row">
                <td colspan="3"><strong>Total</strong></td>
                <td><strong>$${totalAmount.toFixed(2)}</strong></td>
              </tr>
            </tbody>
          </table>

          ${
            order.shipping_address
              ? `
            <h3>Shipping Address</h3>
            <div class="address">
              <p><strong>${order.shipping_address.name}</strong></p>
              <p>${order.shipping_address.line1}</p>
              ${
                order.shipping_address.line2
                  ? `<p>${order.shipping_address.line2}</p>`
                  : ""
              }
              <p>${order.shipping_address.city}, ${
                  order.shipping_address.state
                } ${order.shipping_address.postal_code}</p>
              <p>${order.shipping_address.country}</p>
              ${
                order.shipping_address.phone
                  ? `<p>Phone: ${order.shipping_address.phone}</p>`
                  : ""
              }
            </div>
          `
              : ""
          }

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280;">
            <p>Thank you for your order! If you have any questions, please contact the store directly.</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generateNotFoundHtml(): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order Not Found</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          margin: 0;
          background-color: #f9fafb;
        }
        .container {
          text-align: center;
          padding: 40px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          max-width: 400px;
        }
        h1 { color: #dc2626; margin-bottom: 16px; }
        p { color: #6b7280; line-height: 1.6; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Order Not Found</h1>
        <p>The order you're looking for could not be found. The link may be expired or invalid.</p>
        <p>Please check your email for the correct order link or contact the store for assistance.</p>
      </div>
    </body>
    </html>
  `;
}

function generateErrorHtml(message: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Error</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
          margin: 0;
          background-color: #f9fafb;
        }
        .container {
          text-align: center;
          padding: 40px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          max-width: 400px;
        }
        h1 { color: #dc2626; margin-bottom: 16px; }
        p { color: #6b7280; line-height: 1.6; }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>Error</h1>
        <p>${message}</p>
        <p>Please try again later or contact support if the problem persists.</p>
      </div>
    </body>
    </html>
  `;
}

export default router;
