import crypto from "crypto";
import { logger } from "../config/logger";

export class OrderHashService {
  private static readonly HASH_SALT =
    process.env.ORDER_HASH_SALT || "default-order-salt-change-in-production";

  private static readonly HASH_LENGTH = 32;

  /**
   * Generate a secure hash for an order
   * Uses order ID, location ID, and created timestamp for uniqueness
   */
  static generateOrderHash(
    orderId: number,
    locationId: number,
    createdAt: Date
  ): string {
    const data = `${orderId}-${locationId}-${createdAt.getTime()}-${
      this.HASH_SALT
    }`;

    const hash = crypto
      .createHash("sha256")
      .update(data)
      .digest("hex")
      .substring(0, this.HASH_LENGTH);

    logger.info({
      message: "Generated order hash",
      order_id: orderId,
      location_id: locationId,
      hash_length: hash.length,
    });

    return hash;
  }

  /**
   * Generate a hash from order data (for existing orders)
   */
  static generateHashFromOrder(order: {
    id: number;
    location_id: number;
    created_at: Date | string;
  }): string {
    const createdAt =
      typeof order.created_at === "string"
        ? new Date(order.created_at)
        : order.created_at;
    return this.generateOrderHash(order.id, order.location_id, createdAt);
  }

  /**
   * Validate hash format (basic security check)
   */
  static isValidHash(hash: string): boolean {
    return /^[a-f0-9]{32}$/.test(hash);
  }
}
