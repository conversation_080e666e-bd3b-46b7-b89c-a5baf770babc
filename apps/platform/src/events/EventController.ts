/**
 * @swagger
 * tags:
 *   name: Events
 *   description: Event management endpoints for location-specific events
 */

import Router from "@koa/router";
import { Context } from "koa";
import { validateBody } from "../misc/MiscMiddleware";
import {
  createEventRequestSchema,
  updateEventRequestSchema,
  getEventsRequestSchema,
} from "../core/schemas";
import { SupabaseService } from "../supabase/SupabaseService";
import { logger } from "../config/logger";
import { v4 as uuidv4 } from "uuid";
import { RequestError } from "../core/errors";
import { locationRoleMiddleware } from "../locations/LocationService";

const router = new Router({
  prefix: "/events",
});

// Helper function to get Supabase service
const getSupabaseService = () => {
  return new SupabaseService({
    url: process.env.SUPABASE_URL || "",
    key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
    bucket: process.env.SUPABASE_BUCKET || "location-data",
  });
};

/**
 * @swagger
 * /events:
 *   get:
 *     summary: Get Events for Location
 *     description: Get all events for a specific location with optional filtering
 *     tags: [Events]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search query for event name
 *       - in: query
 *         name: future_only
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Only show future events
 *     responses:
 *       200:
 *         description: List of events
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 events:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Event'
 *                 total_count:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 total_pages:
 *                   type: integer
 */
router.get("/", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const { page = 1, limit = 20, search, future_only = "true" } = ctx.query;

    const supabaseService = getSupabaseService();

    // Get location-specific events
    const result = await supabaseService.getLocationEvents(locationId, {
      search: search as string,
      futureOnly: future_only === "true",
      page: parseInt(page as string) || 1,
      limit: parseInt(limit as string) || 20,
    });

    ctx.status = 200;
    ctx.body = result;
  } catch (error) {
    logger.error("Error getting location events:", error);
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 400;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get events",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
});

/**
 * @swagger
 * /events:
 *   post:
 *     summary: Create Event
 *     description: Create a new event for the specified location
 *     tags: [Events]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateEventRequest'
 *     responses:
 *       201:
 *         description: Event created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Event'
 *       400:
 *         description: Invalid request data
 */
router.post(
  "/",
  locationRoleMiddleware("editor"),
  validateBody(createEventRequestSchema),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const eventData = ctx.request.body;

      const supabaseService = getSupabaseService();

      // Create event with location association
      const newEvent = {
        event_id: uuidv4(),
        job_id: `user_created_${locationId}_${Date.now()}`,
        user_id: ctx.state.user?.id || "system",
        location_id: locationId,
        event_name: eventData.event_name,
        category: eventData.category || [],
        start_time: eventData.start_time,
        timezone: eventData.timezone,
        host: eventData.host,
        starting_price: eventData.starting_price,
        address: eventData.address,
        city: eventData.city,
        state: eventData.state,
        postal_code: eventData.postal_code,
        image: eventData.image,
        url: eventData.url || "",
        source: ctx.state.location.name,
        data: {
          created_by_user: true,
          created_at: new Date().toISOString(),
        },
      };

      const result = await supabaseService.createEvent(newEvent);

      ctx.status = 201;
      ctx.body = result;
    } catch (error) {
      logger.error("Error creating event:", error);
      if (error instanceof RequestError) {
        ctx.status = error.statusCode || 400;
        ctx.body = { error: error.message };
      } else {
        ctx.status = 500;
        ctx.body = {
          error: "Failed to create event",
          details: error instanceof Error ? error.message : String(error),
        };
      }
    }
  }
);

/**
 * @swagger
 * /events/{eventId}:
 *   get:
 *     summary: Get Event Details
 *     description: Get details of a specific event
 *     tags: [Events]
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         schema:
 *           type: string
 *         description: Event ID
 *     responses:
 *       200:
 *         description: Event details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Event'
 *       404:
 *         description: Event not found
 */
router.get("/:eventId", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const { eventId } = ctx.params;

    const supabaseService = getSupabaseService();
    const event = await supabaseService.getEventById(eventId, locationId);

    if (!event) {
      ctx.status = 404;
      ctx.body = { error: "Event not found" };
      return;
    }

    ctx.status = 200;
    ctx.body = event;
  } catch (error) {
    logger.error("Error getting event:", error);
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 400;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get event",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
});

/**
 * @swagger
 * /events/{eventId}:
 *   put:
 *     summary: Update Event
 *     description: Update an existing event (only events owned by the location)
 *     tags: [Events]
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         schema:
 *           type: string
 *         description: Event ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateEventRequest'
 *     responses:
 *       200:
 *         description: Event updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Event'
 *       404:
 *         description: Event not found or not owned by location
 */
router.put(
  "/:eventId",
  locationRoleMiddleware("editor"),
  validateBody(updateEventRequestSchema),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const { eventId } = ctx.params;
      const updateData = ctx.request.body;

      const supabaseService = getSupabaseService();

      // Verify event exists and belongs to this location
      const existingEvent = await supabaseService.getEventById(
        eventId,
        locationId
      );
      if (!existingEvent || existingEvent.location_id !== locationId) {
        ctx.status = 404;
        ctx.body = { error: "Event not found or not owned by this location" };
        return;
      }

      // Update the event
      const updatedEvent = await supabaseService.updateEvent(
        eventId,
        locationId,
        updateData
      );

      ctx.status = 200;
      ctx.body = updatedEvent;
    } catch (error) {
      logger.error("Error updating event:", error);
      if (error instanceof RequestError) {
        ctx.status = error.statusCode || 400;
        ctx.body = { error: error.message };
      } else {
        ctx.status = 500;
        ctx.body = {
          error: "Failed to update event",
          details: error instanceof Error ? error.message : String(error),
        };
      }
    }
  }
);

/**
 * @swagger
 * /events/{eventId}:
 *   delete:
 *     summary: Delete Event
 *     description: Delete an event (only events owned by the location)
 *     tags: [Events]
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         schema:
 *           type: string
 *         description: Event ID
 *     responses:
 *       200:
 *         description: Event deleted successfully
 *       404:
 *         description: Event not found or not owned by location
 */
router.delete("/:eventId", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const { eventId } = ctx.params;

    const supabaseService = getSupabaseService();

    // Verify event exists and belongs to this location
    const existingEvent = await supabaseService.getEventById(
      eventId,
      locationId
    );
    if (!existingEvent || existingEvent.location_id !== locationId) {
      ctx.status = 404;
      ctx.body = { error: "Event not found or not owned by this location" };
      return;
    }

    // Delete the event
    await supabaseService.deleteEvent(eventId, locationId);

    ctx.status = 200;
    ctx.body = { message: "Event deleted successfully" };
  } catch (error) {
    logger.error("Error deleting event:", error);
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 400;
      ctx.body = { error: error.message };
    } else {
      ctx.status = 500;
      ctx.body = {
        error: "Failed to delete event",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
});

export default router;
