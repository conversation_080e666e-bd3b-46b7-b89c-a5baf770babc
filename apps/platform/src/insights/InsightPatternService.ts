import { InsightPattern } from "./InsightPattern";
import { logger } from "../config/logger";

export class InsightPatternService {
  private static instance: InsightPatternService;
  private cachedPatterns: InsightPattern[] | null = null;
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static getInstance(): InsightPatternService {
    if (!InsightPatternService.instance) {
      InsightPatternService.instance = new InsightPatternService();
    }
    return InsightPatternService.instance;
  }

  /**
   * Get all active patterns, ordered by priority (highest first)
   */
  async getActivePatterns(): Promise<InsightPattern[]> {
    // Check cache first
    if (this.cachedPatterns && Date.now() < this.cacheExpiry) {
      return this.cachedPatterns;
    }

    try {
      const patterns = await InsightPattern.all((qb) =>
        qb.where("is_active", true)
          .orderBy("priority", "desc")
          .orderBy("created_at", "asc")
      );

      // Update cache
      this.cachedPatterns = patterns;
      this.cacheExpiry = Date.now() + this.CACHE_DURATION;

      return patterns;
    } catch (error) {
      logger.error("Error fetching active patterns:", error);
      return [];
    }
  }

  /**
   * Get all patterns (for admin UI)
   */
  async getAllPatterns(): Promise<InsightPattern[]> {
    try {
      return await InsightPattern.all((qb) =>
        qb.orderBy("priority", "desc")
          .orderBy("created_at", "asc")
      );
    } catch (error) {
      logger.error("Error fetching all patterns:", error);
      return [];
    }
  }

  /**
   * Check if text matches any non-actionable patterns
   */
  async isNonActionable(title: string, description: string = ""): Promise<boolean> {
    const patterns = await this.getActivePatterns();
    const nonActionablePatterns = patterns.filter(p => p.type === "non_actionable");

    const textToCheck = `${title} ${description}`.toLowerCase();

    return nonActionablePatterns.some(pattern => pattern.matches(textToCheck));
  }

  /**
   * Check if text matches any actionable patterns
   */
  async isActionable(title: string, description: string = ""): Promise<boolean> {
    const patterns = await this.getActivePatterns();
    const actionablePatterns = patterns.filter(p => p.type === "actionable");

    const textToCheck = `${title} ${description}`.toLowerCase();

    return actionablePatterns.some(pattern => pattern.matches(textToCheck));
  }

  /**
   * Create a new pattern
   */
  async createPattern(data: {
    pattern: string;
    description: string;
    type: "non_actionable" | "actionable";
    is_active?: boolean;
    priority?: number;
    examples?: string[];
  }): Promise<InsightPattern> {
    try {
      // Validate regex pattern
      RegExp(data.pattern, 'i');

      const pattern = await InsightPattern.insertAndFetch({
        pattern: data.pattern,
        description: data.description,
        type: data.type,
        is_active: data.is_active ?? true,
        priority: data.priority ?? 0,
        examples: data.examples ? JSON.stringify(data.examples) : undefined,
      });

      // Clear cache
      this.clearCache();

      logger.info(`Created insight pattern: ${pattern.id}`);
      return pattern;
    } catch (error) {
      logger.error("Error creating pattern:", error);
      throw error;
    }
  }

  /**
   * Update an existing pattern
   */
  async updatePattern(id: number, data: Partial<{
    pattern: string;
    description: string;
    type: "non_actionable" | "actionable";
    is_active: boolean;
    priority: number;
    examples: string[];
  }>): Promise<InsightPattern> {
    try {
      // Validate regex pattern if provided
      if (data.pattern) {
        RegExp(data.pattern, 'i');
      }

      const updateData: any = { ...data };
      if (data.examples) {
        updateData.examples = JSON.stringify(data.examples);
      }

      await InsightPattern.update((qb) => qb.where("id", id), updateData);
      const pattern = await InsightPattern.find(id);

      if (!pattern) {
        throw new Error(`Pattern with id ${id} not found`);
      }

      // Clear cache
      this.clearCache();

      logger.info(`Updated insight pattern: ${id}`);
      return pattern;
    } catch (error) {
      logger.error("Error updating pattern:", error);
      throw error;
    }
  }

  /**
   * Delete a pattern
   */
  async deletePattern(id: number): Promise<void> {
    try {
      await InsightPattern.delete((qb) => qb.where("id", id));

      // Clear cache
      this.clearCache();

      logger.info(`Deleted insight pattern: ${id}`);
    } catch (error) {
      logger.error("Error deleting pattern:", error);
      throw error;
    }
  }

  /**
   * Test a pattern against sample text
   */
  async testPattern(pattern: string, testText: string): Promise<{
    matches: boolean;
    error?: string;
  }> {
    try {
      const regex = new RegExp(pattern, 'i');
      return { matches: regex.test(testText) };
    } catch (error) {
      return { 
        matches: false, 
        error: error instanceof Error ? error.message : "Invalid regex pattern" 
      };
    }
  }

  /**
   * Clear the pattern cache
   */
  clearCache(): void {
    this.cachedPatterns = null;
    this.cacheExpiry = 0;
  }

  /**
   * Get patterns that match a specific text (for debugging)
   */
  async getMatchingPatterns(title: string, description: string = ""): Promise<InsightPattern[]> {
    const patterns = await this.getActivePatterns();
    const textToCheck = `${title} ${description}`.toLowerCase();

    return patterns.filter(pattern => pattern.matches(textToCheck));
  }
}

// Export singleton instance
export const insightPatternService = InsightPatternService.getInstance();
