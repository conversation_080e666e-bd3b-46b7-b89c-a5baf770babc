import Router from "@koa/router";
import { Context } from "koa";
import { insightPatternService } from "./InsightPatternService";
import { InsightPattern } from "./InsightPattern";
import { logger } from "../config/logger";
import { locationRoleMiddleware } from "../locations/LocationService";

const router = new Router();

/**
 * Get all insight patterns (admin only)
 */
router.get("/patterns", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const patterns = await insightPatternService.getAllPatterns();
    
    // Convert patterns to include parsed examples
    const formattedPatterns = patterns.map(pattern => ({
      ...pattern,
      examples: pattern.getExamplesArray(),
    }));

    ctx.body = {
      success: true,
      data: formattedPatterns,
    };
  } catch (error) {
    logger.error("Error fetching insight patterns:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to fetch insight patterns",
    };
  }
});

/**
 * Create a new insight pattern (admin only)
 */
router.post("/patterns", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const { pattern, description, type, is_active, priority, examples } = ctx.request.body as any;

    // Validate required fields
    if (!pattern || !description || !type) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Pattern, description, and type are required",
      };
      return;
    }

    // Validate type
    if (!["non_actionable", "actionable"].includes(type)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Type must be either 'non_actionable' or 'actionable'",
      };
      return;
    }

    // Test the regex pattern
    const testResult = await insightPatternService.testPattern(pattern, "test");
    if (testResult.error) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: `Invalid regex pattern: ${testResult.error}`,
      };
      return;
    }

    const newPattern = await insightPatternService.createPattern({
      pattern,
      description,
      type,
      is_active: is_active ?? true,
      priority: priority ?? 0,
      examples: examples || [],
    });

    ctx.body = {
      success: true,
      data: {
        ...newPattern,
        examples: newPattern.getExamplesArray(),
      },
    };
  } catch (error) {
    logger.error("Error creating insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to create insight pattern",
    };
  }
});

/**
 * Update an insight pattern (admin only)
 */
router.put("/patterns/:id", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id);
    const { pattern, description, type, is_active, priority, examples } = ctx.request.body as any;

    if (isNaN(id)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Invalid pattern ID",
      };
      return;
    }

    // Validate type if provided
    if (type && !["non_actionable", "actionable"].includes(type)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Type must be either 'non_actionable' or 'actionable'",
      };
      return;
    }

    // Test the regex pattern if provided
    if (pattern) {
      const testResult = await insightPatternService.testPattern(pattern, "test");
      if (testResult.error) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          error: `Invalid regex pattern: ${testResult.error}`,
        };
        return;
      }
    }

    const updatedPattern = await insightPatternService.updatePattern(id, {
      pattern,
      description,
      type,
      is_active,
      priority,
      examples,
    });

    ctx.body = {
      success: true,
      data: {
        ...updatedPattern,
        examples: updatedPattern.getExamplesArray(),
      },
    };
  } catch (error) {
    logger.error("Error updating insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to update insight pattern",
    };
  }
});

/**
 * Delete an insight pattern (admin only)
 */
router.delete("/patterns/:id", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const id = parseInt(ctx.params.id);

    if (isNaN(id)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Invalid pattern ID",
      };
      return;
    }

    await insightPatternService.deletePattern(id);

    ctx.body = {
      success: true,
      message: "Pattern deleted successfully",
    };
  } catch (error) {
    logger.error("Error deleting insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to delete insight pattern",
    };
  }
});

/**
 * Test a pattern against sample text
 */
router.post("/patterns/test", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const { pattern, testText } = ctx.request.body as any;

    if (!pattern || !testText) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Pattern and testText are required",
      };
      return;
    }

    const result = await insightPatternService.testPattern(pattern, testText);

    ctx.body = {
      success: true,
      data: result,
    };
  } catch (error) {
    logger.error("Error testing pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to test pattern",
    };
  }
});

/**
 * Get patterns that match specific text (for debugging)
 */
router.post("/patterns/match", locationRoleMiddleware("admin"), async (ctx: Context) => {
  try {
    const { title, description } = ctx.request.body as any;

    if (!title) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Title is required",
      };
      return;
    }

    const matchingPatterns = await insightPatternService.getMatchingPatterns(title, description || "");

    ctx.body = {
      success: true,
      data: matchingPatterns.map(pattern => ({
        ...pattern,
        examples: pattern.getExamplesArray(),
      })),
    };
  } catch (error) {
    logger.error("Error finding matching patterns:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to find matching patterns",
    };
  }
});

export default router;
