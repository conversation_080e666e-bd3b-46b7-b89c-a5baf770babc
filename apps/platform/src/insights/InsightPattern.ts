import Model from "../core/Model";

export class InsightPattern extends Model {
  pattern!: string;
  description!: string;
  type!: "non_actionable" | "actionable";
  is_active!: boolean;
  priority!: number;
  examples?: string; // JSON string

  static tableName = "insight_patterns";
  static jsonAttributes = ["examples"];

  // Helper method to get examples as array
  getExamplesArray(): string[] {
    if (!this.examples) return [];
    try {
      // If examples is already an array (from JSON parsing), return it
      if (Array.isArray(this.examples)) return this.examples;
      // If it's a string, parse it
      return JSON.parse(this.examples);
    } catch {
      return [];
    }
  }

  // Helper method to set examples from array
  setExamplesArray(examples: string[]) {
    this.examples = JSON.stringify(examples);
  }

  // Test if this pattern matches a given text
  matches(text: string): boolean {
    if (!this.is_active) return false;
    try {
      const regex = new RegExp(this.pattern, 'i');
      return regex.test(text);
    } catch (error) {
      console.warn(`Invalid regex pattern: ${this.pattern}`, error);
      return false;
    }
  }
}
