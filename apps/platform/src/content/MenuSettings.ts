import Model from "../core/Model";

export interface MenuSettingsParams {
  type: string;
  title: string;
  description?: string;
  image_url?: string;
  link?: string;
  order?: number;
  active?: boolean;
  start_date?: string;
  end_date?: string;
  metadata?: any;
}

export class MenuSettings extends Model {
  declare id: number;
  location_id!: number;
  type!: string;
  title!: string;
  description?: string;
  image_url?: string;
  link?: string;
  order!: number;
  active!: boolean;
  start_date?: Date;
  end_date?: Date;
  metadata?: any;
  declare created_at: Date;
  declare updated_at: Date;

  static tableName = "menu_settings";

  static jsonAttributes = ["metadata"];

  flatten() {
    return {
      ...this,
      start_date: this.start_date?.toISOString(),
      end_date: this.end_date?.toISOString(),
      created_at: this.created_at?.toISOString(),
      updated_at: this.updated_at?.toISOString(),
    };
  }

  toJSON() {
    return this.flatten();
  }
}

export type MenuSettingsInternalParams = Partial<MenuSettingsParams> & {
  location_id: number;
};
