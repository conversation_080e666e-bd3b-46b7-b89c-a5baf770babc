import Router from "@koa/router";
import { logger } from "../config/logger";
import { RequestError } from "../core/errors";
import { JSONSchemaType, validate } from "../core/validate";
import App from "../app";
import { MenuSettings, MenuSettingsParams } from "./MenuSettings";
import parseMultipartForm from "../storage/MultiFileParser";
import { ProductImageService } from "../products/ProductImageService";
import { locationRoleMiddleware } from "../locations/LocationService";

// Extended params for API input that includes processing fields
interface MenuSettingsApiParams extends MenuSettingsParams {
  download_image_url?: string;
}

const menuSettingsSchema: JSONSchemaType<MenuSettingsApiParams> = {
  type: "object",
  required: ["type", "title"],
  properties: {
    type: { type: "string", minLength: 1 },
    title: { type: "string", minLength: 1 },
    description: { type: "string", nullable: true },
    image_url: { type: "string", nullable: true },
    link: { type: "string", nullable: true },
    order: { type: "number", nullable: true, minimum: 0 },
    active: { type: "boolean", nullable: true },
    start_date: { type: "string", nullable: true },
    end_date: { type: "string", nullable: true },
    metadata: { type: "object", nullable: true, additionalProperties: true },
    download_image_url: { type: "string", nullable: true },
  },
  additionalProperties: false,
};

const router = new Router({
  prefix: "/menu-settings",
});

// Apply location role middleware for admin access
router.use(locationRoleMiddleware("editor"));

/**
 * @swagger
 * /admin/locations/{locationId}/menu-settings:
 *   get:
 *     summary: List Menu Settings
 *     description: Get all menu settings for a location
 *     tags: [MenuSettings]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by menu setting type
 *     responses:
 *       200:
 *         description: List of menu settings
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */
router.get("/", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const typeFilter = ctx.query.type as string;

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    let query = db("menu_settings")
      .where("location_id", locationId)
      .orderBy("type", "asc")
      .orderBy("order", "asc")
      .orderBy("created_at", "desc");

    if (typeFilter) {
      query = query.where("type", typeFilter);
    }

    const settings = await query;

    ctx.body = {
      results: settings,
      total: settings.length,
    };
  } catch (error) {
    logger.error({
      message: "Error retrieving menu settings",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
});

/**
 * @swagger
 * /admin/locations/{locationId}/menu-settings:
 *   post:
 *     summary: Create Menu Setting
 *     description: Create a new menu setting for a location
 *     tags: [MenuSettings]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               image_url:
 *                 type: string
 *               link:
 *                 type: string
 *               order:
 *                 type: integer
 *               active:
 *                 type: boolean
 *               start_date:
 *                 type: string
 *               end_date:
 *                 type: string
 *               metadata:
 *                 type: object
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               image_url:
 *                 type: string
 *               download_image_url:
 *                 type: string
 *               menuImage:
 *                 type: string
 *                 format: binary
 *               link:
 *                 type: string
 *               order:
 *                 type: integer
 *               active:
 *                 type: boolean
 *               start_date:
 *                 type: string
 *               end_date:
 *                 type: string
 *               metadata:
 *                 type: string
 *     responses:
 *       201:
 *         description: Menu setting created successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal server error
 */
router.post("/", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Initialize product image service
    const productImageService = new ProductImageService();

    // Handle both JSON and multipart/form-data requests
    let settingData: MenuSettingsParams;
    let menuImage: any = null;
    let imageUrl: string | undefined;

    // Check if this is a multipart request by looking at content-type
    const contentType = ctx.request.headers["content-type"] || "";
    const isMultipart = contentType.includes("multipart/form-data");

    logger.info({
      message: "POST request analysis",
      contentType,
      isMultipart,
      hasRequestFiles: !!ctx.request.files,
      requestBodyType: typeof ctx.request.body,
      requestBodyKeys: ctx.request.body ? Object.keys(ctx.request.body) : [],
    });

    if (isMultipart) {
      // Parse multipart form data
      const formData = await parseMultipartForm(ctx);

      // Extract setting data and image
      const rawSettingData = formData.fields || {};
      menuImage = formData.files.menuImage;

      // Convert string values to proper types for multipart form data
      settingData = {
        ...rawSettingData,
        // Convert boolean fields
        active: rawSettingData.active === "true",
        // Convert number fields
        order: rawSettingData.order
          ? parseInt(rawSettingData.order)
          : undefined,
        // Parse JSON fields if needed
        metadata: rawSettingData.metadata
          ? JSON.parse(rawSettingData.metadata)
          : undefined,
      } as MenuSettingsParams;

      logger.info({
        message: "Multipart request detected and parsed",
        rawSettingData,
        settingData,
        hasMenuImage: !!menuImage,
        menuImageType: menuImage?.metadata?.mimeType,
        menuImageSize: menuImage?.metadata?.size,
        formDataKeys: Object.keys(formData.fields || {}),
        fileKeys: Object.keys(formData.files || {}),
      });
    } else {
      // Regular JSON request
      settingData = validate<MenuSettingsApiParams>(
        menuSettingsSchema,
        ctx.request.body
      );
    }

    // Handle image processing
    const hasImageFile = menuImage;
    const hasImageUrl =
      settingData.image_url && settingData.image_url.trim() !== "";
    const hasDownloadUrl =
      (settingData as any).download_image_url &&
      (settingData as any).download_image_url.trim() !== "";

    if (hasImageFile) {
      // Process uploaded file from FileStream
      const fileName =
        menuImage.metadata.fileName || `menu-${settingData.type}-${Date.now()}`;
      const contentType = menuImage.metadata.mimeType || "image/jpeg";
      const fileSize = menuImage.metadata.size;

      if (!contentType.startsWith("image/")) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid file type. Only image files are allowed.",
          validation_errors: [
            {
              field: "menuImage",
              message: "Invalid file type. Only image files are allowed.",
            },
          ],
        };
        return;
      }

      logger.info({
        message: "Processing uploaded FileStream",
        fileName,
        contentType,
        fileSize,
        fieldName: menuImage.metadata.fieldName,
      });

      // Get file data as buffer from the stream
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        menuImage.file.on("data", (chunk: Buffer) => chunks.push(chunk));
        menuImage.file.on("end", () => resolve(Buffer.concat(chunks)));
        menuImage.file.on("error", reject);
      });

      logger.info({
        message: "File stream read successfully",
        bufferSize: buffer.length,
        expectedSize: fileSize,
      });

      // Store the image
      const result = await productImageService.storeProductImage(
        locationId,
        buffer,
        fileName,
        contentType,
        fileSize
      );

      imageUrl = result.url;
      settingData.image_url = imageUrl;
      logger.info({
        message: "Successfully uploaded menu image",
        imageUrl: result.url,
      });
    } else if (hasDownloadUrl) {
      // Download and store image from URL
      const result = await productImageService.downloadAndStoreProductImage(
        locationId,
        (settingData as any).download_image_url!,
        `menu-${settingData.type}`
      );

      imageUrl = result.url;
      settingData.image_url = imageUrl;
      logger.info(
        `Successfully downloaded and stored menu image: ${result.url}`
      );
    }

    // Remove download_image_url from the setting data as it's not a menu setting field
    delete (settingData as any).download_image_url;

    const finalSettingData = {
      location_id: locationId,
      ...settingData,
      start_date: settingData.start_date
        ? new Date(settingData.start_date)
        : null,
      end_date: settingData.end_date ? new Date(settingData.end_date) : null,
    };

    const [id] = await db("menu_settings").insert(finalSettingData);
    const createdSetting = await db("menu_settings").where("id", id).first();

    logger.info({
      message: "Menu setting created",
      settingId: id,
      locationId,
      type: settingData.type,
      hasImage: !!imageUrl,
    });

    ctx.status = 201;
    ctx.body = createdSetting;
  } catch (error) {
    logger.error({
      message: "Error creating menu setting",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
});

/**
 * @swagger
 * /admin/locations/{locationId}/menu-settings/{id}:
 *   put:
 *     summary: Update Menu Setting
 *     description: Update an existing menu setting
 *     tags: [MenuSettings]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Menu setting ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               image_url:
 *                 type: string
 *               link:
 *                 type: string
 *               order:
 *                 type: integer
 *               active:
 *                 type: boolean
 *               start_date:
 *                 type: string
 *               end_date:
 *                 type: string
 *               metadata:
 *                 type: object
 *     responses:
 *       200:
 *         description: Menu setting updated successfully
 *       404:
 *         description: Menu setting not found
 *       500:
 *         description: Internal server error
 */
router.put("/:id", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const settingId = parseInt(ctx.params.id);

    if (isNaN(settingId)) {
      throw new RequestError("Invalid setting ID", 400);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Check if setting exists
    const existingSetting = await db("menu_settings")
      .where("id", settingId)
      .where("location_id", locationId)
      .first();

    if (!existingSetting) {
      throw new RequestError("Menu setting not found", 404);
    }

    // Initialize product image service
    const productImageService = new ProductImageService();

    // Handle both JSON and multipart/form-data requests
    let settingData: Partial<MenuSettingsParams>;
    let menuImage: any = null;
    let imageUrl: string | undefined;

    // Check if this is a multipart request by looking at content-type
    const contentType = ctx.request.headers["content-type"] || "";
    const isMultipart = contentType.includes("multipart/form-data");

    logger.info({
      message: "PUT request analysis",
      contentType,
      isMultipart,
      hasRequestFiles: !!ctx.request.files,
      requestBodyType: typeof ctx.request.body,
      requestBodyKeys: ctx.request.body ? Object.keys(ctx.request.body) : [],
    });

    if (isMultipart) {
      // Parse multipart form data
      const formData = await parseMultipartForm(ctx);

      // Extract setting data and image
      const rawSettingData = formData.fields || {};
      menuImage = formData.files.menuImage;

      // Convert string values to proper types for multipart form data
      settingData = {
        ...rawSettingData,
        // Convert boolean fields
        active: rawSettingData.active === "true",
        // Convert number fields
        order: rawSettingData.order
          ? parseInt(rawSettingData.order)
          : undefined,
        // Parse JSON fields if needed
        metadata: rawSettingData.metadata
          ? JSON.parse(rawSettingData.metadata)
          : undefined,
      };

      logger.info({
        message: "PUT: Multipart request detected and parsed",
        rawSettingData,
        settingData,
        hasMenuImage: !!menuImage,
        menuImageType: menuImage?.metadata?.mimeType,
        menuImageSize: menuImage?.metadata?.size,
        formDataKeys: Object.keys(formData.fields || {}),
        fileKeys: Object.keys(formData.files || {}),
      });
    } else {
      // Regular JSON request
      settingData = ctx.request.body as Partial<MenuSettingsParams>;
    }

    // Handle image processing
    const hasImageFile = menuImage;
    const hasImageUrl =
      settingData.image_url && settingData.image_url.trim() !== "";
    const hasDownloadUrl =
      (settingData as any).download_image_url &&
      (settingData as any).download_image_url.trim() !== "";

    if (hasImageFile) {
      // Process uploaded file from FileStream
      const fileName =
        menuImage.metadata.fileName ||
        `menu-${existingSetting.type}-${Date.now()}`;
      const contentType = menuImage.metadata.mimeType || "image/jpeg";
      const fileSize = menuImage.metadata.size;

      if (!contentType.startsWith("image/")) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid file type. Only image files are allowed.",
          validation_errors: [
            {
              field: "menuImage",
              message: "Invalid file type. Only image files are allowed.",
            },
          ],
        };
        return;
      }

      logger.info({
        message: "Processing uploaded FileStream",
        fileName,
        contentType,
        fileSize,
        fieldName: menuImage.metadata.fieldName,
      });

      // Get file data as buffer from the stream
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        menuImage.file.on("data", (chunk: Buffer) => chunks.push(chunk));
        menuImage.file.on("end", () => resolve(Buffer.concat(chunks)));
        menuImage.file.on("error", reject);
      });

      logger.info({
        message: "File stream read successfully",
        bufferSize: buffer.length,
        expectedSize: fileSize,
      });

      // Store the image
      const result = await productImageService.storeProductImage(
        locationId,
        buffer,
        fileName,
        contentType,
        fileSize
      );

      imageUrl = result.url;
      settingData.image_url = imageUrl;
      logger.info({
        message: "Successfully uploaded menu image",
        imageUrl: result.url,
      });
    } else if (hasDownloadUrl) {
      // Download and store image from URL
      const result = await productImageService.downloadAndStoreProductImage(
        locationId,
        (settingData as any).download_image_url!,
        `menu-${existingSetting.type}`
      );

      imageUrl = result.url;
      settingData.image_url = imageUrl;
      logger.info(
        `Successfully downloaded and stored menu image: ${result.url}`
      );
    }

    // Remove download_image_url from the setting data as it's not a menu setting field
    delete (settingData as any).download_image_url;

    const updateData: any = {
      ...settingData,
      start_date: settingData.start_date
        ? new Date(settingData.start_date)
        : undefined,
      end_date: settingData.end_date
        ? new Date(settingData.end_date)
        : undefined,
    };

    // Remove undefined values
    Object.keys(updateData).forEach((key) => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    await db("menu_settings")
      .where("id", settingId)
      .where("location_id", locationId)
      .update(updateData);

    const updatedSetting = await db("menu_settings")
      .where("id", settingId)
      .first();

    logger.info({
      message: "Menu setting updated",
      settingId,
      locationId,
      hasImage: !!imageUrl,
    });

    ctx.body = updatedSetting;
  } catch (error) {
    logger.error({
      message: "Error updating menu setting",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
      settingId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
});

/**
 * @swagger
 * /admin/locations/{locationId}/menu-settings/{id}:
 *   delete:
 *     summary: Delete Menu Setting
 *     description: Delete a menu setting
 *     tags: [MenuSettings]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Menu setting ID
 *     responses:
 *       204:
 *         description: Menu setting deleted successfully
 *       404:
 *         description: Menu setting not found
 *       500:
 *         description: Internal server error
 */
router.delete("/:id", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const settingId = parseInt(ctx.params.id);

    if (isNaN(settingId)) {
      throw new RequestError("Invalid setting ID", 400);
    }

    const app = App.main;
    const db = app.db;
    if (!db) {
      throw new RequestError("Database connection not available", 500);
    }

    // Check if setting exists
    const existingSetting = await db("menu_settings")
      .where("id", settingId)
      .where("location_id", locationId)
      .first();

    if (!existingSetting) {
      throw new RequestError("Menu setting not found", 404);
    }

    await db("menu_settings")
      .where("id", settingId)
      .where("location_id", locationId)
      .delete();

    logger.info({
      message: "Menu setting deleted",
      settingId,
      locationId,
    });

    ctx.status = 204;
  } catch (error) {
    logger.error({
      message: "Error deleting menu setting",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
      settingId: ctx.params.id,
    });

    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 500;
    } else {
      ctx.status = 500;
    }

    ctx.body = {
      error: error instanceof Error ? error.message : "Internal server error",
    };
  }
});

export default router;
