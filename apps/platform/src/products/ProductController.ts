/**
 * @swagger
 * tags:
 *   name: Product
 *   description: Product management endpoints
 */

/* eslint-disable indent */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { importProducts, importFromMarketplace } from "./ProductImport";
import parse from "../storage/FileStream";
import { extractQueryParams, uuid } from "../utilities";
import { searchParamsSchema, SearchSchema } from "../core/searchParams";
import {
  Product,
  ProductParams,
  ProductInternalParams,
  FeaturedProduct,
  FeaturedProductParams,
  FeaturedProductWithProduct,
} from "./Product";
import { JSONSchemaType, validate } from "../core/validate";
import App from "../app";
import ProductPatchJob from "./ProductPatchJob";
import ProductDeleteJob from "./ProductDeleteJob";
import { getProduct, pagedProducts } from "./ProductRepository";
import { ProductAIEnhancementService } from "./ProductAIEnhancementService";
import { ProductDataVectorService } from "./ProductDataVectorService";
import { logger } from "../config/logger";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { SupabaseService } from "../supabase/SupabaseService";
import parseMultipartForm from "../storage/MultiFileParser";
import Storage from "../storage/Storage";
import { Readable } from "stream";
import { RequestError } from "../core/errors";
import Image from "../storage/Image";
import { productImageService } from "./ProductImageService";
import { isEqual } from "lodash";
import { ProductGapAnalysisTool } from "../chats/tools/dataAnalysisTools";
import { AIEnhancementMetrics } from "./AIEnhancementMetrics";

const router = new Router({
  prefix: "/products",
});

const patchProductsRequest: JSONSchemaType<Partial<ProductParams>[]> = {
  type: "array",
  items: {
    $id: "partialProductParams",
    type: "object",
    properties: {
      location_id: { type: "number", nullable: true },
      meta_sku: { type: "string" },
      source: { type: "string", nullable: true },
      retailer_id: { type: "string", nullable: true },
      raw_product_name: { type: "string", nullable: true },
      product_name: { type: "string", nullable: true },
      medical: { type: "boolean", nullable: true },
      recreational: { type: "boolean", nullable: true },
      cann_sku_id: { type: "string", nullable: true },
      brand_name: { type: "string", nullable: true },
      brand_id: { type: "number", nullable: true },
      url: { type: "string", nullable: true },
      image_url: { type: "string", nullable: true },
      raw_weight_string: { type: "string", nullable: true },
      display_weight: { type: "string", nullable: true },
      raw_product_category: { type: "string", nullable: true },
      category: { type: "string", nullable: true },
      raw_subcategory: { type: "string", nullable: true },
      subcategory: { type: "string", nullable: true },
      product_tags: {
        type: "array",
        items: { type: "string" },
        nullable: true,
      },
      percentage_thc: { type: "number", nullable: true },
      percentage_cbd: { type: "number", nullable: true },
      mg_thc: { type: "number", nullable: true },
      mg_cbd: { type: "number", nullable: true },
      quantity_per_package: { type: "number", nullable: true },
      latest_price: { type: "number", nullable: true },
      menu_provider: { type: "string", nullable: true },
      data: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      images_urls: { type: "string", nullable: true },
      review_summary: { type: "string", nullable: true },
      rating: { type: "number", nullable: true },
      reviews_count: { type: "number", nullable: true },
      product_description: { type: "string", nullable: true },
      short_description: { type: "string", nullable: true },
      thc: { type: "number", nullable: true },
      cbd: { type: "number", nullable: true },
      variants: {
        type: "array",
        nullable: true,
        items: { type: "string" },
      },
      enhancement_status: { type: "string", nullable: true },
      ai_enhanced_fields: {
        type: "array",
        nullable: true,
        items: { type: "string" },
      },
      mood: {
        type: "array",
        nullable: true,
        items: { type: "string" },
      },
      estimated_cbd_percentage: { type: "string", nullable: true },
      estimated_thc_percentage: { type: "string", nullable: true },
      effects: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      enhancement_error: { type: "string", nullable: true },
      external_id: { type: "string", nullable: true },
      wholesale_price: { type: "number", nullable: true },
      retail_price: { type: "number", nullable: true },
      msrp: { type: "number", nullable: true },
      profit_margin: { type: "number", nullable: true },
      grower_name: { type: "string", nullable: true },
      cultivar: { type: "string", nullable: true },
      batch_number: { type: "string", nullable: true },
      harvest_date: { type: "string", nullable: true },
      coa_url: { type: "string", nullable: true },
      slug: { type: "string", nullable: true },
      out_of_stock: { type: "boolean", nullable: true },
      inventory_quantity: { type: "number", nullable: true },
    },
    additionalProperties: false,
  },
  minItems: 1,
} as any;

const deleteProductsRequest: JSONSchemaType<string[]> = {
  $id: "deleteProducts",
  type: "array",
  items: {
    type: "string",
  },
  minItems: 1,
};

/**
 * @swagger
 * /products:
 *   get:
 *     summary: List Products
 *     description: Retrieves a paginated list of products for the current location with search and filter capabilities
 *     tags: [Product]
 *     parameters:
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: string
 *         description: Cursor for pagination
 *       - in: query
 *         name: page
 *         schema:
 *           type: string
 *           enum: [prev, next]
 *         description: Page direction
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [id, product_name, brand_name, category, latest_price, created_at, updated_at, featured]
 *         description: Field to sort by (use 'featured' to show featured products first)
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query for product name, brand, category, etc.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *         description: Filter object for specific field filtering
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 nextCursor:
 *                   type: string
 *                 prevCursor:
 *                   type: string
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    logger.info({
      message: "Getting products",
      locationId,
      query: ctx.query,
      path: ctx.path,
    });

    // Extract cursor pagination parameters
    const cursor = ctx.query.cursor as string;
    const page = (ctx.query.page as string) || "next";
    const limit = Math.min(
      100,
      Math.max(1, parseInt(ctx.query.limit as string) || 25)
    );

    // Handle search parameters
    const searchQuery = (ctx.query.q as string)?.trim();
    const sort = (ctx.query.sort as string) || "created_at";
    const direction = (ctx.query.direction as string) || "desc";

    // Handle filter object (from frontend)
    let filterObj: any = {};
    try {
      if (ctx.query.filter && typeof ctx.query.filter === "string") {
        filterObj = JSON.parse(ctx.query.filter);
      } else if (ctx.query.filter && typeof ctx.query.filter === "object") {
        filterObj = ctx.query.filter;
      }
    } catch (e) {
      logger.warn("Invalid filter parameter:", ctx.query.filter);
    }

    // Build query with featured products join - admin can see all products including inactive
    let query = App.main
      .db("products")
      .where("products.location_id", locationId)
      .leftJoin("featured_products", function () {
        this.on("featured_products.product_id", "=", "products.id").andOn(
          "featured_products.location_id",
          "=",
          "products.location_id"
        );
      })
      .select(
        "products.*",
        App.main.db.raw(
          "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
        ),
        App.main.db.raw(
          "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
        ),
        App.main.db.raw("featured_products.sort_order as featured_sort_order"),
        App.main.db.raw(
          "COALESCE(featured_products.active, false) as featured_active"
        )
      );

    // Apply filters from filter object
    if (filterObj) {
      Object.keys(filterObj).forEach((key) => {
        if (
          filterObj[key] !== null &&
          filterObj[key] !== undefined &&
          filterObj[key] !== ""
        ) {
          // Use products table prefix for product fields
          query = query.where(`products.${key}`, filterObj[key]);
        }
      });
    }

    // Apply text search if provided
    if (searchQuery) {
      query = query.where((builder) => {
        builder
          .where("products.product_name", "like", `%${searchQuery}%`)
          .orWhere("products.raw_product_name", "like", `%${searchQuery}%`)
          .orWhere("products.brand_name", "like", `%${searchQuery}%`)
          .orWhere("products.category", "like", `%${searchQuery}%`)
          .orWhere("products.subcategory", "like", `%${searchQuery}%`)
          .orWhere("products.meta_sku", "like", `%${searchQuery}%`)
          .orWhere("products.retailer_id", "like", `%${searchQuery}%`)
          .orWhereRaw(
            "JSON_SEARCH(products.product_tags, 'one', ?) IS NOT NULL",
            [`%${searchQuery}%`]
          );
      });
    }

    // Apply sorting
    const validSortFields = [
      "id",
      "product_name",
      "brand_name",
      "category",
      "latest_price",
      "created_at",
      "updated_at",
      "featured",
    ];
    const sortField = validSortFields.includes(sort) ? sort : "created_at";
    const sortDirection = direction.toLowerCase() === "asc" ? "asc" : "desc";

    if (sortField === "featured") {
      // Special handling for featured sorting:add featured
      // 1. Featured products first (is_featured DESC)
      // 2. Top picks first among featured (is_top_pick DESC)
      // 3. Then by featured sort order (featured_sort_order ASC)
      // 4. Finally by product name for consistency
      query = query
        .orderByRaw(
          "CASE WHEN featured_products.id IS NOT NULL THEN 0 ELSE 1 END"
        ) // Featured first
        .orderBy("featured_products.is_top_pick", "desc") // Top picks first among featured
        .orderBy("featured_products.sort_order", "asc") // Featured products in their set order
        .orderBy("products.product_name", "asc"); // Consistent secondary sort
    } else {
      query = query.orderBy(`products.${sortField}`, sortDirection);
    }

    // Apply cursor pagination
    if (cursor) {
      const operator = page === "prev" ? "<" : ">";
      query = query.where("products.id", operator, cursor);
    }

    // Get one extra item to check if there are more results
    const products = await query.limit(limit + 1);

    const hasMore = products.length > limit;
    if (hasMore) {
      products.pop(); // Remove the extra item we fetched
    }
    const totalProducts = await App.main
      .db("products")
      .count("* as count")
      .where("products.location_id", locationId)
      .first();

    logger.info({
      message: "Retrieved products",
      count: products.length,
      hasMore,
      locationId,
      searchQuery,
      filterObj,
      totalProducts: totalProducts?.count,
      sort: sortField,
      direction: sortDirection,
    });

    ctx.body = {
      results: products,
      nextCursor: hasMore ? products[products.length - 1]?.id : undefined,
      prevCursor: cursor,
      total: totalProducts?.count,
      limit,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching products",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to fetch products" };
  }
});

/**
 * @swagger
 * /products:
 *   post:
 *     summary: Create Product
 *     description: Creates a new product for the current location
 *     tags: [Product]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - meta_sku
 *               - product_name
 *               - image_url
 *               - category
 *               - latest_price
 *             properties:
 *               meta_sku:
 *                 type: string
 *                 description: Unique identifier for the product
 *               retailer_id:
 *                 type: string
 *                 description: Retailer's product identifier
 *               product_name:
 *                 type: string
 *                 description: Product name
 *               raw_product_name:
 *                 type: string
 *                 description: Original product name
 *               brand_name:
 *                 type: string
 *                 description: Brand name
 *               image_url:
 *                 type: string
 *                 description: Product image URL
 *               category:
 *                 type: string
 *                 description: Product category
 *               subcategory:
 *                 type: string
 *                 description: Product subcategory
 *               latest_price:
 *                 type: number
 *                 description: Current product price
 *               medical:
 *                 type: boolean
 *                 description: Whether product is for medical use
 *               recreational:
 *                 type: boolean
 *                 description: Whether product is for recreational use
 *               percentage_thc:
 *                 type: number
 *                 description: THC percentage
 *               percentage_cbd:
 *                 type: number
 *                 description: CBD percentage
 *               product_description:
 *                 type: string
 *                 description: Product description
 *               display_weight:
 *                 type: string
 *                 description: Product weight for display
 *               effects:
 *                 type: object
 *                 description: Product effects
 *               mood:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Product mood effects
 *     responses:
 *       201:
 *         description: Product created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Product'
 *       400:
 *         description: Invalid input or validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 validation_errors:
 *                   type: array
 *                   items:
 *                     type: object
 *       409:
 *         description: Product with meta_sku already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.post("/", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    // Handle both JSON and multipart/form-data requests
    let productData: Partial<ProductParams>;
    let productImage: any = null;
    let imageUrl: string | null = null;

    // Check if this is a multipart request by looking at content-type
    const contentType = ctx.request.headers["content-type"] || "";
    const isMultipart = contentType.includes("multipart/form-data");

    logger.info({
      message: "POST request analysis",
      contentType,
      isMultipart,
      hasRequestFiles: !!ctx.request.files,
      requestBodyType: typeof ctx.request.body,
      requestBodyKeys: ctx.request.body ? Object.keys(ctx.request.body) : [],
    });

    if (isMultipart) {
      // Parse multipart form data
      const formData = await parseMultipartForm(ctx);

      // Extract product data and image
      const rawProductData = formData.fields || {};
      productImage = formData.files.productImage;

      // Convert string values to proper types for multipart form data
      productData = {
        ...rawProductData,
        // Convert boolean fields
        medical: rawProductData.medical === "true",
        recreational: rawProductData.recreational === "true",
        // Convert number fields
        latest_price: rawProductData.latest_price
          ? parseFloat(rawProductData.latest_price)
          : undefined,
        location_id: rawProductData.location_id
          ? parseInt(rawProductData.location_id)
          : undefined,
        brand_id: rawProductData.brand_id
          ? parseInt(rawProductData.brand_id)
          : undefined,
        percentage_thc: rawProductData.percentage_thc
          ? parseFloat(rawProductData.percentage_thc)
          : undefined,
        percentage_cbd: rawProductData.percentage_cbd
          ? parseFloat(rawProductData.percentage_cbd)
          : undefined,
        mg_thc: rawProductData.mg_thc
          ? parseFloat(rawProductData.mg_thc)
          : undefined,
        mg_cbd: rawProductData.mg_cbd
          ? parseFloat(rawProductData.mg_cbd)
          : undefined,
        quantity_per_package: rawProductData.quantity_per_package
          ? parseInt(rawProductData.quantity_per_package)
          : undefined,
        rating: rawProductData.rating
          ? parseFloat(rawProductData.rating)
          : undefined,
        reviews_count: rawProductData.reviews_count
          ? parseInt(rawProductData.reviews_count)
          : undefined,
        // Parse JSON fields - handle both string and JSON array for mood
        mood: rawProductData.mood ? parseMoodField(rawProductData.mood) : [],
        product_tags: rawProductData.product_tags
          ? JSON.parse(rawProductData.product_tags)
          : undefined,
        effects: rawProductData.effects
          ? rawProductData.effects === '""'
            ? ""
            : rawProductData.effects
          : undefined,
      };

      logger.info({
        message: "Multipart request detected and parsed",
        rawProductData,
        productData,
        hasProductImage: !!productImage,
        productImageType: productImage?.metadata?.mimeType,
        productImageSize: productImage?.metadata?.size,
        formDataKeys: Object.keys(formData.fields || {}),
        fileKeys: Object.keys(formData.files || {}),
      });
    } else {
      // Regular JSON request
      productData = ctx.request.body as Partial<ProductParams>;
    }

    // Validate required fields
    if (!productData.meta_sku) {
      ctx.status = 400;
      ctx.body = {
        error: "meta_sku is required",
        validation_errors: [
          { field: "meta_sku", message: "meta_sku is required" },
        ],
      };
      return;
    }

    if (!productData.product_name) {
      ctx.status = 400;
      ctx.body = {
        error: "product_name is required",
        validation_errors: [
          { field: "product_name", message: "product_name is required" },
        ],
      };
      return;
    }

    if (!productData.category) {
      ctx.status = 400;
      ctx.body = {
        error: "category is required",
        validation_errors: [
          { field: "category", message: "category is required" },
        ],
      };
      return;
    }

    if (
      productData.latest_price === undefined ||
      productData.latest_price === null
    ) {
      ctx.status = 400;
      ctx.body = {
        error: "latest_price is required",
        validation_errors: [
          { field: "latest_price", message: "latest_price is required" },
        ],
      };
      return;
    }

    // Validate image requirement - either file upload, image URL, or download URL
    const hasImageFile = productImage;
    const hasImageUrl =
      productData.image_url && productData.image_url.trim() !== "";
    const hasDownloadUrl =
      productData.download_image_url &&
      productData.download_image_url.trim() !== "";

    if (!hasImageFile && !hasImageUrl && !hasDownloadUrl) {
      ctx.status = 400;
      ctx.body = {
        error:
          "image is required - provide either a file upload, image_url, or download_image_url",
        validation_errors: [
          {
            field: "image",
            message:
              "image is required - provide either a file upload, image_url, or download_image_url",
          },
        ],
      };
      return;
    }

    // Check if product with meta_sku already exists
    const existingProduct = await Product.first((qb) =>
      qb
        .where("meta_sku", productData.meta_sku)
        .where("location_id", locationId)
    );

    if (existingProduct) {
      ctx.status = 409;
      ctx.body = {
        error: `Product with meta_sku '${productData.meta_sku}' already exists`,
      };
      return;
    }

    // Handle image processing
    if (hasImageFile) {
      // Process uploaded file from FileStream
      const fileName =
        productImage.metadata.fileName || `product-${Date.now()}`;
      const contentType = productImage.metadata.mimeType || "image/jpeg";
      const fileSize = productImage.metadata.size;

      if (!contentType.startsWith("image/")) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid file type. Only image files are allowed.",
          validation_errors: [
            {
              field: "productImage",
              message: "Invalid file type. Only image files are allowed.",
            },
          ],
        };
        return;
      }

      logger.info({
        message: "Processing uploaded FileStream",
        fileName,
        contentType,
        fileSize,
        fieldName: productImage.metadata.fieldName,
      });

      // Get file data as buffer from the stream (same pattern as PATCH endpoint)
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        productImage.file.on("data", (chunk: Buffer) => chunks.push(chunk));
        productImage.file.on("end", () => resolve(Buffer.concat(chunks)));
        productImage.file.on("error", reject);
      });

      logger.info({
        message: "File stream read successfully",
        bufferSize: buffer.length,
        expectedSize: fileSize,
      });

      // Store the image
      const result = await productImageService.storeProductImage(
        locationId,
        buffer,
        fileName,
        contentType,
        fileSize
      );

      imageUrl = result.url;
      logger.info(`Successfully uploaded product image: ${result.url}`);
    } else if (hasDownloadUrl) {
      // Download and store image from URL
      const result = await productImageService.downloadAndStoreProductImage(
        locationId,
        productData.download_image_url!,
        `product-${productData.product_name}`
      );

      imageUrl = result.url;
      logger.info(
        `Successfully downloaded and stored product image: ${result.url}`
      );
    } else if (hasImageUrl) {
      // Use provided image URL directly
      imageUrl = productData.image_url!;
    }

    // Set default values
    const productToCreate: ProductInternalParams = {
      ...productData,
      meta_sku: productData.meta_sku!, // We've already validated this exists
      location_id: locationId,
      product_id: productData.product_id || productData.meta_sku,
      retailer_id: productData.retailer_id || `loc_${locationId}`,
      raw_product_name:
        productData.raw_product_name || productData.product_name!,
      medical: productData.medical !== undefined ? productData.medical : false,
      recreational:
        productData.recreational !== undefined
          ? productData.recreational
          : true,
      source: productData.source || "manual",
      image_url: imageUrl!, // Use the processed image URL
    };

    // Remove download_image_url from the product data as it's not a product field
    delete (productToCreate as any).download_image_url;

    // Create the product using ProductPatchJob for consistency
    const product = await ProductPatchJob.handler({
      location_id: locationId,
      product: productToCreate,
    });

    logger.info({
      message: "Product created successfully with image",
      product_id: product.id,
      meta_sku: product.meta_sku,
      image_url: imageUrl,
      location_id: locationId,
    });

    ctx.status = 201;
    ctx.body = product.toJSON();
  } catch (error) {
    logger.error({
      message: "Error creating product",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      location_id: ctx.state?.location?.id,
    });

    if (error instanceof Error && error.message.includes("Duplicate entry")) {
      ctx.status = 409;
      ctx.body = {
        error: "Product with this meta_sku already exists",
      };
    } else {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error ? error.message : "Failed to create product",
      };
    }
  }
});

/**
 * @swagger
 * /products/upload:
 *   post:
 *     summary: Upload Products
 *     description: Uploads products from a file or JSON data
 *     tags: [Product]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or JSON file containing product data
 *               reindex:
 *                 type: boolean
 *                 description: Whether to reindex the products after import
 *               enhance_with_ai:
 *                 type: boolean
 *                 description: Whether to enhance products with AI
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               product_data:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/ProductParams'
 *               reindex:
 *                 type: boolean
 *               enhance_with_ai:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Products uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 imported:
 *                   type: integer
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Invalid input
 */
router.post("/upload", locationRoleMiddleware("editor"), async (ctx) => {
  // Get reindex flag from query params or body
  const shouldReindex =
    ctx.request.query.reindex === "true" || ctx.request.body?.reindex === true;
  const enhanceWithAI =
    ctx.request.query.enhance_with_ai === "true" ||
    ctx.request.body?.enhance_with_ai === true;

  // Check if this is a direct JSON data import
  if (ctx.request.body?.product_data) {
    try {
      // Create normalized data directly
      const normalizedData: NormalizedData = {
        type: "product",
        data: Array.isArray(ctx.request.body.product_data)
          ? ctx.request.body.product_data
          : [ctx.request.body.product_data],
        errors: [],
      };

      // Import the normalized data
      const result = await importProducts({
        location_id: ctx.state.location.id,
        normalization_data: normalizedData,
        enhance_with_ai: enhanceWithAI,
        reindex: shouldReindex,
      });

      ctx.status = 200;
      ctx.body = {
        message: "Product data imported successfully",
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to import product data",
        details: error instanceof Error ? error.stack : undefined,
        validation_errors:
          error instanceof Error && error.name === "ValidationError"
            ? (error as any).validationErrors || []
            : [],
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    const result = await importProducts({
      location_id: ctx.state.location.id,
      stream,
      enhance_with_ai: enhanceWithAI,
      reindex: shouldReindex,
    });

    ctx.status = 200;
    ctx.body = {
      message: "Products imported successfully",
      ...result,
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error
          ? error.message
          : "Failed to import product data",
      details: error instanceof Error ? error.stack : undefined,
      validation_errors:
        error instanceof Error && error.name === "ValidationError"
          ? (error as any).validationErrors || []
          : [],
    };
  }
});

/**
 * @swagger
 * /products/enhance:
 *   post:
 *     summary: Enhance Products with AI
 *     description: Enhances product data using AI and optionally reindexes
 *     tags: [Product]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of products to process
 *       - in: query
 *         name: skip
 *         schema:
 *           type: integer
 *         description: Number of products to skip
 *       - in: query
 *         name: meta_sku
 *         schema:
 *           type: string
 *         description: Specific product SKU to enhance
 *       - in: query
 *         name: async
 *         schema:
 *           type: boolean
 *         description: Whether to run enhancement asynchronously
 *     responses:
 *       200:
 *         description: Products enhanced successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 enhanced:
 *                   type: integer
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Invalid input
 */
router.post("/enhance", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const {
      limit = 100,
      skip = 0,
      meta_sku,
      async = false,
    } = ctx.request.query as {
      limit?: string;
      skip?: string;
      meta_sku?: string;
      async?: string;
    };

    // Parse async flag, defaulting to false
    const isAsync = async === "true";

    // Initialize product data vector service
    await ProductDataVectorService.ensureIndexExists();

    // Query to get products to enhance
    const queryBuilder = (qb: any) => {
      qb.where("location_id", ctx.state.location.id);

      // Filter by meta_sku if provided
      if (meta_sku) {
        qb.where("meta_sku", meta_sku);
      }

      qb.limit(parseInt(limit as string) || 100).offset(
        parseInt(skip as string) || 0
      );

      return qb;
    };

    // Get products to enhance
    const products = await Product.all(queryBuilder);

    if (products.length === 0) {
      ctx.body = {
        message: "No products found that need enhancement",
        count: 0,
      };
      return;
    }

    logger.info(`Enhancing ${products.length} products with AI`);

    if (isAsync) {
      // Process asynchronously - just queue and return immediately
      const AIEnhancementJob = require("./AIEnhancementJob").default;
      await App.main.queue.enqueue(
        AIEnhancementJob.from({
          location_id: ctx.state.location.id,
          products: products.map((p) => p.id),
          options: {
            limit: parseInt(limit as string) || 100,
            skip: parseInt(skip as string) || 0,
            meta_sku,
          },
        })
      );

      ctx.body = {
        message: "Product enhancement queued for processing",
        status: "processing",
        count: products.length,
        async: true,
      };
      return;
    }

    // Process synchronously (old way)
    // Enhance products with AI
    const enhancedProducts = [];
    const batchSize = 10;

    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);

      // Use the new batchEnhanceProducts method for cost optimization
      const enhancedBatch =
        await ProductAIEnhancementService.batchEnhanceProducts(
          batch.map((product) => product.toJSON() as unknown as ProductParams)
        );

      // Save enhanced products
      for (let j = 0; j < enhancedBatch.length; j++) {
        await App.main.queue.enqueue(
          ProductPatchJob.from({
            location_id: ctx.state.location.id,
            product: {
              ...enhancedBatch[j],
              location_id: ctx.state.location.id,
            },
          })
        );
      }

      enhancedProducts.push(...enhancedBatch);
      logger.info(
        `Enhanced batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
          products.length / batchSize
        )}`
      );
    }

    // Reindex enhanced products in vector database
    const reindexResult = await ProductDataVectorService.upsertProductData(
      products
    );

    ctx.body = {
      message: "Products enhanced and reindexed successfully",
      enhanced_count: enhancedProducts.length,
      vectorized_count: reindexResult.successCount,
      failed_vectorization: reindexResult.errorCount,
      async: false,
    };
  } catch (error) {
    logger.error("Error enhancing products:", error);
    ctx.status = 500;
    ctx.body = {
      message: "Error enhancing products",
      error: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /products/bulk-vectorize:
 *   post:
 *     summary: Bulk Vectorize Enhanced Products
 *     description: Triggers bulk vectorization of enhanced products (run after AI enhancement jobs complete)
 *     tags: [Product]
 *     parameters:
 *       - in: query
 *         name: enhancement_status
 *         schema:
 *           type: string
 *         description: Filter by enhancement status (e.g., 'complete')
 *       - in: query
 *         name: updated_since
 *         schema:
 *           type: string
 *         description: Filter by products updated since this timestamp
 *       - in: query
 *         name: batch_size
 *         schema:
 *           type: integer
 *         description: Number of products to process in each batch
 *     responses:
 *       200:
 *         description: Bulk vectorization started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 status:
 *                   type: string
 *                 queued:
 *                   type: boolean
 *       400:
 *         description: Invalid input
 */
router.post(
  "/bulk-vectorize",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    try {
      const {
        enhancement_status = "complete",
        updated_since,
        batch_size = 100,
      } = ctx.request.query as {
        enhancement_status?: string;
        updated_since?: string;
        batch_size?: string;
      };

      const location_id = ctx.state.location.id;

      logger.info(`Starting bulk vectorization for location ${location_id}`);

      // Import and queue the bulk vectorization job
      const ProductDataVectorJob = require("./ProductDataVectorJob").default;

      await App.main.queue.enqueue(
        ProductDataVectorJob.from({
          location_id,
          filter: {
            enhancement_status,
            updated_since,
          },
          batch_size: parseInt(batch_size as string) || 100,
        })
      );

      ctx.body = {
        message: "Bulk vectorization job queued successfully",
        status: "processing",
        queued: true,
        filter: {
          enhancement_status,
          updated_since,
        },
      };
    } catch (error) {
      logger.error("Error starting bulk vectorization:", error);
      ctx.status = 500;
      ctx.body = {
        message: "Error starting bulk vectorization",
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /products/re-vectorize:
 *   post:
 *     summary: Re-vectorize Products
 *     description: Triggers re-vectorization of product data
 *     tags: [Product]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batch_size:
 *                 type: integer
 *                 description: Number of products to process in each batch
 *               clean_start:
 *                 type: boolean
 *                 description: Whether to delete existing vectors before re-vectorization
 *     responses:
 *       200:
 *         description: Re-vectorization started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 status:
 *                   type: string
 *                 job_id:
 *                   type: string
 *                 job_type:
 *                   type: string
 *                 clean_start:
 *                   type: boolean
 *       500:
 *         description: Internal server error
 */
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const location_id = ctx.state.location.id;
  const { batch_size = 100, clean_start = true } = ctx.request.body;

  try {
    // First ensure the index exists
    await ProductDataVectorService.ensureIndexExists();

    // If clean_start is true, delete all existing vectors for this location first
    if (clean_start) {
      logger.info(
        `Deleting existing vectors for location ${location_id} before re-vectorization`
      );
      await ProductDataVectorService.deleteProductData(location_id);
      logger.info(
        `Successfully deleted existing vectors for location ${location_id}`
      );
    }

    // Start a job tracker for this process
    const jobType = "product_data_vectorization";
    OnboardingJobTracker.startJob(location_id, jobType);

    // Import ProductDataVectorJob and ensure it's properly queued
    const ProductDataVectorJob = require("./ProductDataVectorJob").default;

    // Create and queue a new job with proper parameters
    await ProductDataVectorJob.from({
      location_id,
      batch_size,
      last_processed_id: 0, // Start from the beginning
    }).queue();

    logger.info(`Re-vectorization job queued for location ${location_id}`);

    ctx.status = 200;
    ctx.body = {
      message: "Product data re-vectorization started",
      status: "processing",
      job_id: location_id,
      job_type: jobType,
      clean_start,
    };
  } catch (error) {
    logger.error(
      `Error starting re-vectorization for location ${location_id}:`,
      error
    );
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /products/vectorization-status:
 *   get:
 *     summary: Get Vectorization Status
 *     description: Retrieves the status of product data vectorization
 *     tags: [Product]
 *     responses:
 *       200:
 *         description: Vectorization status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isProcessing:
 *                   type: boolean
 *                 total:
 *                   type: integer
 *                 completed:
 *                   type: integer
 *                 failed:
 *                   type: integer
 *                 pending:
 *                   type: integer
 *                 processing:
 *                   type: integer
 *                 jobs:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       jobType:
 *                         type: string
 *                       status:
 *                         type: string
 *                       startedAt:
 *                         type: string
 *                         format: date-time
 *                       completedAt:
 *                         type: string
 *                         format: date-time
 *                       error:
 *                         type: string
 *       500:
 *         description: Internal server error
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;

    try {
      const jobType = "product_data_vectorization";
      const allJobsSummary = OnboardingJobTracker.getSummary(location_id);

      // Filter jobs to only include those of the product_data_vectorization type
      const jobsOfThisType = allJobsSummary.jobs.filter(
        (job) => job.jobType === jobType
      );

      // Get only the latest job (most recently started)
      const latestJob =
        jobsOfThisType.length > 0
          ? jobsOfThisType.sort(
              (a, b) =>
                new Date(b.startTime || 0).getTime() -
                new Date(a.startTime || 0).getTime()
            )[0]
          : null;

      // Create summary based only on the latest job
      const summary = {
        isProcessing: latestJob?.status === "processing" || false,
        total: latestJob ? 1 : 0,
        completed: latestJob?.status === "completed" ? 1 : 0,
        failed: latestJob?.status === "failed" ? 1 : 0,
        pending: latestJob?.status === "pending" ? 1 : 0,
        processing: latestJob?.status === "processing" ? 1 : 0,
        jobs: latestJob ? [latestJob] : [],
      };

      // Get current vectorization statistics
      const countResult = await Product.query()
        .where("location_id", location_id)
        .count("id as total")
        .first();

      const dbRecordCount = countResult
        ? parseInt(countResult.total as string, 10)
        : 0;

      // Get vector stats using namespace-based approach
      const vectorStats = await ProductDataVectorService.getLocationStats(
        location_id
      );

      // Determine detailed vectorization status
      let vectorizationStatus = "unknown";
      if (dbRecordCount === 0) {
        vectorizationStatus = "no_data";
      } else if (vectorStats.recordCount === 0) {
        vectorizationStatus = "not_started";
      } else if (vectorStats.recordCount === dbRecordCount) {
        vectorizationStatus = "complete";
      } else if (vectorStats.recordCount < dbRecordCount) {
        vectorizationStatus = "incomplete";
      } else if (vectorStats.recordCount > dbRecordCount) {
        vectorizationStatus = "excess"; // More vectors than records
      }

      // Determine job status from tracker
      const jobStatus = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // Check if we have a recently completed job (within last 30 seconds) to handle race condition
      const recentlyCompletedJob =
        latestJob &&
        latestJob.status === "completed" &&
        latestJob.endTime &&
        Date.now() - new Date(latestJob.endTime).getTime() < 30000 // 30 seconds
          ? latestJob
          : null;

      // Combined status that takes both job and vector counts into account
      let combinedStatus = jobStatus;

      // Handle race condition: if job completed recently but no vectors yet, keep status as processing
      if (
        recentlyCompletedJob &&
        vectorStats.recordCount === 0 &&
        dbRecordCount > 0
      ) {
        combinedStatus = "processing";
        logger.info(
          `Job completed recently but vectors not yet written for location ${location_id}, keeping status as processing`
        );
      } else if (
        jobStatus === "completed" &&
        vectorizationStatus !== "complete"
      ) {
        // If job shows completed but vectors don't match DB, override the status
        if (vectorStats.recordCount > 0) {
          combinedStatus = "incomplete";
          logger.info(
            `Job shows completed but only ${vectorStats.recordCount}/${dbRecordCount} vectors found for location ${location_id}`
          );
        } else {
          combinedStatus = "incomplete"; // Corrected from "not_started"
          logger.info(
            `Job shows completed but no vectors found for location ${location_id}, marking as incomplete.`
          );
        }
      }

      // If job not started but we have vectors, report proper status based on vector count
      if (jobStatus === "not_started" && vectorStats.recordCount > 0) {
        if (vectorStats.recordCount === dbRecordCount) {
          combinedStatus = "completed";
          logger.info(
            `No job record but full vectorization confirmed with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        } else {
          combinedStatus = "incomplete";
          logger.info(
            `No job record but partial vectorization detected with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        }
      }

      // Calculate completion percentage
      const completionPercentage =
        dbRecordCount > 0
          ? Math.round((vectorStats.recordCount / dbRecordCount) * 100)
          : 0;

      ctx.body = {
        // Combined final status
        status: combinedStatus,

        // Detailed information
        job_status: jobStatus,
        job_summary: summary,

        // Vector statistics
        namespace: vectorStats.namespace,
        vector_count: vectorStats.recordCount,
        db_record_count: dbRecordCount,
        vector_status: vectorizationStatus,
        is_fully_indexed: vectorStats.recordCount === dbRecordCount,
        completion_percentage: completionPercentage,

        // Include error information if any
        error: vectorStats.error,
      };
    } catch (error) {
      logger.error(
        `Error getting vectorization status for location ${location_id}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

router.patch("/", locationRoleMiddleware("editor"), async (ctx) => {
  const productDiffs = validate(patchProductsRequest, ctx.request.body);

  for (const productDiff of productDiffs) {
    if (!productDiff) continue;

    if (!productDiff.meta_sku) {
      logger.warn(
        "Skipping batch product patch: meta_sku is missing",
        productDiff
      );
      continue;
    }

    const currentProduct = await Product.query()
      .where({
        meta_sku: productDiff.meta_sku,
        location_id: ctx.state.location.id,
      })
      .first();

    if (!currentProduct) {
      logger.warn(
        `Skipping batch product patch: Product with meta_sku ${productDiff.meta_sku} not found for location ${ctx.state.location.id}`
      );
      continue;
    }

    const mergedProductData = {
      ...currentProduct.toJSON(),
      ...productDiff,
    };

    const finalAiEnhancedFields = currentProduct.ai_enhanced_fields
      ? [...currentProduct.ai_enhanced_fields]
      : [];
    let finalEnhancementStatus = currentProduct.enhancement_status;

    if (
      currentProduct.ai_enhanced_fields &&
      Array.isArray(currentProduct.ai_enhanced_fields) &&
      currentProduct.ai_enhanced_fields.length > 0
    ) {
      for (const enhancedField of currentProduct.ai_enhanced_fields) {
        if (Object.prototype.hasOwnProperty.call(productDiff, enhancedField)) {
          const originalValue = (currentProduct as any)[enhancedField];
          const newValueInDiff = (productDiff as any)[enhancedField];
          const isChanged = !isEqual(originalValue, newValueInDiff);

          if (isChanged) {
            const index = finalAiEnhancedFields.indexOf(enhancedField);
            if (index !== -1) {
              finalAiEnhancedFields.splice(index, 1);
            }
          }
        }
      }

      if (finalAiEnhancedFields.length === 0) {
        finalEnhancementStatus = "manual";
      } else if (
        finalAiEnhancedFields.length < currentProduct.ai_enhanced_fields.length
      ) {
        finalEnhancementStatus = "partial";
      }
    }

    mergedProductData.ai_enhanced_fields = finalAiEnhancedFields;
    mergedProductData.enhancement_status = finalEnhancementStatus;

    await App.main.queue.enqueue(
      ProductPatchJob.from({
        location_id: ctx.state.location.id,
        product: mergedProductData as any,
      })
    );
  }

  ctx.status = 204;
  ctx.body = "";
});

/**
 * @swagger
 * /products/bulk-delete:
 *   delete:
 *     summary: Bulk Delete Products
 *     description: Deletes multiple products by their IDs
 *     tags: [Product]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - product_ids
 *             properties:
 *               product_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of product IDs to delete
 *                 minItems: 1
 *                 maxItems: 100
 *     responses:
 *       200:
 *         description: Products deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 deleted_count:
 *                   type: integer
 *                 queued_jobs:
 *                   type: integer
 *       400:
 *         description: Invalid input
 *       404:
 *         description: One or more products not found
 */
router.delete("/bulk-delete", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const { product_ids } = ctx.request.body;

    // Validate input
    if (!Array.isArray(product_ids) || product_ids.length === 0) {
      ctx.status = 400;
      ctx.body = {
        error: "product_ids array is required and cannot be empty",
      };
      return;
    }

    if (product_ids.length > 100) {
      ctx.status = 400;
      ctx.body = {
        error: "Cannot delete more than 100 products at once",
      };
      return;
    }

    // Validate that all product_ids are valid numbers
    const invalidIds = product_ids.filter(
      (id) => !Number.isInteger(id) || id <= 0
    );
    if (invalidIds.length > 0) {
      ctx.status = 400;
      ctx.body = {
        error: "All product_ids must be positive integers",
        invalid_ids: invalidIds,
      };
      return;
    }

    // Validate that all products exist and belong to this location
    const products = await App.main
      .db("products")
      .whereIn("id", product_ids)
      .where("location_id", locationId)
      .select("id", "meta_sku");

    if (products.length !== product_ids.length) {
      const foundIds = products.map((p) => p.id);
      const missingIds = product_ids.filter((id) => !foundIds.includes(id));

      ctx.status = 404;
      ctx.body = {
        error:
          "One or more products not found or don't belong to this location",
        missing_ids: missingIds,
      };
      return;
    }

    // Queue deletion jobs for each product
    const deleteJobs = products.map((product) =>
      ProductDeleteJob.from({
        location_id: locationId,
        meta_sku: product.meta_sku,
      })
    );

    // Enqueue all deletion jobs
    await Promise.all(deleteJobs.map((job) => App.main.queue.enqueue(job)));

    logger.info({
      message: "Bulk product deletion jobs queued",
      locationId,
      productCount: products.length,
      productIds: product_ids,
    });

    ctx.body = {
      message: `Successfully queued ${products.length} products for deletion`,
      deleted_count: products.length,
      queued_jobs: products.length,
    };
  } catch (error) {
    logger.error({
      message: "Error bulk deleting products",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to delete products" };
  }
});

router.delete("/", locationRoleMiddleware("editor"), async (ctx) => {
  let productIds = ctx.request.query.product_id || [];
  if (!Array.isArray(productIds)) {
    productIds = productIds.length ? [productIds] : [];
  }

  productIds = validate(deleteProductsRequest, productIds);

  for (const metaSku of productIds) {
    await App.main.queue.enqueue(
      ProductDeleteJob.from({
        location_id: ctx.state.location.id,
        meta_sku: metaSku,
      })
    );
  }

  ctx.status = 204;
  ctx.body = "";
});

router.param("productId", async (value, ctx, next) => {
  // Skip parameter processing for non-numeric values (like "bulk-delete")
  const productId = parseInt(value);
  if (isNaN(productId)) {
    return await next();
  }

  // Get product with featured status included
  const productWithFeatured = await App.main
    .db("products")
    .where("products.id", productId)
    .where("products.location_id", ctx.state.location.id)
    .leftJoin("featured_products", function () {
      this.on("featured_products.product_id", "=", "products.id").andOn(
        "featured_products.location_id",
        "=",
        "products.location_id"
      );
    })
    .select(
      "products.*",
      App.main.db.raw(
        "CASE WHEN featured_products.id IS NOT NULL THEN true ELSE false END as is_featured"
      ),
      App.main.db.raw(
        "COALESCE(featured_products.is_top_pick, false) as is_top_pick"
      ),
      App.main.db.raw("featured_products.sort_order as featured_sort_order"),
      App.main.db.raw(
        "COALESCE(featured_products.active, false) as featured_active"
      ),
      App.main.db.raw("featured_products.id as featured_id")
    )
    .first();

  if (!productWithFeatured) {
    ctx.throw(404);
    return;
  }

  ctx.state.product = productWithFeatured;
  return await next();
});

router.get("/:productId", async (ctx) => {
  ctx.body = ctx.state.product;
});

router.patch("/:productId", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const currentProduct = ctx.state.product;
    // Use the imported productImageService instance

    if (!currentProduct) {
      ctx.throw(404, "Product not found");
      return;
    }

    // Handle both JSON and multipart/form-data requests
    let productDiff: Partial<ProductParams>;
    let productImage: any = null;
    let imageUrl: string | undefined;

    // Check if this is a multipart request by looking at content-type
    const contentType = ctx.request.headers["content-type"] || "";
    const isMultipart = contentType.includes("multipart/form-data");

    logger.info({
      message: "PATCH request analysis",
      contentType,
      isMultipart,
      hasRequestFiles: !!ctx.request.files,
      requestBodyType: typeof ctx.request.body,
      requestBodyKeys: ctx.request.body ? Object.keys(ctx.request.body) : [],
    });

    if (isMultipart) {
      // Parse multipart form data
      const formData = await parseMultipartForm(ctx);

      // Extract product data and image
      const rawProductDiff = formData.fields || {};
      productImage = formData.files.productImage;

      // Convert string values to proper types for multipart form data
      productDiff = {
        ...rawProductDiff,
        // Convert boolean fields
        medical: rawProductDiff.medical === "true",
        recreational: rawProductDiff.recreational === "true",
        // Convert number fields
        latest_price: rawProductDiff.latest_price
          ? parseFloat(rawProductDiff.latest_price)
          : undefined,
        location_id: rawProductDiff.location_id
          ? parseInt(rawProductDiff.location_id)
          : undefined,
        brand_id: rawProductDiff.brand_id
          ? parseInt(rawProductDiff.brand_id)
          : undefined,
        percentage_thc: rawProductDiff.percentage_thc
          ? parseFloat(rawProductDiff.percentage_thc)
          : undefined,
        percentage_cbd: rawProductDiff.percentage_cbd
          ? parseFloat(rawProductDiff.percentage_cbd)
          : undefined,
        mg_thc: rawProductDiff.mg_thc
          ? parseFloat(rawProductDiff.mg_thc)
          : undefined,
        mg_cbd: rawProductDiff.mg_cbd
          ? parseFloat(rawProductDiff.mg_cbd)
          : undefined,
        quantity_per_package: rawProductDiff.quantity_per_package
          ? parseInt(rawProductDiff.quantity_per_package)
          : undefined,
        rating: rawProductDiff.rating
          ? parseFloat(rawProductDiff.rating)
          : undefined,
        reviews_count: rawProductDiff.reviews_count
          ? parseInt(rawProductDiff.reviews_count)
          : undefined,
        // Parse JSON fields
        mood: rawProductDiff.mood ? JSON.parse(rawProductDiff.mood) : undefined,
        product_tags: rawProductDiff.product_tags
          ? JSON.parse(rawProductDiff.product_tags)
          : undefined,
        effects: rawProductDiff.effects
          ? rawProductDiff.effects === '""'
            ? ""
            : rawProductDiff.effects
          : undefined,
      };

      logger.info({
        message: "PATCH: Multipart request detected and parsed",
        rawProductDiff,
        productDiff,
        hasProductImage: !!productImage,
        productImageType: productImage?.mimetype,
        productImageSize: productImage?.size,
        productImagePath: productImage?.filepath,
        formDataKeys: Object.keys(formData.fields || {}),
        fileKeys: Object.keys(formData.files || {}),
      });
    } else {
      // Regular JSON request
      productDiff = validate(singleProductPatchRequest, ctx.request.body);
    }

    // Handle image processing
    const hasImageFile = productImage;
    const hasImageUrl =
      productDiff.image_url && productDiff.image_url.trim() !== "";
    const hasDownloadUrl =
      (productDiff as any).download_image_url &&
      (productDiff as any).download_image_url.trim() !== "";

    if (hasImageFile) {
      // Process uploaded file from FileStream
      const fileName =
        productImage.metadata.fileName ||
        `product-${currentProduct.meta_sku}-${Date.now()}`;
      const contentType = productImage.metadata.mimeType || "image/jpeg";
      const fileSize = productImage.metadata.size;

      if (!contentType.startsWith("image/")) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid file type. Only image files are allowed.",
          validation_errors: [
            {
              field: "productImage",
              message: "Invalid file type. Only image files are allowed.",
            },
          ],
        };
        return;
      }

      logger.info({
        message: "Processing uploaded FileStream",
        fileName,
        contentType,
        fileSize,
        fieldName: productImage.metadata.fieldName,
      });

      // Get file data as buffer from the stream (same pattern as POST endpoint)
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        productImage.file.on("data", (chunk: Buffer) => chunks.push(chunk));
        productImage.file.on("end", () => resolve(Buffer.concat(chunks)));
        productImage.file.on("error", reject);
      });

      logger.info({
        message: "File stream read successfully",
        bufferSize: buffer.length,
        expectedSize: fileSize,
      });

      // Store the image
      const result = await productImageService.storeProductImage(
        locationId,
        buffer,
        fileName,
        contentType,
        fileSize
      );

      imageUrl = result.url;
      productDiff.image_url = imageUrl;
      logger.info({
        message: "Successfully uploaded product image",
        imageUrl: result.url,
        productDiffAfterImage: productDiff,
      });
    } else if (hasDownloadUrl) {
      // Download and store image from URL
      const result = await productImageService.downloadAndStoreProductImage(
        locationId,
        (productDiff as any).download_image_url!,
        `product-${currentProduct.meta_sku}`
      );

      imageUrl = result.url;
      productDiff.image_url = imageUrl;
      logger.info(
        `Successfully downloaded and stored product image: ${result.url}`
      );
    }

    // Remove download_image_url from the product diff as it's not a product field
    delete (productDiff as any).download_image_url;

    // Handle featured product updates if any featured fields are in the diff
    const featuredFields = [
      "is_featured",
      "is_top_pick",
      "featured_sort_order",
      "featured_active",
    ];
    const featuredUpdates: any = {};
    let hasFeaturedChanges = false;

    // Extract featured field changes from productDiff
    for (const field of featuredFields) {
      if (Object.prototype.hasOwnProperty.call(productDiff, field)) {
        featuredUpdates[field] = (productDiff as any)[field];
        hasFeaturedChanges = true;
        // Remove from productDiff since it's not a product table column
        delete (productDiff as any)[field];
      }
    }

    // Update featured_products table if there are featured changes
    if (hasFeaturedChanges) {
      const productId = currentProduct.id;

      // Check if product is currently featured
      const existingFeatured = await App.main
        .db("featured_products")
        .where("location_id", locationId)
        .where("product_id", productId)
        .first();

      if (featuredUpdates.is_featured === false) {
        // Remove from featured products
        if (existingFeatured) {
          await App.main
            .db("featured_products")
            .where("id", existingFeatured.id)
            .del();

          logger.info({
            message: "Removed product from featured products",
            product_id: productId,
            location_id: locationId,
          });
        }
      } else if (featuredUpdates.is_featured === true || existingFeatured) {
        // Add to featured products or update existing
        const updateData: any = { updated_at: new Date() };

        if (featuredUpdates.is_top_pick !== undefined) {
          updateData.is_top_pick = featuredUpdates.is_top_pick;
        }
        if (featuredUpdates.featured_sort_order !== undefined) {
          updateData.sort_order = featuredUpdates.featured_sort_order;
        }
        if (featuredUpdates.featured_active !== undefined) {
          updateData.active = featuredUpdates.featured_active;
        }

        if (existingFeatured) {
          // Update existing featured product
          await App.main
            .db("featured_products")
            .where("id", existingFeatured.id)
            .update(updateData);

          logger.info({
            message: "Updated existing featured product",
            featured_id: existingFeatured.id,
            product_id: productId,
            location_id: locationId,
            updates: updateData,
          });
        } else {
          // Create new featured product entry
          const insertData = {
            location_id: locationId,
            product_id: productId,
            is_top_pick: featuredUpdates.is_top_pick || false,
            sort_order: featuredUpdates.featured_sort_order || null,
            active: featuredUpdates.featured_active !== false, // Default to true
            created_at: new Date(),
            updated_at: new Date(),
          };

          await App.main.db("featured_products").insert(insertData);

          logger.info({
            message: "Added product to featured products",
            product_id: productId,
            location_id: locationId,
            data: insertData,
          });
        }
      }
    }

    // Filter out virtual featured product fields that come from the JOIN but aren't actual product columns
    const {
      is_featured,
      is_top_pick,
      featured_sort_order,
      featured_active,
      featured_id,
      ...actualProductData
    } = currentProduct;

    const mergedProductData = {
      ...actualProductData,
      ...productDiff,
    };

    const finalAiEnhancedFields = currentProduct.ai_enhanced_fields
      ? [...currentProduct.ai_enhanced_fields]
      : [];
    let finalEnhancementStatus = currentProduct.enhancement_status;

    if (
      currentProduct.ai_enhanced_fields &&
      Array.isArray(currentProduct.ai_enhanced_fields) &&
      currentProduct.ai_enhanced_fields.length > 0
    ) {
      for (const enhancedField of currentProduct.ai_enhanced_fields) {
        if (Object.prototype.hasOwnProperty.call(productDiff, enhancedField)) {
          const originalValue = (currentProduct as any)[enhancedField];
          const newValueInDiff = (productDiff as any)[enhancedField];
          const isChanged = !isEqual(originalValue, newValueInDiff);

          if (isChanged) {
            const index = finalAiEnhancedFields.indexOf(enhancedField);
            if (index !== -1) {
              finalAiEnhancedFields.splice(index, 1);
            }
          }
        }
      }

      if (finalAiEnhancedFields.length === 0) {
        finalEnhancementStatus = "manual";
      } else if (
        finalAiEnhancedFields.length < currentProduct.ai_enhanced_fields.length
      ) {
        finalEnhancementStatus = "partial";
      }
    }

    mergedProductData.ai_enhanced_fields = finalAiEnhancedFields;
    mergedProductData.enhancement_status = finalEnhancementStatus;

    // Use ProductPatchJob.handler directly for immediate processing (same as POST endpoint)
    const updatedProduct = await ProductPatchJob.handler({
      location_id: ctx.state.location.id,
      product: mergedProductData as any,
    });

    logger.info({
      message: "Product updated successfully with image",
      product_id: updatedProduct.id,
      meta_sku: updatedProduct.meta_sku,
      image_url: imageUrl ?? "no image change",
      location_id: locationId,
    });

    ctx.body = updatedProduct.toJSON();
  } catch (error) {
    logger.error({
      message: "Error updating product",
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      product_id: ctx.state?.product?.id,
      location_id: ctx.state?.location?.id,
    });

    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error ? error.message : "Failed to update product",
    };
  }
});

router.delete("/:productId", locationRoleMiddleware("editor"), async (ctx) => {
  await App.main.queue.enqueue(
    ProductDeleteJob.from({
      location_id: ctx.state.location.id,
      meta_sku: ctx.state.product!.meta_sku,
    })
  );

  ctx.status = 204;
  ctx.body = "";
});

// Add a new endpoint to sync products from the marketplace
router.post(
  "/sync-marketplace",
  locationRoleMiddleware("admin"),
  async (ctx) => {
    const { retailer_id, skip_scrape = false } = ctx.request.body as {
      retailer_id: string;
      skip_scrape?: boolean;
    };
    let locationId: number | undefined;
    const jobType: string = "marketplace_sync"; // Assign default value

    try {
      locationId = ctx.state.location.id; // Assign inside try
      const location = ctx.state.location;
      // jobType is already defined

      if (!retailer_id) {
        ctx.status = 400;
        ctx.body = {
          error: "Retailer ID is required",
        };
        return;
      }

      // Start a job tracker
      OnboardingJobTracker.startJob(locationId, jobType);

      logger.info(
        `Starting marketplace sync for location ${locationId} with retailer ${retailer_id}. Skip scrape: ${skip_scrape}`
      );

      // --- Skip Scrape Logic ---
      if (skip_scrape) {
        logger.info(
          `Skipping live scrape. Syncing directly from DB for retailer ${retailer_id}`
        );

        const result = await importCurrentProducts(locationId, retailer_id);
        OnboardingJobTracker.completeJob(locationId, jobType);

        ctx.body = {
          success: true,
          message: `Successfully synced ${result.processed} products directly from the database.`,
          syncedProducts: result.processed,
          aiEnhanced: result.ai_enhanced,
          reindexed: result.reindexed,
          scrapeInitiated: false, // Explicitly false
          status: "completed", // Indicate immediate completion
        };
        return; // Exit early
      }

      // --- Default Logic (Scrape and Delay) ---
      try {
        const scrapeUrl =
          "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus";
        logger.info(
          `Triggering rescrape for retailer ${retailer_id} in state ${location.state}`
        );

        // Declare variables before the if/else to ensure they are in scope
        let productCount: number = 0;
        let estimatedScrapeTimeMinutes: number = 5; // Default to 5 mins

        // Make request to scrape service
        const scrapeResponse = await fetch(scrapeUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            retailer_ids: [retailer_id],
            states: [location.state],
          }),
        });

        if (!scrapeResponse.ok) {
          const errorText = await scrapeResponse.text();
          logger.warn(
            `Scrape API returned non-200 response: ${scrapeResponse.status}, ${errorText}`
          );

          // Even if scrape fails, we'll proceed with importing current data
          const fallbackResult = await importCurrentProducts(
            locationId,
            retailer_id
          );

          // Respond immediately with fallback result
          ctx.body = {
            success: true,
            message: `Scraping failed. Synced ${fallbackResult.processed} products from current database data.`,
            syncedProducts: fallbackResult.processed,
            aiEnhanced: fallbackResult.ai_enhanced,
            reindexed: fallbackResult.reindexed,
            scrapeInitiated: false,
            status: "completed_with_fallback",
          };
          OnboardingJobTracker.failJob(
            locationId,
            jobType,
            new Error(`Scrape failed: ${scrapeResponse.status}`)
          );
          return; // Exit after fallback
        } else {
          logger.info(
            `Successfully initiated scraping for retailer ${retailer_id}`
          );

          // Initialize Supabase service just to get current product count
          const supabaseService = new SupabaseService({
            url: process.env.SUPABASE_URL || "",
            key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
            bucket: process.env.SUPABASE_BUCKET || "location-data",
          });

          // Get current product count to estimate scrape time
          const response = await supabaseService.getRetailerProducts(
            retailer_id
          );
          const products = response.products || [];
          productCount = products.length;

          // Calculate estimated scrape time (5 minutes per 400 products)
          estimatedScrapeTimeMinutes = Math.max(
            5,
            Math.ceil((productCount / 400) * 5)
          );
          const delayInMs = estimatedScrapeTimeMinutes * 60 * 1000;

          // Schedule a delayed job to import products after scraping is likely complete
          const MarketplaceSyncJob = require("./MarketplaceSyncJob").default;
          await App.main.queue.enqueue(
            MarketplaceSyncJob.from({
              location_id: locationId,
              retailer_id,
              delay: delayInMs,
            })
          );

          logger.info(
            `Scheduled import job to run after ${estimatedScrapeTimeMinutes} minutes`
          );
        }

        ctx.body = {
          success: true,
          message: `Sync process initiated. Currently scraping marketplace data.`,
          scrapeInitiated: true,
          productCount, // Now in scope
          estimatedScrapeTime: estimatedScrapeTimeMinutes, // Now in scope
          status: "processing",
        };
      } catch (scrapeError) {
        logger.error(`Error during scraping or job scheduling: ${scrapeError}`);
        // Fall back to importing current data if anything in the try block fails
        const fallbackResult = await importCurrentProducts(
          locationId,
          retailer_id
        );

        ctx.body = {
          success: true,
          message: `An error occurred during scraping. Synced ${fallbackResult.processed} products from current database data.`,
          syncedProducts: fallbackResult.processed,
          aiEnhanced: fallbackResult.ai_enhanced,
          reindexed: fallbackResult.reindexed,
          scrapeInitiated: false,
          status: "completed_with_error",
        };
        OnboardingJobTracker.failJob(
          locationId,
          jobType,
          scrapeError instanceof Error
            ? scrapeError
            : new Error(String(scrapeError))
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        `Failed to sync products from marketplace: ${errorMessage}`,
        error
      );

      // Mark job as failed if it was started and hasn't been marked yet
      if (locationId) {
        // Check if locationId was assigned
        try {
          // Check if job is already marked before marking again
          const jobStatus = OnboardingJobTracker.getSummary(
            locationId
          ).jobs.find((j) => j.jobType === jobType)?.status;
          if (jobStatus !== "failed" && jobStatus !== "completed") {
            OnboardingJobTracker.failJob(
              locationId, // Use locationId directly
              jobType, // Use jobType directly
              error instanceof Error ? error : new Error(errorMessage)
            );
          }
        } catch (trackerError) {
          logger.error("Error updating job tracker:", trackerError);
        }
      }

      ctx.status = 500;
      ctx.body = {
        success: false,
        error: "Failed to sync products from marketplace",
        details: errorMessage,
      };
    }
  }
);

// Helper function to import current products (used as fallback)
async function importCurrentProducts(locationId: number, retailerId: string) {
  // Initialize Supabase service
  const supabaseService = new SupabaseService({
    url: process.env.SUPABASE_URL || "",
    key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
    bucket: process.env.SUPABASE_BUCKET || "location-data",
  });

  // Get retailer products from Supabase
  const response = await supabaseService.getRetailerProducts(retailerId);
  const products = response.products || [];

  if (products.length === 0) {
    logger.info(`No products found for retailer ${retailerId} in marketplace`);
    return { processed: 0, ai_enhanced: 0, reindexed: 0 };
  }

  // Use the importFromMarketplace function to import products
  return await importFromMarketplace(locationId, retailerId, products, {
    enhance_with_ai: false,
    reindex: true,
  });
}

// Add this endpoint to the router
router.post("/upload-image", locationRoleMiddleware("editor"), async (ctx) => {
  const locationId = ctx.state.location.id;
  logger.info(`Uploading product image for location ${locationId}`);

  try {
    // Parse multipart form data
    const formData = await parseMultipartForm(ctx);

    // Get the uploaded image file
    const productImage = formData.files.productImage;
    if (!productImage) {
      logger.error("No product image found in request");
      throw new RequestError({
        message: "No product image found in request",
        code: 400,
        statusCode: 400,
      });
    }

    // Get file metadata
    const fileName = productImage.metadata.fileName || "product-image";
    const contentType = productImage.metadata.mimeType || "image/jpeg";
    const fileSize = productImage.metadata.size || 0;

    logger.info(
      `Processing product image: ${fileName}, type: ${contentType}, size: ${fileSize}`
    );

    // Validate that it's actually an image
    if (!contentType.startsWith("image/")) {
      logger.error(`Invalid content type: ${contentType}`);
      throw new RequestError({
        message: "Uploaded file is not an image",
        code: 400,
        statusCode: 400,
      });
    }

    // Get file data as buffer from the stream
    const chunks: Buffer[] = [];

    // FileStream has a file property that is a Stream
    const buffer = await new Promise<Buffer>((resolve, reject) => {
      productImage.file.on("data", (chunk: Buffer) => chunks.push(chunk));
      productImage.file.on("end", () => resolve(Buffer.concat(chunks)));
      productImage.file.on("error", reject);
    });

    // Use the ProductImageService to store the image
    const result = await productImageService.storeProductImage(
      locationId,
      buffer,
      fileName,
      contentType,
      fileSize
    );

    logger.info(`Successfully uploaded product image: ${result.url}`);

    // Return the image URL and metadata
    ctx.body = {
      success: true,
      url: result.url,
      imageId: result.image.id,
      fileName: result.image.name,
    };
  } catch (error: unknown) {
    logger.error("Error uploading product image:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Failed to upload product image";
    throw new RequestError({
      message: errorMessage,
      code: 400,
      statusCode: 400,
    });
  }
});

// Add API endpoint to handle downloading an image from URL
router.post(
  "/download-image",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    const locationId = parseInt(ctx.params.location);
    const { imageUrl, name } = ctx.request.body;

    if (!imageUrl) {
      throw new RequestError({
        message: "Image URL is required",
        code: 400,
        statusCode: 400,
      });
    }

    try {
      logger.info(
        `Downloading product image from URL for location ${locationId}: ${imageUrl}`
      );

      // Use the ProductImageService to download and store the image
      const result = await productImageService.downloadAndStoreProductImage(
        locationId,
        imageUrl,
        name
      );

      logger.info(
        `Successfully downloaded and stored product image: ${result.url}`
      );

      // Return the image URL and metadata
      ctx.body = {
        success: true,
        url: result.url,
        imageId: result.image.id,
        fileName: result.image.name,
      };
    } catch (error: unknown) {
      logger.error("Error downloading product image from URL:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to download product image";
      throw new RequestError({
        message: errorMessage,
        code: 400,
        statusCode: 400,
      });
    }
  }
);

// Define a separate schema for the single product PATCH endpoint
// This schema also makes all fields optional
const singleProductPatchRequest: JSONSchemaType<Partial<ProductParams>> = {
  $id: "singlePartialProductParams",
  type: "object",
  properties: {
    location_id: { type: "number", nullable: true },
    meta_sku: { type: "string", nullable: true },
    source: { type: "string", nullable: true },
    retailer_id: { type: "string", nullable: true },
    raw_product_name: { type: "string", nullable: true },
    product_name: { type: "string", nullable: true },
    medical: { type: "boolean", nullable: true },
    recreational: { type: "boolean", nullable: true },
    cann_sku_id: { type: "string", nullable: true },
    brand_name: { type: "string", nullable: true },
    brand_id: { type: "number", nullable: true },
    url: { type: "string", nullable: true },
    image_url: { type: "string", nullable: true },
    raw_weight_string: { type: "string", nullable: true },
    display_weight: { type: "string", nullable: true },
    raw_product_category: { type: "string", nullable: true },
    category: { type: "string", nullable: true },
    raw_subcategory: { type: "string", nullable: true },
    subcategory: { type: "string", nullable: true },
    product_tags: {
      type: "array",
      items: { type: "string" },
      nullable: true,
    },
    percentage_thc: { type: "number", nullable: true },
    percentage_cbd: { type: "number", nullable: true },
    mg_thc: { type: "number", nullable: true },
    mg_cbd: { type: "number", nullable: true },
    quantity_per_package: { type: "number", nullable: true },
    latest_price: { type: "number", nullable: true },
    menu_provider: { type: "string", nullable: true },
    data: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
    images_urls: { type: "string", nullable: true },
    review_summary: { type: "string", nullable: true },
    rating: { type: "number", nullable: true },
    reviews_count: { type: "number", nullable: true },
    product_description: { type: "string", nullable: true },
    short_description: { type: "string", nullable: true },
    thc: { type: "number", nullable: true },
    cbd: { type: "number", nullable: true },
    variants: {
      type: "array",
      nullable: true,
      items: { type: "string" },
    },
    enhancement_status: { type: "string", nullable: true },
    ai_enhanced_fields: {
      type: "array",
      nullable: true,
      items: { type: "string" },
    },
    mood: {
      type: "array",
      nullable: true,
      items: { type: "string" },
    },
    estimated_cbd_percentage: { type: "string", nullable: true },
    estimated_thc_percentage: { type: "string", nullable: true },
    effects: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
    enhancement_error: { type: "string", nullable: true },
    external_id: { type: "string", nullable: true },
    wholesale_price: { type: "number", nullable: true },
    retail_price: { type: "number", nullable: true },
    msrp: { type: "number", nullable: true },
    profit_margin: { type: "number", nullable: true },
    grower_name: { type: "string", nullable: true },
    cultivar: { type: "string", nullable: true },
    batch_number: { type: "string", nullable: true },
    harvest_date: { type: "string", nullable: true },
    coa_url: { type: "string", nullable: true },
    slug: { type: "string", nullable: true },
    out_of_stock: { type: "boolean", nullable: true },
    inventory_quantity: { type: "number", nullable: true },
  },
  additionalProperties: false,
} as any;

// Add a new endpoint after existing endpoints
router.post("/gap-analysis", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const { startDate, endDate, usePosData = false } = ctx.request.body;
    const locationId = ctx.state.location.id;

    if (!startDate || !endDate) {
      ctx.status = 400;
      ctx.body = {
        error: "startDate and endDate are required",
      };
      return;
    }

    // Create and use the gap analysis tool
    const gapAnalysisTool = new ProductGapAnalysisTool();
    const result = await gapAnalysisTool._call(
      JSON.stringify({
        locationId,
        startDate,
        endDate,
        usePosData, // Pass flag to control whether POS data should be used
      })
    );

    ctx.body = JSON.parse(result);
  } catch (error) {
    logger.error("Error in product gap analysis:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to analyze product gaps",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

// Add AI Enhancement Monitoring Endpoints

/**
 * @swagger
 * /products/metrics/enhancement/{locationId}:
 *   get:
 *     summary: Get AI Enhancement Metrics
 *     description: Retrieves AI enhancement performance metrics for a specific location
 *     tags: [Product, Monitoring]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID to get metrics for
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: integer
 *           default: 24
 *         description: Time range in hours for metrics collection
 *     responses:
 *       200:
 *         description: AI enhancement metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalJobs:
 *                   type: integer
 *                 totalProducts:
 *                   type: integer
 *                 totalCost:
 *                   type: number
 *                 averageErrorRate:
 *                   type: number
 *                 averageProcessingTime:
 *                   type: number
 *                 costTrends:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       hour:
 *                         type: integer
 *                       cost:
 *                         type: number
 */
router.get(
  "/metrics/enhancement/:locationId",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const locationId = parseInt(ctx.params.locationId);
      const timeRange = parseInt(ctx.query.timeRange as string) || 24;

      const metrics = AIEnhancementMetrics.getLocationMetrics(
        locationId,
        timeRange
      );

      ctx.body = {
        success: true,
        locationId,
        timeRange,
        metrics,
      };
    } catch (error) {
      logger.error("Error getting AI enhancement metrics:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to retrieve AI enhancement metrics",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /products/metrics/system:
 *   get:
 *     summary: Get System-wide Enhancement Metrics
 *     description: Retrieves system-wide AI enhancement performance metrics
 *     tags: [Product, Monitoring]
 *     responses:
 *       200:
 *         description: System metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 activeJobs:
 *                   type: integer
 *                 totalJobsLast24h:
 *                   type: integer
 *                 totalCostLast24h:
 *                   type: number
 *                 averageErrorRate:
 *                   type: number
 *                 topCostLocations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       locationId:
 *                         type: integer
 *                       cost:
 *                         type: number
 */
router.get("/metrics/system", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const systemMetrics = AIEnhancementMetrics.getSystemMetrics();

    ctx.body = {
      success: true,
      metrics: systemMetrics,
    };
  } catch (error) {
    logger.error("Error getting system enhancement metrics:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to retrieve system enhancement metrics",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /products/metrics/export:
 *   get:
 *     summary: Export Enhancement Metrics
 *     description: Exports AI enhancement metrics in various formats for external monitoring
 *     tags: [Product, Monitoring]
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, prometheus]
 *           default: json
 *         description: Export format
 *     responses:
 *       200:
 *         description: Metrics exported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *           text/plain:
 *             schema:
 *               type: string
 */
router.get("/metrics/export", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const format = (ctx.query.format as string) || "json";

    if (!["json", "prometheus"].includes(format)) {
      ctx.status = 400;
      ctx.body = {
        error: "Invalid format. Supported formats: json, prometheus",
      };
      return;
    }

    const metrics = AIEnhancementMetrics.exportMetrics(
      format as "json" | "prometheus"
    );

    if (format === "prometheus") {
      ctx.type = "text/plain";
      ctx.body = metrics;
    } else {
      ctx.type = "application/json";
      ctx.body = JSON.parse(metrics);
    }
  } catch (error) {
    logger.error("Error exporting enhancement metrics:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to export enhancement metrics",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /products/queue/health:
 *   get:
 *     summary: Get Queue Health Status
 *     description: Retrieves queue health status including circuit breaker state
 *     tags: [Product, Monitoring]
 *     responses:
 *       200:
 *         description: Queue health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 healthy:
 *                   type: boolean
 *                 circuitState:
 *                   type: string
 *                 metrics:
 *                   type: object
 */
router.get("/queue/health", locationRoleMiddleware("support"), async (ctx) => {
  try {
    // Access the queue provider to get health status
    const queueProvider = App.main.queue.provider;

    if (queueProvider && "getHealthStatus" in queueProvider) {
      const healthStatus = (queueProvider as any).getHealthStatus();

      ctx.body = {
        success: true,
        healthy: healthStatus.circuitState === "CLOSED",
        ...healthStatus,
      };
    } else {
      ctx.body = {
        success: true,
        healthy: true,
        message: "Health monitoring not available for this queue provider",
      };
    }
  } catch (error) {
    logger.error("Error getting queue health status:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to retrieve queue health status",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /products/enhancement-status:
 *   get:
 *     summary: Get AI Enhancement Status
 *     description: Retrieves the current AI enhancement job status for the location
 *     tags: [Product, Monitoring]
 *     responses:
 *       200:
 *         description: AI enhancement status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isProcessing:
 *                   type: boolean
 *                 total:
 *                   type: integer
 *                 completed:
 *                   type: integer
 *                 failed:
 *                   type: integer
 *                 processing:
 *                   type: integer
 *                 jobs:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       jobId:
 *                         type: string
 *                       jobType:
 *                         type: string
 *                       status:
 *                         type: string
 *                       startTime:
 *                         type: string
 *                         format: date-time
 *                       endTime:
 *                         type: string
 *                         format: date-time
 *                       totalProducts:
 *                         type: integer
 *                       processedProducts:
 *                         type: integer
 *                       failedProducts:
 *                         type: integer
 *                       totalCost:
 *                         type: number
 *                       errorRate:
 *                         type: number
 */
router.get(
  "/enhancement-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;

      // Get AI enhancement job summary for this location
      const aiEnhancementSummary = AIEnhancementMetrics.getLocationJobSummary(
        locationId,
        24 // Last 24 hours
      );

      // Also check OnboardingJobTracker for AI enhancement jobs
      const onboardingStatus = OnboardingJobTracker.getSummary(locationId);
      const aiEnhancementJobs = onboardingStatus.jobs.filter((job) =>
        job.jobType.startsWith("ai_enhancement_")
      );

      // Create a unified response that includes both sources
      const enhancementStatus = {
        isProcessing:
          aiEnhancementSummary.isProcessing ||
          aiEnhancementJobs.some((job) => job.status === "processing"),
        metrics: {
          total: aiEnhancementSummary.total,
          completed: aiEnhancementSummary.completed,
          failed: aiEnhancementSummary.failed,
          processing: aiEnhancementSummary.processing,
        },
        onboarding_jobs: {
          total: aiEnhancementJobs.length,
          processing: aiEnhancementJobs.filter(
            (job) => job.status === "processing"
          ).length,
          completed: aiEnhancementJobs.filter(
            (job) => job.status === "completed"
          ).length,
          failed: aiEnhancementJobs.filter((job) => job.status === "failed")
            .length,
          jobs: aiEnhancementJobs,
        },
        detailed_jobs: aiEnhancementSummary.jobs,
        last_updated: new Date().toISOString(),
      };

      ctx.body = {
        success: true,
        locationId,
        ...enhancementStatus,
      };
    } catch (error) {
      logger.error("Error getting AI enhancement status:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to retrieve AI enhancement status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /products/optimization/estimate:
 *   post:
 *     summary: Estimate Enhancement Costs
 *     description: Provides cost and time estimates for AI enhancement jobs based on product count
 *     tags: [Product, Monitoring]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               limit:
 *                 type: integer
 *                 description: Number of products to enhance
 *               skip:
 *                 type: integer
 *                 description: Number of products to skip (default 0)
 *               meta_sku:
 *                 type: string
 *                 description: Specific product SKU to enhance
 *               force_mode:
 *                 type: string
 *                 enum: [fast, batch, auto]
 *                 description: Processing mode override
 *     responses:
 *       200:
 *         description: Cost estimate generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 estimatedCost:
 *                   type: number
 *                 estimatedTokens:
 *                   type: integer
 *                 processingStrategy:
 *                   type: object
 */
router.post(
  "/optimization/estimate",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const {
        limit = 100,
        skip = 0,
        meta_sku,
        force_mode = "auto",
      } = ctx.request.body;
      const locationId = ctx.state.location.id;

      // Validate limit
      const enhanceLimit = parseInt(limit as string) || 100;
      const enhanceSkip = parseInt(skip as string) || 0;

      if (enhanceLimit <= 0) {
        ctx.status = 400;
        ctx.body = {
          error: "limit must be greater than 0",
        };
        return;
      }

      // Use the same product selection logic as the enhancement endpoint
      // This ensures cost estimation matches actual enhancement behavior
      const queryBuilder = (qb: any) => {
        qb.where("location_id", locationId);

        // Filter by meta_sku if provided (for single product estimates)
        if (meta_sku) {
          qb.where("meta_sku", meta_sku);
        } else {
          // Priority order for enhancement:
          // 1. Products that have never been enhanced (enhancement_status is null or 'not_started')
          // 2. Products that failed enhancement (enhancement_status = 'failed')
          // 3. All other products
          qb.orderByRaw(`
            CASE 
              WHEN enhancement_status IS NULL OR enhancement_status = 'not_started' THEN 1
              WHEN enhancement_status = 'failed' THEN 2
              ELSE 3
            END,
            created_at DESC
          `);
        }

        qb.limit(enhanceLimit).offset(enhanceSkip);
        return qb;
      };

      // Get products using the same selection logic as enhancement
      const productObjects = await Product.all(queryBuilder);

      if (productObjects.length === 0) {
        ctx.body = {
          success: true,
          originalCount: 0,
          deduplicatedCount: 0,
          estimatedCost: 0,
          estimatedTokens: 0,
          processingStrategy: {
            expressBatch: 0,
            standardBatch: 0,
            deepEnhancementBatch: 0,
          },
          costPerProduct: 0,
          recommendedMode: force_mode,
          message: "No products found for enhancement estimation",
        };
        return;
      }

      logger.info(
        `Estimating costs for ${productObjects.length} products in location ${locationId}`
      );

      // Convert to ProductParams for optimization
      const productParams = productObjects.map((product: Product) => {
        return (typeof product.toJSON === "function"
          ? product.toJSON()
          : { ...product }) as unknown as ProductParams;
      });

      // Import the cost optimizer
      const {
        AIEnhancementCostOptimizer,
      } = require("./AIEnhancementCostOptimizer");

      // Apply the same deduplication and optimization logic as actual enhancement
      const deduplicatedProducts =
        await AIEnhancementCostOptimizer.deduplicateProducts(productParams);

      const optimizationResult =
        AIEnhancementCostOptimizer.optimizeBatchProcessing(
          deduplicatedProducts
        );

      // Override with forced mode if specified
      if (force_mode && force_mode !== "auto") {
        logger.info(`Forcing processing mode: ${force_mode}`);
        switch (force_mode) {
          case "fast":
            optimizationResult.expressBatch = deduplicatedProducts;
            optimizationResult.standardBatch = [];
            optimizationResult.deepEnhancementBatch = [];
            break;
          case "batch":
            optimizationResult.expressBatch = [];
            optimizationResult.standardBatch = deduplicatedProducts;
            optimizationResult.deepEnhancementBatch = [];
            break;
        }
      }

      ctx.body = {
        success: true,
        originalCount: productObjects.length,
        deduplicatedCount: deduplicatedProducts.length,
        estimatedCost: optimizationResult.totalEstimatedCost,
        estimatedTokens: optimizationResult.totalEstimatedTokens,
        processingStrategy: {
          expressBatch: optimizationResult.expressBatch.length,
          standardBatch: optimizationResult.standardBatch.length,
          deepEnhancementBatch: optimizationResult.deepEnhancementBatch.length,
        },
        costPerProduct:
          deduplicatedProducts.length > 0
            ? optimizationResult.totalEstimatedCost /
              deduplicatedProducts.length
            : 0,
        recommendedMode: force_mode,
      };
    } catch (error) {
      logger.error("Error estimating enhancement costs:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to estimate enhancement costs",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Featured Products Endpoints

/**
 * @swagger
 * /products/featured:
 *   get:
 *     summary: Get Featured Products
 *     description: Retrieves featured products for a location, optionally filtered by top picks
 *     tags: [Product, Featured]
 *     parameters:
 *       - in: query
 *         name: top_picks_only
 *         schema:
 *           type: boolean
 *         description: Filter to only show top picks
 *       - in: query
 *         name: active_only
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Filter to only show active featured products
 *     responses:
 *       200:
 *         description: Featured products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       product_id:
 *                         type: integer
 *                       is_top_pick:
 *                         type: boolean
 *                       sort_order:
 *                         type: integer
 *                       active:
 *                         type: boolean
 *                       product:
 *                         $ref: '#/components/schemas/Product'
 */
router.get("/featured", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const topPicksOnly = ctx.query.top_picks_only === "true";
    const activeOnly = ctx.query.active_only !== "false"; // Default to true

    logger.info({
      message: "Getting featured products",
      locationId,
      topPicksOnly,
      activeOnly,
    });

    let query = App.main
      .db("featured_products")
      .where("location_id", locationId)
      .leftJoin("products", "featured_products.product_id", "products.id")
      .select(
        "featured_products.*",
        "products.*",
        App.main.db.raw("featured_products.id as featured_id")
      )
      .orderBy("featured_products.sort_order", "asc");

    if (activeOnly) {
      query = query.where("featured_products.active", true);
    }

    if (topPicksOnly) {
      query = query.where("featured_products.is_top_pick", true);
    }

    const featuredProducts = await query;

    // Transform the results to include product data
    const results = featuredProducts.map((row: any) => ({
      id: row.featured_id,
      location_id: row.location_id,
      product_id: row.product_id,
      is_top_pick: row.is_top_pick,
      sort_order: row.sort_order,
      active: row.active,
      created_at: row.created_at,
      updated_at: row.updated_at,
      product: {
        id: row.product_id,
        meta_sku: row.meta_sku,
        product_name: row.product_name,
        brand_name: row.brand_name,
        category: row.category,
        subcategory: row.subcategory,
        image_url: row.image_url,
        latest_price: row.latest_price,
        product_description: row.product_description,
        percentage_thc: row.percentage_thc,
        percentage_cbd: row.percentage_cbd,
        rating: row.rating,
        reviews_count: row.reviews_count,
        medical: row.medical,
        recreational: row.recreational,
      },
    }));

    logger.info({
      message: "Retrieved featured products",
      count: results.length,
      locationId,
    });

    ctx.body = {
      results,
      total: results.length,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching featured products",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to fetch featured products" };
  }
});

/**
 * @swagger
 * /products/featured:
 *   post:
 *     summary: Add Featured Product
 *     description: Adds a product to the featured products list
 *     tags: [Product, Featured]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - product_id
 *             properties:
 *               product_id:
 *                 type: integer
 *                 description: ID of the product to feature
 *               is_top_pick:
 *                 type: boolean
 *                 default: false
 *                 description: Whether this is a top pick
 *               sort_order:
 *                 type: integer
 *                 description: Sort order (auto-assigned if not provided)
 *               active:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the featured product is active
 *     responses:
 *       201:
 *         description: Featured product added successfully
 *       400:
 *         description: Invalid input or product already featured
 *       404:
 *         description: Product not found
 */
router.post("/featured", locationRoleMiddleware("editor"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;
    const {
      product_id,
      is_top_pick = false,
      sort_order,
      active = true,
    } = ctx.request.body;

    if (!product_id) {
      ctx.status = 400;
      ctx.body = { error: "product_id is required" };
      return;
    }

    // Check if product exists and belongs to this location
    const product = await Product.first((qb) =>
      qb.where("id", product_id).where("location_id", locationId)
    );

    if (!product) {
      ctx.status = 404;
      ctx.body = { error: "Product not found" };
      return;
    }

    // Check if product is already featured - if so, just update it instead of erroring
    const existingFeatured = await App.main
      .db("featured_products")
      .where("location_id", locationId)
      .where("product_id", product_id)
      .first();

    if (existingFeatured) {
      // Product already featured, just update its properties
      const updateData: any = { updated_at: new Date() };
      if (is_top_pick !== undefined) updateData.is_top_pick = is_top_pick;
      if (sort_order !== undefined) updateData.sort_order = sort_order;
      if (active !== undefined) updateData.active = active;

      await App.main
        .db("featured_products")
        .where("id", existingFeatured.id)
        .update(updateData);

      const updatedFeatured = await App.main
        .db("featured_products")
        .where("id", existingFeatured.id)
        .first();

      logger.info({
        message: "Featured product updated (was already featured)",
        featuredProductId: existingFeatured.id,
        productId: product_id,
        locationId,
        isTopPick: is_top_pick,
      });

      ctx.status = 200;
      ctx.body = updatedFeatured;
      return;
    }

    // Determine sort order if not provided
    let finalSortOrder = sort_order;
    if (finalSortOrder === undefined) {
      const maxOrder = await App.main
        .db("featured_products")
        .where("location_id", locationId)
        .max("sort_order as max_order")
        .first();
      finalSortOrder = (maxOrder?.max_order || 0) + 1;
    }

    // Create featured product
    const featuredProduct = await App.main
      .db("featured_products")
      .insert({
        location_id: locationId,
        product_id,
        is_top_pick,
        sort_order: finalSortOrder,
        active,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .returning("*");

    logger.info({
      message: "Featured product added",
      featuredProductId: featuredProduct[0]?.id,
      productId: product_id,
      locationId,
      isTopPick: is_top_pick,
    });

    ctx.status = 201;
    ctx.body = featuredProduct[0];
  } catch (error) {
    logger.error({
      message: "Error adding featured product",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to add featured product" };
  }
});

/**
 * @swagger
 * /products/featured/{featuredId}:
 *   patch:
 *     summary: Update Featured Product
 *     description: Updates a featured product's properties
 *     tags: [Product, Featured]
 *     parameters:
 *       - in: path
 *         name: featuredId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Featured product ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               is_top_pick:
 *                 type: boolean
 *                 description: Whether this is a top pick
 *               sort_order:
 *                 type: integer
 *                 description: Sort order
 *               active:
 *                 type: boolean
 *                 description: Whether the featured product is active
 *     responses:
 *       200:
 *         description: Featured product updated successfully
 *       404:
 *         description: Featured product not found
 */
router.patch(
  "/featured/:featuredId",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const featuredId = parseInt(ctx.params.featuredId);
      const { is_top_pick, sort_order, active } = ctx.request.body;

      // Check if featured product exists
      const featuredProduct = await App.main
        .db("featured_products")
        .where("id", featuredId)
        .where("location_id", locationId)
        .first();

      if (!featuredProduct) {
        ctx.status = 404;
        ctx.body = { error: "Featured product not found" };
        return;
      }

      // Build update object
      const updateData: any = { updated_at: new Date() };
      if (is_top_pick !== undefined) updateData.is_top_pick = is_top_pick;
      if (sort_order !== undefined) updateData.sort_order = sort_order;
      if (active !== undefined) updateData.active = active;

      // Update featured product
      await App.main
        .db("featured_products")
        .where("id", featuredId)
        .update(updateData);

      // Fetch updated record
      const updatedFeatured = await App.main
        .db("featured_products")
        .where("id", featuredId)
        .first();

      logger.info({
        message: "Featured product updated",
        featuredProductId: featuredId,
        locationId,
        updateData,
      });

      ctx.body = updatedFeatured;
    } catch (error) {
      logger.error({
        message: "Error updating featured product",
        error: error instanceof Error ? error.message : "Unknown error",
        featuredId: ctx.params.featuredId,
        locationId: ctx.state.location?.id,
      });

      ctx.status = 500;
      ctx.body = { error: "Failed to update featured product" };
    }
  }
);

/**
 * @swagger
 * /products/featured/bulk-update:
 *   patch:
 *     summary: Bulk Update Featured Products
 *     description: Updates multiple featured products at once (useful for reordering)
 *     tags: [Product, Featured]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - updates
 *             properties:
 *               updates:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: Featured product ID
 *                     is_top_pick:
 *                       type: boolean
 *                     sort_order:
 *                       type: integer
 *                     active:
 *                       type: boolean
 *     responses:
 *       200:
 *         description: Featured products updated successfully
 *       400:
 *         description: Invalid input
 */
router.patch(
  "/featured/bulk-update",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const { updates } = ctx.request.body;

      if (!Array.isArray(updates) || updates.length === 0) {
        ctx.status = 400;
        ctx.body = { error: "updates array is required and cannot be empty" };
        return;
      }

      // Validate all featured products belong to this location
      const featuredIds = updates.map((u) => u.id);
      const existingFeatured = await App.main
        .db("featured_products")
        .whereIn("id", featuredIds)
        .where("location_id", locationId);

      if (existingFeatured.length !== featuredIds.length) {
        ctx.status = 400;
        ctx.body = {
          error:
            "One or more featured products not found or don't belong to this location",
        };
        return;
      }

      // Perform bulk updates
      const updatePromises = updates.map((update) => {
        const { id, ...updateData } = update;
        const finalUpdateData = { ...updateData, updated_at: new Date() };

        return App.main
          .db("featured_products")
          .where("id", id)
          .update(finalUpdateData);
      });

      await Promise.all(updatePromises);

      logger.info({
        message: "Bulk updated featured products",
        updatedCount: updates.length,
        locationId,
      });

      ctx.body = {
        message: "Featured products updated successfully",
        updated_count: updates.length,
      };
    } catch (error) {
      logger.error({
        message: "Error bulk updating featured products",
        error: error instanceof Error ? error.message : "Unknown error",
        locationId: ctx.state.location?.id,
      });

      ctx.status = 500;
      ctx.body = { error: "Failed to update featured products" };
    }
  }
);

/**
 * @swagger
 * /products/featured/bulk-remove:
 *   delete:
 *     summary: Bulk Remove Featured Products
 *     description: Removes multiple products from featured by product IDs
 *     tags: [Product, Featured]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - product_ids
 *             properties:
 *               product_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of product IDs to remove from featured
 *     responses:
 *       200:
 *         description: Featured products removed successfully
 *       400:
 *         description: Invalid input
 */
router.delete(
  "/featured/bulk-remove",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const { product_ids } = ctx.request.body;

      if (!Array.isArray(product_ids) || product_ids.length === 0) {
        ctx.status = 400;
        ctx.body = {
          error: "product_ids array is required and cannot be empty",
        };
        return;
      }

      // Just blindly try to delete featured products by product_ids
      // This handles the case where we don't have featured_id in frontend
      const deletedCount = await App.main
        .db("featured_products")
        .whereIn("product_id", product_ids)
        .where("location_id", locationId)
        .delete();

      logger.info({
        message: "Bulk removed featured products",
        requestedCount: product_ids.length,
        actuallyDeleted: deletedCount,
        locationId,
      });

      ctx.body = {
        message: "Featured products removed successfully",
        requested_count: product_ids.length,
        removed_count: deletedCount,
      };
    } catch (error) {
      logger.error({
        message: "Error bulk removing featured products",
        error: error instanceof Error ? error.message : "Unknown error",
        locationId: ctx.state.location?.id,
      });

      ctx.status = 500;
      ctx.body = { error: "Failed to remove featured products" };
    }
  }
);

/**
 * @swagger
 * /products/featured/{featuredId}:
 *   delete:
 *     summary: Remove Featured Product
 *     description: Removes a product from the featured products list
 *     tags: [Product, Featured]
 *     parameters:
 *       - in: path
 *         name: featuredId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Featured product ID
 *     responses:
 *       204:
 *         description: Featured product removed successfully
 *       404:
 *         description: Featured product not found
 */
router.delete(
  "/featured/:featuredId",
  locationRoleMiddleware("editor"),
  async (ctx) => {
    try {
      const locationId = ctx.state.location.id;
      const featuredId = parseInt(ctx.params.featuredId);

      // Check if featured product exists
      const featuredProduct = await App.main
        .db("featured_products")
        .where("id", featuredId)
        .where("location_id", locationId)
        .first();

      if (!featuredProduct) {
        ctx.status = 404;
        ctx.body = { error: "Featured product not found" };
        return;
      }

      // Delete featured product
      await App.main.db("featured_products").where("id", featuredId).delete();

      logger.info({
        message: "Featured product removed",
        featuredProductId: featuredId,
        locationId,
      });

      ctx.status = 204;
      ctx.body = "";
    } catch (error) {
      logger.error({
        message: "Error removing featured product",
        error: error instanceof Error ? error.message : "Unknown error",
        featuredId: ctx.params.featuredId,
        locationId: ctx.state.location?.id,
      });

      ctx.status = 500;
      ctx.body = { error: "Failed to remove featured product" };
    }
  }
);

export default router;

/**
 * Helper method to parse mood field from form data
 * Handles both string and JSON array formats
 */
function parseMoodField(moodValue: string): string[] {
  try {
    // Try to parse as JSON first (for array format)
    const parsed = JSON.parse(moodValue);
    if (Array.isArray(parsed)) {
      return parsed;
    }
    // If it's a valid JSON but not an array, treat as single item
    return [String(parsed)];
  } catch (e) {
    // If JSON parsing fails, treat as comma-separated string
    if (typeof moodValue === "string" && moodValue.trim()) {
      return moodValue
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item.length > 0);
    }
    return [];
  }
}
