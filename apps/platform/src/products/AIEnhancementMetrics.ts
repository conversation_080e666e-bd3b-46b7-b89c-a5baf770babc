/* eslint-disable indent */
import { logger } from "../config/logger";

interface EnhancementMetrics {
  jobId: string;
  locationId: number;
  startTime: number;
  endTime?: number;
  totalProducts: number;
  processedProducts: number;
  failedProducts: number;
  skippedProducts: number;
  totalCost: number;
  totalTokens: number;
  processingMethod: "fast" | "batch" | "mixed";
  averageProcessingTime: number;
  errorRate: number;
  memoryUsage?: number;
  costPerProduct: number;
}

interface BatchMetrics {
  batchId: string;
  batchSize: number;
  complexity: "simple" | "medium" | "complex";
  startTime: number;
  endTime?: number;
  success: boolean;
  tokensUsed: number;
  cost: number;
  error?: string;
}

interface PerformanceAlert {
  type: "cost_threshold" | "error_rate" | "processing_time" | "memory_usage";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  jobId: string;
  timestamp: number;
  metrics: any;
}

export class AIEnhancementMetrics {
  private static metricsStore = new Map<string, EnhancementMetrics>();
  private static batchMetricsStore = new Map<string, BatchMetrics>();
  private static alertCallbacks: ((alert: PerformanceAlert) => void)[] = [];

  // Configurable thresholds
  private static readonly COST_THRESHOLD_PER_PRODUCT = 0.05; // $0.05 per product
  private static readonly ERROR_RATE_THRESHOLD = 0.15; // 15% error rate
  private static readonly PROCESSING_TIME_THRESHOLD = 300000; // 5 minutes per product
  private static readonly MEMORY_USAGE_THRESHOLD = 1024; // 1GB

  /**
   * Start tracking metrics for a new enhancement job
   */
  static startJob(
    jobId: string,
    locationId: number,
    totalProducts: number,
    processingMethod: "fast" | "batch" | "mixed"
  ): void {
    const metrics: EnhancementMetrics = {
      jobId,
      locationId,
      startTime: Date.now(),
      totalProducts,
      processedProducts: 0,
      failedProducts: 0,
      skippedProducts: 0,
      totalCost: 0,
      totalTokens: 0,
      processingMethod,
      averageProcessingTime: 0,
      errorRate: 0,
      costPerProduct: 0,
    };

    this.metricsStore.set(jobId, metrics);

    logger.info("Started tracking enhancement job metrics", {
      jobId,
      locationId,
      totalProducts,
      processingMethod,
    });
  }

  /**
   * Update job progress and metrics
   */
  static updateJobProgress(
    jobId: string,
    processed: number,
    failed: number,
    skipped: number,
    additionalCost: number = 0,
    additionalTokens: number = 0
  ): void {
    const metrics = this.metricsStore.get(jobId);
    if (!metrics) return;

    metrics.processedProducts = processed;
    metrics.failedProducts = failed;
    metrics.skippedProducts = skipped;
    metrics.totalCost += additionalCost;
    metrics.totalTokens += additionalTokens;

    // Calculate derived metrics
    const totalProcessed = processed + failed + skipped;
    if (totalProcessed > 0) {
      metrics.errorRate = failed / totalProcessed;
      metrics.costPerProduct = metrics.totalCost / totalProcessed;
    }

    // Check for alerts
    this.checkForAlerts(jobId, metrics);

    logger.info("Updated job progress", {
      jobId,
      processed,
      failed,
      skipped,
      totalCost: metrics.totalCost,
      errorRate: metrics.errorRate,
    });
  }

  /**
   * Complete a job and calculate final metrics
   */
  static completeJob(jobId: string): EnhancementMetrics | null {
    const metrics = this.metricsStore.get(jobId);
    if (!metrics) return null;

    metrics.endTime = Date.now();
    const totalDuration = metrics.endTime - metrics.startTime;
    const totalProcessed =
      metrics.processedProducts +
      metrics.failedProducts +
      metrics.skippedProducts;

    if (totalProcessed > 0) {
      metrics.averageProcessingTime = totalDuration / totalProcessed;
    }

    // Log final metrics
    logger.info("Job completion metrics", {
      jobId: metrics.jobId,
      locationId: metrics.locationId,
      totalDuration: `${(totalDuration / 1000).toFixed(2)}s`,
      totalProducts: metrics.totalProducts,
      processedProducts: metrics.processedProducts,
      failedProducts: metrics.failedProducts,
      skippedProducts: metrics.skippedProducts,
      totalCost: `$${metrics.totalCost.toFixed(4)}`,
      totalTokens: metrics.totalTokens,
      averageProcessingTime: `${(metrics.averageProcessingTime / 1000).toFixed(
        2
      )}s`,
      errorRate: `${(metrics.errorRate * 100).toFixed(2)}%`,
      costPerProduct: `$${metrics.costPerProduct.toFixed(4)}`,
      processingMethod: metrics.processingMethod,
    });

    return metrics;
  }

  /**
   * Track metrics for a specific batch
   */
  static startBatch(
    batchId: string,
    batchSize: number,
    complexity: "simple" | "medium" | "complex"
  ): void {
    const batchMetrics: BatchMetrics = {
      batchId,
      batchSize,
      complexity,
      startTime: Date.now(),
      success: false,
      tokensUsed: 0,
      cost: 0,
    };

    this.batchMetricsStore.set(batchId, batchMetrics);
  }

  /**
   * Complete batch tracking
   */
  static completeBatch(
    batchId: string,
    success: boolean,
    tokensUsed: number,
    cost: number,
    error?: string
  ): void {
    const batchMetrics = this.batchMetricsStore.get(batchId);
    if (!batchMetrics) return;

    batchMetrics.endTime = Date.now();
    batchMetrics.success = success;
    batchMetrics.tokensUsed = tokensUsed;
    batchMetrics.cost = cost;
    batchMetrics.error = error;

    const duration = batchMetrics.endTime - batchMetrics.startTime;

    logger.info("Batch completion metrics", {
      batchId,
      batchSize: batchMetrics.batchSize,
      complexity: batchMetrics.complexity,
      duration: `${(duration / 1000).toFixed(2)}s`,
      success,
      tokensUsed,
      cost: `$${cost.toFixed(4)}`,
      tokensPerSecond:
        duration > 0 ? Math.round(tokensUsed / (duration / 1000)) : 0,
      error,
    });
  }

  /**
   * Get real-time metrics for a job
   */
  static getJobMetrics(jobId: string): EnhancementMetrics | null {
    return this.metricsStore.get(jobId) || null;
  }

  /**
   * Get aggregated metrics for a location
   */
  static getLocationMetrics(
    locationId: number,
    timeRangeHours: number = 24
  ): {
    totalJobs: number;
    totalProducts: number;
    totalCost: number;
    averageErrorRate: number;
    averageProcessingTime: number;
    costTrends: Array<{ hour: number; cost: number }>;
  } {
    const since = Date.now() - timeRangeHours * 60 * 60 * 1000;
    const locationJobs = Array.from(this.metricsStore.values()).filter(
      (m) => m.locationId === locationId && m.startTime >= since
    );

    const totalJobs = locationJobs.length;
    const totalProducts = locationJobs.reduce(
      (sum, m) => sum + m.totalProducts,
      0
    );
    const totalCost = locationJobs.reduce((sum, m) => sum + m.totalCost, 0);
    const averageErrorRate =
      totalJobs > 0
        ? locationJobs.reduce((sum, m) => sum + m.errorRate, 0) / totalJobs
        : 0;
    const averageProcessingTime =
      totalJobs > 0
        ? locationJobs.reduce((sum, m) => sum + m.averageProcessingTime, 0) /
          totalJobs
        : 0;

    // Generate cost trends (hourly)
    const costTrends: Array<{ hour: number; cost: number }> = [];
    for (let i = 0; i < timeRangeHours; i++) {
      const hourStart = since + i * 60 * 60 * 1000;
      const hourEnd = hourStart + 60 * 60 * 1000;
      const hourCost = locationJobs
        .filter((m) => m.startTime >= hourStart && m.startTime < hourEnd)
        .reduce((sum, m) => sum + m.totalCost, 0);

      costTrends.push({ hour: i, cost: hourCost });
    }

    return {
      totalJobs,
      totalProducts,
      totalCost,
      averageErrorRate,
      averageProcessingTime,
      costTrends,
    };
  }

  /**
   * Get location-specific job summary similar to OnboardingJobTracker format
   */
  static getLocationJobSummary(
    locationId: number,
    timeRangeHours: number = 24
  ): {
    isProcessing: boolean;
    total: number;
    completed: number;
    failed: number;
    pending: number;
    processing: number;
    jobs: Array<{
      jobId: string;
      jobType: string;
      status: "pending" | "processing" | "completed" | "failed";
      startTime: Date;
      endTime?: Date;
      totalProducts: number;
      processedProducts: number;
      failedProducts: number;
      totalCost: number;
      errorRate: number;
      processingMethod: string;
    }>;
  } {
    const since = Date.now() - timeRangeHours * 60 * 60 * 1000;
    const locationJobs = Array.from(this.metricsStore.values()).filter(
      (m) => m.locationId === locationId && m.startTime >= since
    );

    const jobs = locationJobs.map((m) => ({
      jobId: m.jobId,
      jobType: `ai_enhancement_${m.processingMethod}`,
      status: !m.endTime
        ? ("processing" as const)
        : m.failedProducts === m.totalProducts
        ? ("failed" as const)
        : ("completed" as const),
      startTime: new Date(m.startTime),
      endTime: m.endTime ? new Date(m.endTime) : undefined,
      totalProducts: m.totalProducts,
      processedProducts: m.processedProducts,
      failedProducts: m.failedProducts,
      totalCost: m.totalCost,
      errorRate: m.errorRate,
      processingMethod: m.processingMethod,
    }));

    const processing = jobs.filter((j) => j.status === "processing").length;
    const completed = jobs.filter((j) => j.status === "completed").length;
    const failed = jobs.filter((j) => j.status === "failed").length;
    const pending = 0; // AI enhancement jobs don't have a pending state

    return {
      isProcessing: processing > 0,
      total: jobs.length,
      completed,
      failed,
      pending,
      processing,
      jobs,
    };
  }

  /**
   * Add an alert callback function
   */
  static onAlert(callback: (alert: PerformanceAlert) => void): void {
    this.alertCallbacks.push(callback);
  }

  /**
   * Check for performance alerts
   */
  private static checkForAlerts(
    jobId: string,
    metrics: EnhancementMetrics
  ): void {
    const alerts: PerformanceAlert[] = [];

    // Cost threshold alert
    if (metrics.costPerProduct > this.COST_THRESHOLD_PER_PRODUCT) {
      alerts.push({
        type: "cost_threshold",
        severity:
          metrics.costPerProduct > this.COST_THRESHOLD_PER_PRODUCT * 2
            ? "high"
            : "medium",
        message: `Cost per product ($${metrics.costPerProduct.toFixed(
          4
        )}) exceeds threshold ($${this.COST_THRESHOLD_PER_PRODUCT})`,
        jobId,
        timestamp: Date.now(),
        metrics: {
          costPerProduct: metrics.costPerProduct,
          threshold: this.COST_THRESHOLD_PER_PRODUCT,
        },
      });
    }

    // Error rate alert
    if (metrics.errorRate > this.ERROR_RATE_THRESHOLD) {
      alerts.push({
        type: "error_rate",
        severity:
          metrics.errorRate > this.ERROR_RATE_THRESHOLD * 2
            ? "critical"
            : "high",
        message: `Error rate (${(metrics.errorRate * 100).toFixed(
          2
        )}%) exceeds threshold (${(this.ERROR_RATE_THRESHOLD * 100).toFixed(
          2
        )}%)`,
        jobId,
        timestamp: Date.now(),
        metrics: {
          errorRate: metrics.errorRate,
          threshold: this.ERROR_RATE_THRESHOLD,
        },
      });
    }

    // Processing time alert
    if (metrics.averageProcessingTime > this.PROCESSING_TIME_THRESHOLD) {
      alerts.push({
        type: "processing_time",
        severity: "medium",
        message: `Average processing time (${(
          metrics.averageProcessingTime / 1000
        ).toFixed(2)}s) exceeds threshold (${
          this.PROCESSING_TIME_THRESHOLD / 1000
        }s)`,
        jobId,
        timestamp: Date.now(),
        metrics: {
          averageProcessingTime: metrics.averageProcessingTime,
          threshold: this.PROCESSING_TIME_THRESHOLD,
        },
      });
    }

    // Memory usage alert (if available)
    if (
      metrics.memoryUsage &&
      metrics.memoryUsage > this.MEMORY_USAGE_THRESHOLD
    ) {
      alerts.push({
        type: "memory_usage",
        severity:
          metrics.memoryUsage > this.MEMORY_USAGE_THRESHOLD * 2
            ? "critical"
            : "high",
        message: `Memory usage (${metrics.memoryUsage}MB) exceeds threshold (${this.MEMORY_USAGE_THRESHOLD}MB)`,
        jobId,
        timestamp: Date.now(),
        metrics: {
          memoryUsage: metrics.memoryUsage,
          threshold: this.MEMORY_USAGE_THRESHOLD,
        },
      });
    }

    // Trigger alert callbacks
    alerts.forEach((alert) => {
      logger.warn("Performance alert triggered", alert);
      this.alertCallbacks.forEach((callback) => {
        try {
          callback(alert);
        } catch (error) {
          logger.error("Error in alert callback:", error);
        }
      });
    });
  }

  /**
   * Get system-wide performance summary
   */
  static getSystemMetrics(): {
    activeJobs: number;
    totalJobsLast24h: number;
    totalCostLast24h: number;
    averageErrorRate: number;
    topCostLocations: Array<{ locationId: number; cost: number }>;
  } {
    const since24h = Date.now() - 24 * 60 * 60 * 1000;
    const allJobs = Array.from(this.metricsStore.values());
    const recent24h = allJobs.filter((m) => m.startTime >= since24h);
    const activeJobs = allJobs.filter((m) => !m.endTime).length;

    const totalCostLast24h = recent24h.reduce((sum, m) => sum + m.totalCost, 0);
    const averageErrorRate =
      recent24h.length > 0
        ? recent24h.reduce((sum, m) => sum + m.errorRate, 0) / recent24h.length
        : 0;

    // Top cost locations
    const locationCosts = new Map<number, number>();
    recent24h.forEach((m) => {
      const current = locationCosts.get(m.locationId) || 0;
      locationCosts.set(m.locationId, current + m.totalCost);
    });

    const topCostLocations = Array.from(locationCosts.entries())
      .map(([locationId, cost]) => ({ locationId, cost }))
      .sort((a, b) => b.cost - a.cost)
      .slice(0, 10);

    return {
      activeJobs,
      totalJobsLast24h: recent24h.length,
      totalCostLast24h,
      averageErrorRate,
      topCostLocations,
    };
  }

  /**
   * Export metrics for external monitoring systems
   */
  static exportMetrics(format: "json" | "prometheus" = "json"): string {
    const systemMetrics = this.getSystemMetrics();

    if (format === "prometheus") {
      return [
        `# HELP ai_enhancement_active_jobs Number of active enhancement jobs`,
        `# TYPE ai_enhancement_active_jobs gauge`,
        `ai_enhancement_active_jobs ${systemMetrics.activeJobs}`,
        ``,
        `# HELP ai_enhancement_cost_24h Total cost in last 24 hours`,
        `# TYPE ai_enhancement_cost_24h counter`,
        `ai_enhancement_cost_24h ${systemMetrics.totalCostLast24h}`,
        ``,
        `# HELP ai_enhancement_error_rate Average error rate`,
        `# TYPE ai_enhancement_error_rate gauge`,
        `ai_enhancement_error_rate ${systemMetrics.averageErrorRate}`,
      ].join("\n");
    }

    return JSON.stringify(
      {
        timestamp: Date.now(),
        system: systemMetrics,
        jobs: Array.from(this.metricsStore.values()),
        batches: Array.from(this.batchMetricsStore.values()),
      },
      null,
      2
    );
  }

  /**
   * Clean up old metrics (call periodically)
   */
  static cleanupOldMetrics(maxAgeHours: number = 168): void {
    // Default 7 days
    const cutoff = Date.now() - maxAgeHours * 60 * 60 * 1000;

    for (const [jobId, metrics] of this.metricsStore.entries()) {
      if (metrics.startTime < cutoff) {
        this.metricsStore.delete(jobId);
      }
    }

    for (const [batchId, metrics] of this.batchMetricsStore.entries()) {
      if (metrics.startTime < cutoff) {
        this.batchMetricsStore.delete(batchId);
      }
    }

    logger.info(`Cleaned up metrics older than ${maxAgeHours} hours`);
  }
}
