import { Product, ProductParams } from "./Product";
import { logger } from "../config/logger";

interface CostEstimate {
  productId: number;
  estimatedTokens: number;
  estimatedCost: number;
  priority: number;
  complexity: "simple" | "medium" | "complex";
  missingFields: string[];
  processingMethod: "fast" | "batch" | "deep";
}

interface BatchOptimizationResult {
  expressBatch: ProductParams[];
  standardBatch: ProductParams[];
  deepEnhancementBatch: ProductParams[];
  totalEstimatedCost: number;
  totalEstimatedTokens: number;
}

export class AIEnhancementCostOptimizer {
  // Cost per 1K tokens (adjust based on current OpenAI pricing)
  private static readonly COST_PER_1K_TOKENS = 0.002; // $0.002 per 1K tokens for GPT-4
  private static readonly BATCH_API_DISCOUNT = 0.5; // 50% discount for batch API

  // Thresholds for processing methods
  private static readonly FAST_MODE_THRESHOLD = 25;
  private static readonly BATCH_MODE_THRESHOLD = 100;

  /**
   * Estimate the cost and complexity of enhancing a single product
   */
  static estimateProductEnhancement(product: ProductParams): CostEstimate {
    const missingFields = this.identifyMissingFields(product);
    const complexity = this.calculateComplexity(product, missingFields);
    const estimatedTokens = this.estimateTokenUsage(
      product,
      missingFields,
      complexity
    );
    const priority = this.calculatePriority(product, missingFields);

    // Determine processing method based on complexity and missing fields
    let processingMethod: "fast" | "batch" | "deep";
    if (missingFields.length <= 2 && complexity === "simple") {
      processingMethod = "fast";
    } else if (missingFields.length <= 5 && complexity !== "complex") {
      processingMethod = "batch";
    } else {
      processingMethod = "deep";
    }

    const baseCost = (estimatedTokens / 1000) * this.COST_PER_1K_TOKENS;
    const estimatedCost =
      processingMethod === "batch"
        ? baseCost * this.BATCH_API_DISCOUNT
        : baseCost;

    return {
      productId: product.id!,
      estimatedTokens,
      estimatedCost,
      priority,
      complexity,
      missingFields,
      processingMethod,
    };
  }

  /**
   * Optimize the processing order and batching strategy for a list of products
   */
  static optimizeBatchProcessing(
    products: ProductParams[]
  ): BatchOptimizationResult {
    const estimates = products.map((product) =>
      this.estimateProductEnhancement(product)
    );

    // Sort by priority (higher priority first) and then by cost efficiency
    estimates.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Higher priority first
      }
      return a.estimatedCost - b.estimatedCost; // Lower cost first for same priority
    });

    const result: BatchOptimizationResult = {
      expressBatch: [],
      standardBatch: [],
      deepEnhancementBatch: [],
      totalEstimatedCost: 0,
      totalEstimatedTokens: 0,
    };

    // Route products to appropriate processing methods
    for (const estimate of estimates) {
      const product = products.find((p) => p.id === estimate.productId)!;

      switch (estimate.processingMethod) {
        case "fast":
          result.expressBatch.push(product);
          break;
        case "batch":
          result.standardBatch.push(product);
          break;
        case "deep":
          result.deepEnhancementBatch.push(product);
          break;
      }

      result.totalEstimatedCost += estimate.estimatedCost;
      result.totalEstimatedTokens += estimate.estimatedTokens;
    }

    logger.info("Batch optimization completed:", {
      totalProducts: products.length,
      expressBatch: result.expressBatch.length,
      standardBatch: result.standardBatch.length,
      deepEnhancementBatch: result.deepEnhancementBatch.length,
      totalEstimatedCost: result.totalEstimatedCost,
      totalEstimatedTokens: result.totalEstimatedTokens,
    });

    return result;
  }

  /**
   * Check for duplicate content to avoid re-processing similar products
   */
  static async deduplicateProducts(
    products: ProductParams[]
  ): Promise<ProductParams[]> {
    const contentHashes = new Map<string, ProductParams>();
    const deduplicated: ProductParams[] = [];

    for (const product of products) {
      const contentHash = this.generateContentHash(product);

      if (!contentHashes.has(contentHash)) {
        contentHashes.set(contentHash, product);
        deduplicated.push(product);
      } else {
        logger.info(
          `Skipping duplicate product: ${product.meta_sku} (similar to ${
            contentHashes.get(contentHash)?.meta_sku
          })`
        );
      }
    }

    logger.info(
      `Deduplication complete: ${products.length} → ${deduplicated.length} products`
    );
    return deduplicated;
  }

  /**
   * Identify missing or incomplete fields in a product
   */
  private static identifyMissingFields(product: ProductParams): string[] {
    const missingFields: string[] = [];

    // Core fields that should be present
    const coreFields = [
      "product_description",
      "category",
      "subcategory",
      "product_tags",
      "percentage_thc",
      "percentage_cbd",
      "mood",
      "effects",
    ];

    for (const field of coreFields) {
      const value = (product as any)[field];
      if (
        !value ||
        (typeof value === "string" && value.trim() === "") ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === "object" && Object.keys(value).length === 0)
      ) {
        missingFields.push(field);
      }
    }

    return missingFields;
  }

  /**
   * Calculate the complexity of enhancing a product
   */
  private static calculateComplexity(
    product: ProductParams,
    missingFields: string[]
  ): "simple" | "medium" | "complex" {
    let complexityScore = 0;

    // Base complexity from missing fields
    complexityScore += missingFields.length;

    // Additional complexity factors
    if (
      !product.product_description ||
      product.product_description.length < 50
    ) {
      complexityScore += 2; // Needs significant description work
    }

    if (
      !product.category ||
      product.category === "Other" ||
      product.category === "Uncategorized"
    ) {
      complexityScore += 2; // Needs categorization
    }

    if (product.raw_product_name && product.raw_product_name.length > 100) {
      complexityScore += 1; // Complex product name to parse
    }

    // Product type complexity
    if (
      product.category?.toLowerCase().includes("concentrate") ||
      product.category?.toLowerCase().includes("extract")
    ) {
      complexityScore += 1; // Concentrates are more complex to categorize
    }

    if (complexityScore <= 3) return "simple";
    if (complexityScore <= 6) return "medium";
    return "complex";
  }

  /**
   * Estimate token usage for enhancing a product
   */
  private static estimateTokenUsage(
    product: ProductParams,
    missingFields: string[],
    complexity: "simple" | "medium" | "complex"
  ): number {
    let baseTokens = 200; // Base tokens for API overhead and basic processing

    // Add tokens based on existing content that needs to be processed
    if (product.product_name) {
      baseTokens += Math.ceil(product.product_name.length / 4);
    }
    if (product.raw_product_name) {
      baseTokens += Math.ceil(product.raw_product_name.length / 4);
    }
    if (product.product_description) {
      baseTokens += Math.ceil(product.product_description.length / 4);
    }
    if (product.brand_name) {
      baseTokens += Math.ceil(product.brand_name.length / 4);
    }

    // Add tokens based on missing fields that need to be generated
    const tokenPerMissingField = {
      product_description: 150,
      category: 10,
      subcategory: 10,
      product_tags: 30,
      percentage_thc: 20,
      percentage_cbd: 20,
      mood: 40,
      effects: 80,
    };

    for (const field of missingFields) {
      baseTokens +=
        tokenPerMissingField[field as keyof typeof tokenPerMissingField] || 20;
    }

    // Complexity multiplier
    const complexityMultiplier = {
      simple: 1.0,
      medium: 1.3,
      complex: 1.6,
    };

    return Math.ceil(baseTokens * complexityMultiplier[complexity]);
  }

  /**
   * Calculate priority score for a product (higher = more important)
   */
  private static calculatePriority(
    product: ProductParams,
    missingFields: string[]
  ): number {
    let priority = 5; // Base priority

    // Higher priority for products with more missing core fields
    if (missingFields.includes("product_description")) {
      priority += 3;
    }
    if (missingFields.includes("category")) {
      priority += 2;
    }
    if (
      missingFields.includes("percentage_thc") ||
      missingFields.includes("percentage_cbd")
    ) {
      priority += 2;
    }

    // Higher priority for products with price (likely more important inventory)
    if (product.latest_price && product.latest_price > 0) {
      priority += 2;
    }

    // Higher priority for branded products
    if (product.brand_name && product.brand_name !== "Unknown") {
      priority += 1;
    }

    // Lower priority for products that are already well-described
    if (
      product.product_description &&
      product.product_description.length > 100
    ) {
      priority -= 1;
    }

    // Higher priority for newer products (if we have creation date)
    if (product.created_at) {
      const daysSinceCreated =
        (Date.now() - new Date(product.created_at).getTime()) /
        (1000 * 60 * 60 * 24);
      if (daysSinceCreated < 30) {
        priority += 1; // Boost newer products
      }
    }

    return Math.max(1, Math.min(10, priority)); // Clamp between 1-10
  }

  /**
   * Generate a content hash for deduplication
   */
  private static generateContentHash(product: ProductParams): string {
    // Create a hash based on key identifying features
    const keyFeatures = [
      product.product_name?.toLowerCase().trim(),
      product.brand_name?.toLowerCase().trim(),
      product.category?.toLowerCase().trim(),
      product.raw_product_name?.toLowerCase().trim(),
      // Normalize THC/CBD percentages
      product.percentage_thc ? Math.round(product.percentage_thc) : null,
      product.percentage_cbd ? Math.round(product.percentage_cbd) : null,
    ].filter(Boolean);

    return Buffer.from(keyFeatures.join("|")).toString("base64");
  }

  /**
   * Get recommended batch size based on complexity distribution
   */
  static getOptimalBatchSize(products: ProductParams[]): number {
    const estimates = products.map((p) => this.estimateProductEnhancement(p));
    const complexityDistribution = {
      simple: estimates.filter((e) => e.complexity === "simple").length,
      medium: estimates.filter((e) => e.complexity === "medium").length,
      complex: estimates.filter((e) => e.complexity === "complex").length,
    };

    // Adjust batch size based on complexity
    const totalProducts = products.length;
    const complexityRatio =
      (complexityDistribution.complex + complexityDistribution.medium * 0.5) /
      totalProducts;

    if (complexityRatio > 0.7) {
      return 10; // Mostly complex products
    }
    if (complexityRatio > 0.4) {
      return 15; // Mixed complexity
    }
    return 20; // Mostly simple products
  }
}
