exports.up = function (knex) {
  return knex.schema.table("products", function (table) {
    // Change short_description from VARCHAR(255) to TEXT to allow longer descriptions
    table.text("short_description").nullable().alter();
  });
};

exports.down = function (knex) {
  return knex.schema.table("products", function (table) {
    // Revert back to VARCHAR(255) - note: this may truncate data
    table.string("short_description", 255).nullable().alter();
  });
};
