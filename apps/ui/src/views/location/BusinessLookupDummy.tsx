import { useState, useEffect, useMemo, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "../../ui";
import TextInput from "../../ui/form/TextInput";
import RadioInput from "../../ui/form/RadioInput";
import { Location } from "../../types";
import { GeocodeResult, miscService } from "../../api/misc";
import { debounce } from "lodash";
import {
  searchRetailersSupabase,
  searchBusinessRetailersSupabase,
  searchBusinessRetailersByLocationSupabase,
  RetailerResult,
  searchRetailersInGooglePlaces,
} from "../../services/firestore";
import { toast, Toaster } from "react-hot-toast";
import { GoogleIcon } from "../../ui/icons";
import GooglePlacesWarningModal from "../../components/dialogs/GooglePlacesWarningModal";

interface BusinessLookupProps {
  onBusinessSelect: (businessData: Partial<Location>) => void;
  onManualEntryClick?: (searchQuery: string) => void;
  selectedBusiness?: Partial<Location> | null;
  isEdit?: boolean;
  onContinue?: () => void;
  forceSearch?: boolean; // New prop to force the search interface when going back
}

// Use RetailerResult directly to avoid duplication
interface SearchResult extends RetailerResult {
  mainText: string;
  secondaryText: string;
  place?: any; // For Google-specific data if needed
}

export default function BusinessLookupDummy({
  onBusinessSelect,
  onManualEntryClick,
  selectedBusiness,
  isEdit,
  onContinue,
  forceSearch = false, // Default to false for backward compatibility
}: BusinessLookupProps) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isContinueLoading, setIsContinueLoading] = useState(false);
  const [hasAddress, setHasAddress] = useState(false);
  const [addressValidated, setAddressValidated] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [showAllResults, setShowAllResults] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [showGooglePlacesWarning, setShowGooglePlacesWarning] = useState(false);
  const [pendingGooglePlacesSelection, setPendingGooglePlacesSelection] = useState<SearchResult | null>(null);

  // Search type state - 'name' or 'location'
  const [searchType, setSearchType] = useState<'name' | 'location'>('name');

  // Add ref to track current search controller
  const searchControllerRef = useRef<AbortController | null>(null);

  // Add ref to track search results for immediate clearing
  const searchResultsRef = useRef<SearchResult[]>([]);

  // Form state for business details
  const [businessData, setBusinessData] = useState<Partial<Location>>(
    selectedBusiness || {
      name: "",
      address: "",
      phone: "",
      website: "",
      city: "",
      state: "",
      zip: "",
      country: "US",
      latitude: undefined,
      longitude: undefined,
      contact_email: "",
      timezone: undefined,
    }
  );

  // Form validation
  const [formErrors, setFormErrors] = useState({
    contact_email: false,
    phone: false,
    address: false,
  });

  // Initialize search query if in edit mode
  useEffect(() => {
    if (selectedBusiness) {
      setSearchQuery(selectedBusiness.name || "");
      setBusinessData(selectedBusiness);
      // If we're in edit mode and have an address, enable the button
      setHasAddress(!!selectedBusiness.address);
      // If the selected business already has coordinates, mark as validated
      setAddressValidated(
        !!(selectedBusiness.latitude && selectedBusiness.longitude)
      );

      // Only show confirmation form if in edit mode AND not forcing search interface
      if (isEdit && !forceSearch) {
        setShowConfirmation(true);
      } else {
        // Always prioritize the search view when forceSearch is true
        setShowConfirmation(false);
      }
    }
  }, [isEdit, selectedBusiness, forceSearch]);

  const debouncedSearch = useMemo(
    () =>
      debounce(async (query: string, forceSearchType?: 'name' | 'location') => {
        if (!query) return;

        // Cancel any ongoing search
        if (searchControllerRef.current) {
          searchControllerRef.current.abort();
        }

        // Create new abort controller for this search
        const controller = new AbortController();
        searchControllerRef.current = controller;

        try {
          setIsLoading(true);

          // Use forced search type if provided, otherwise use current state
          const currentSearchType = forceSearchType || searchType;
          console.log(`🔍 Executing search with type: "${currentSearchType}" for query: "${query}"`);

          // Choose search method based on search type
          let retailers: RetailerResult[];
          if (currentSearchType === 'location') {
            // Search by location (city/state) with Google Places fallback
            console.log(`🏙️ Business location search for "${query}" (searching city/state fields)`);
            retailers = await searchBusinessRetailersByLocationSupabase(query, true, controller.signal);
          } else {
            // Search by name with Google Places fallback (default)
            console.log(`🏢 Business name search for "${query}" (searching name/dispensary_name fields)`);
            retailers = await searchBusinessRetailersSupabase(query, true, controller.signal);
          }

          // Check if search was cancelled
          if (controller.signal.aborted) {
            console.log(`Business search for "${query}" was cancelled`);
            return;
          }

          if (retailers.length === 0) {
            const emptyResults: SearchResult[] = [];
            setSearchResults(emptyResults);
            searchResultsRef.current = emptyResults;
            setIsLoading(false);
            console.log(`❌ No results found for ${currentSearchType} search: "${query}"`);
            return;
          }

          // Transform retailer results to our display format
          const formattedResults: SearchResult[] = retailers.map(
            (retailer: RetailerResult) => ({
              ...retailer,
              // Add display fields needed by the UI
              mainText: retailer.dispensary_name,
              secondaryText: retailer.physical_address
                ? `${retailer.physical_address}, ${retailer.city}, ${retailer.state} ${retailer.zip_code}`
                : `${retailer.city}, ${retailer.state} ${
                    retailer.zip_code || ""
                  }`,
            })
          );

          // Check again if search was cancelled before setting results
          if (controller.signal.aborted) {
            console.log(`Business search for "${query}" was cancelled before setting results`);
            return;
          }

          // Apply limit if not showing all results
          const resultsList = !showAllResults
            ? formattedResults.slice(0, 3)
            : formattedResults;

          setSearchResults(resultsList);
          searchResultsRef.current = resultsList;
          console.log(
            `✅ Business ${searchType} search for "${query}" found ${resultsList.length} results:`
          );
          // Log first few results for debugging with special attention to Transcend
          resultsList.slice(0, 5).forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.mainText} - ${result.secondaryText}`);

            // Special check for the problematic Transcend business
            if (result.mainText && result.mainText.toLowerCase().includes('transcend') && result.mainText.toLowerCase().includes('chicago')) {
              console.log(`   🐛 TRANSCEND FOUND IN ${searchType.toUpperCase()} SEARCH!`);
              console.log(`      Name: "${result.mainText}"`);
              console.log(`      City: "${result.city}"`);
              console.log(`      State: "${result.state}"`);
              console.log(`      Source: ${result.isFromOurDatabase ? 'Database' : 'Google Places'}`);

              if (searchType === 'location') {
                console.log(`   ❌ THIS IS THE BUG - Transcend should NOT appear in location search!`);
              } else {
                console.log(`   ✅ This is correct - Transcend should appear in name search`);
              }
            }
          });
        } catch (error: any) {
          // Don't log errors for cancelled requests
          if (controller.signal.aborted || error.name === 'AbortError') {
            console.log(`Business search for "${query}" was cancelled`);
            return;
          }
          console.error("Error searching business retailers by location:", error);
          const emptyResults: SearchResult[] = [];
          setSearchResults(emptyResults);
          searchResultsRef.current = emptyResults;
        } finally {
          // Only update loading state if this search wasn't cancelled
          if (!controller.signal.aborted) {
            setIsLoading(false);
          }
          // Clear the controller reference if this is the current search
          if (searchControllerRef.current === controller) {
            searchControllerRef.current = null;
          }
        }
      }, 300),
    [showAllResults, searchType]
  );

  // Real-time search as user types
  useEffect(() => {
    if (!searchQuery.trim()) {
      const emptyResults: SearchResult[] = [];
      setSearchResults(emptyResults);
      searchResultsRef.current = emptyResults;
      setShowAllResults(false);
      return;
    }

    debouncedSearch(searchQuery);

    return () => debouncedSearch.cancel();
  }, [searchQuery, showAllResults, debouncedSearch]);

  // Cleanup effect to cancel searches on unmount
  useEffect(() => {
    return () => {
      // Cancel any ongoing search
      if (searchControllerRef.current) {
        searchControllerRef.current.abort();
      }
      // Cancel debounced function
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);

    // Show all results when search button is clicked
    setShowAllResults(true);

    // Run the search
    debouncedSearch(searchQuery);
  };

  // Helper function to validate coordinates
  const validateCoordinates = (lat: number, lng: number): boolean => {
    return (
      !isNaN(lat) &&
      !isNaN(lng) &&
      lat >= -90 &&
      lat <= 90 &&
      lng >= -180 &&
      lng <= 180 &&
      lat !== 0 &&
      lng !== 0
    );
  };

  // Helper function to determine timezone from coordinates
  const getTimezoneFromCoordinates = (lat: number, lng: number): string | undefined => {
    console.log(`🕐 getTimezoneFromCoordinates called with lat=${lat}, lng=${lng}`);

    if (!validateCoordinates(lat, lng)) {
      console.warn(`❌ Invalid coordinates for timezone determination: lat=${lat}, lng=${lng}`);
      return undefined;
    }

    // Define timezone boundaries for major US regions
    // These are approximate boundaries for common US timezones
    const timezoneRegions = [
      // Pacific Time (UTC-8/-7)
      {
        timezone: "America/Los_Angeles",
        bounds: { minLat: 32.5, maxLat: 49.0, minLng: -124.8, maxLng: -114.0 }
      },
      // Mountain Time (UTC-7/-6)
      {
        timezone: "America/Denver",
        bounds: { minLat: 31.3, maxLat: 49.0, minLng: -114.0, maxLng: -104.0 }
      },
      // Central Time (UTC-6/-5)
      {
        timezone: "America/Chicago",
        bounds: { minLat: 25.8, maxLat: 49.4, minLng: -104.0, maxLng: -82.0 }
      },
      // Eastern Time (UTC-5/-4)
      {
        timezone: "America/New_York",
        bounds: { minLat: 24.5, maxLat: 47.5, minLng: -82.0, maxLng: -66.9 }
      },
      // Alaska Time (UTC-9/-8)
      {
        timezone: "America/Anchorage",
        bounds: { minLat: 54.0, maxLat: 71.5, minLng: -180.0, maxLng: -129.0 }
      },
      // Hawaii Time (UTC-10)
      {
        timezone: "Pacific/Honolulu",
        bounds: { minLat: 18.9, maxLat: 28.5, minLng: -178.0, maxLng: -154.0 }
      }
    ];

    console.log(`🔍 Checking timezone regions for coordinates (${lat}, ${lng})`);

    // Find the timezone region that contains these coordinates
    for (const region of timezoneRegions) {
      const { bounds } = region;
      console.log(`  Checking ${region.timezone}: lat ${bounds.minLat}-${bounds.maxLat}, lng ${bounds.minLng}-${bounds.maxLng}`);

      if (lat >= bounds.minLat && lat <= bounds.maxLat &&
          lng >= bounds.minLng && lng <= bounds.maxLng) {
        console.log(`✅ Determined timezone ${region.timezone} from coordinates (${lat}, ${lng})`);
        return region.timezone;
      }
    }

    console.warn(`⚠️ Could not determine timezone for coordinates (${lat}, ${lng}), leaving blank`);
    return undefined;
  };

  const handleResultSelect = async (result: SearchResult) => {
    // Check if this is a Google Places result and show warning modal
    if (result.isFromOurDatabase === false) {
      setPendingGooglePlacesSelection(result);
      setShowGooglePlacesWarning(true);
      return;
    }

    // Proceed with database result selection
    proceedWithBusinessSelection(result);
  };

  const proceedWithBusinessSelection = async (result: SearchResult) => {
    setIsLoading(true);

    if (result.isFromOurDatabase === true) {
      // Handle selection from our database
      const newBusinessData: Partial<Location> = {
        name: result.dispensary_name,
        address: result.physical_address,
        phone: result.contact_phone,
        website: result.website_url,
        city: result.city,
        state: result.state,
        zip: result.zip_code,
        country: result.country || "US",
        latitude: validateCoordinates(result.latitude, result.longitude)
          ? result.latitude
          : undefined,
        longitude: validateCoordinates(result.latitude, result.longitude)
          ? result.longitude
          : undefined,
        contact_email: result.contact_email || "",
        searchMethod: "firebase", // Track that this came from our database
        hasProductData: Boolean(result.productCount && result.productCount > 0),
        productCount: result.productCount || 0,
        isFromOurDatabase: true,
        retailer_id: result.id, // Save the retailer ID from our database
        timezone: validateCoordinates(result.latitude, result.longitude)
          ? getTimezoneFromCoordinates(result.latitude, result.longitude)
          : undefined,
      };

      // Log coordinate validation for debugging
      if (!validateCoordinates(result.latitude, result.longitude)) {
        console.warn(
          `Invalid coordinates for database result: lat=${result.latitude}, lng=${result.longitude}`
        );
      }

      // Skip confirmation screen and proceed directly
      handleDirectContinue(newBusinessData);
    } else {
      // Handle selection from Google Places
      const newBusinessData: Partial<Location> = {
        name: result.dispensary_name,
        address: result.physical_address || "",
        phone: result.contact_phone || "",
        website: result.website_url || "",
        city: result.city || "",
        state: result.state || "",
        zip: result.zip_code || "",
        country: result.country || "US",
        latitude: validateCoordinates(result.latitude, result.longitude)
          ? result.latitude
          : undefined,
        longitude: validateCoordinates(result.latitude, result.longitude)
          ? result.longitude
          : undefined,
        contact_email: result.contact_email || "",
        searchMethod: "api", // Track that this came from search API
        isFromOurDatabase: false,
        timezone: validateCoordinates(result.latitude, result.longitude)
          ? getTimezoneFromCoordinates(result.latitude, result.longitude)
          : undefined,
      };

      // Log coordinate validation for debugging
      if (!validateCoordinates(result.latitude, result.longitude)) {
        console.warn(
          `Invalid coordinates for Google Places result: lat=${result.latitude}, lng=${result.longitude}`
        );
        toast.error(
          t(
            "onboarding.steps.company_info.coordinates_unavailable",
            "Location coordinates not available. You can add them manually later."
          )
        );
      }

      // Skip confirmation screen and proceed directly
      handleDirectContinue(newBusinessData);
    }

    // Clear the search results
    setSearchResults([]);
    setHasAddress(true);
    setIsLoading(false);
  };

  const handleManualEntry = async () => {
    if (!searchQuery.trim()) return;

    // If we have a manual entry handler, use it
    if (onManualEntryClick) {
      onManualEntryClick(searchQuery);
      return;
    }

    console.log(
      `🔍 Manual entry for: "${searchQuery}" - checking database first`
    );

    try {
      // First, try to find this business in our database using the enhanced business search logic
      const retailers = await searchBusinessRetailersSupabase(searchQuery);

      if (retailers.length > 0) {
        // Check if any of the results is a close match to what the user typed
        const exactMatch = retailers.find(
          (retailer) =>
            retailer.dispensary_name
              .toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            searchQuery
              .toLowerCase()
              .includes(retailer.dispensary_name.toLowerCase())
        );

        if (exactMatch) {
          console.log(`✅ Found exact match in database:`, exactMatch);

          // Check if database coordinates match the expected location
          // Parse the search query to extract location information
          const searchParts = searchQuery.split(',').map(part => part.trim());
          let expectedState = '';
          let expectedCity = '';

          // Try to extract state and city from search query
          // Common patterns: "Business Name, City, State" or "Business Name at Address, City, State"
          if (searchParts.length >= 3) {
            expectedState = searchParts[searchParts.length - 1]; // Last part is usually state
            expectedCity = searchParts[searchParts.length - 2]; // Second to last is usually city
          } else if (searchParts.length === 2) {
            expectedState = searchParts[1]; // Assume second part is state
          }

          // Validate coordinates against expected location
          let useDatabase = true;
          if (exactMatch.latitude && exactMatch.longitude && expectedState) {
            // Define rough coordinate bounds for major states
            const stateBounds: Record<string, {minLat: number, maxLat: number, minLng: number, maxLng: number}> = {
              'CA': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 }, // California
              'California': { minLat: 32.5, maxLat: 42.0, minLng: -124.5, maxLng: -114.1 },
              'IL': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 }, // Illinois
              'Illinois': { minLat: 36.9, maxLat: 42.5, minLng: -91.5, maxLng: -87.0 },
              'NY': { minLat: 40.4, maxLat: 45.0, minLng: -79.8, maxLng: -71.8 }, // New York
              'New York': { minLat: 40.4, maxLat: 45.0, minLng: -79.8, maxLng: -71.8 },
              'TX': { minLat: 25.8, maxLat: 36.5, minLng: -106.6, maxLng: -93.5 }, // Texas
              'Texas': { minLat: 25.8, maxLat: 36.5, minLng: -106.6, maxLng: -93.5 },
              'FL': { minLat: 24.4, maxLat: 31.0, minLng: -87.6, maxLng: -79.8 }, // Florida
              'Florida': { minLat: 24.4, maxLat: 31.0, minLng: -87.6, maxLng: -79.8 },
            };

            const bounds = stateBounds[expectedState] || stateBounds[expectedState.toUpperCase()];
            if (bounds) {
              const lat = exactMatch.latitude;
              const lng = exactMatch.longitude;
              const withinBounds = lat >= bounds.minLat && lat <= bounds.maxLat &&
                                 lng >= bounds.minLng && lng <= bounds.maxLng;

              if (!withinBounds) {
                console.warn(
                  `🚨 Database coordinates (${lat}, ${lng}) for "${exactMatch.dispensary_name}" are outside expected bounds for ${expectedState}. Will geocode manually entered address instead.`
                );
                useDatabase = false;
              } else {
                console.log(
                  `✅ Database coordinates (${lat}, ${lng}) are within expected bounds for ${expectedState}`
                );
              }
            }
          }

          if (useDatabase) {
            // Use the database business data with coordinates
            const databaseBusinessData: Partial<Location> = {
              name: exactMatch.dispensary_name,
              address: exactMatch.physical_address,
              phone: exactMatch.contact_phone,
              website: exactMatch.website_url,
              city: exactMatch.city,
              state: exactMatch.state,
              zip: exactMatch.zip_code,
              country: exactMatch.country || "US",
              latitude: validateCoordinates(
                exactMatch.latitude,
                exactMatch.longitude
              )
                ? exactMatch.latitude
                : undefined,
              longitude: validateCoordinates(
                exactMatch.latitude,
                exactMatch.longitude
              )
                ? exactMatch.longitude
                : undefined,
              contact_email: exactMatch.contact_email || "",
              searchMethod: "firebase", // Track that this came from database
              hasProductData: Boolean(
                exactMatch.productCount && exactMatch.productCount > 0
              ),
              productCount: exactMatch.productCount || 0,
              isFromOurDatabase: true,
              timezone: getTimezoneFromCoordinates(exactMatch.latitude, exactMatch.longitude),
            };

            console.log(
              `🎯 Using database coordinates for manual entry: lat=${exactMatch.latitude}, lng=${exactMatch.longitude}`
            );

            // Skip confirmation screen and proceed directly with database data
            handleDirectContinue(databaseBusinessData);
            return;
          } else {
            console.log(
              `⚠️ Database coordinates don't match expected location, falling back to manual geocoding`
            );
            // Fall through to manual entry logic below
          }
        }
      }

      console.log(
        `❌ No database match found for "${searchQuery}", falling back to manual entry`
      );
    } catch (error) {
      console.error("Error searching database for manual entry:", error);
    }

    // Fallback to manual entry - try to geocode if we have address information
    let manualBusinessData: Partial<Location> & { timezone?: string } = {
      ...businessData,
      name: searchQuery,
      timezone: undefined, // Will be set after geocoding if coordinates are found
      searchMethod: "manual" as const,
    };

    // Try to parse address information from the search query
    // Common patterns: "Business Name at Address, City, State ZIP" or "Business Name, Address, City, State"
    const addressMatch = searchQuery.match(/(?:at\s+)?(.+?),?\s*([^,]+),\s*([A-Z]{2})\s*(\d{5})?/i);
    if (addressMatch) {
      const [, possibleAddress, city, state, zip] = addressMatch;

      // If the first part looks like an address (contains numbers), use it
      if (/\d/.test(possibleAddress)) {
        manualBusinessData = {
          ...manualBusinessData,
          address: possibleAddress.replace(/^.*?\s+at\s+/i, '').trim(), // Remove "Business Name at" part
          city: city.trim(),
          state: state.trim().toUpperCase(),
          zip: zip?.trim(),
        };

        console.log(
          `📍 Parsed address from search query: ${manualBusinessData.address}, ${manualBusinessData.city}, ${manualBusinessData.state} ${manualBusinessData.zip || ''}`
        );

        // Try to geocode the parsed address
        try {
          const fullAddress = `${manualBusinessData.address}, ${manualBusinessData.city}, ${manualBusinessData.state} ${manualBusinessData.zip || ''}`.trim();
          console.log(`🔍 Attempting to geocode parsed address: ${fullAddress}`);

          const geocodeResponse = await miscService.searchPlaces(fullAddress);

          if (geocodeResponse?.suggestions && geocodeResponse.suggestions.length > 0) {
            const placeId = geocodeResponse.suggestions[0].placePrediction.placeId;
            const detailResponse = await miscService.geocodePlace(placeId);

            if (detailResponse?.results && detailResponse.results.length > 0) {
              const result = detailResponse.results[0];
              const lat = result.geometry?.location?.lat;
              const lng = result.geometry?.location?.lng;

              if (lat && lng && validateCoordinates(lat, lng)) {
                manualBusinessData.latitude = lat;
                manualBusinessData.longitude = lng;
                manualBusinessData.timezone = getTimezoneFromCoordinates(lat, lng);
                console.log(
                  `✅ Successfully geocoded manual address: lat=${lat}, lng=${lng}, timezone=${manualBusinessData.timezone}`
                );
              }
            }
          }
        } catch (geocodeError) {
          console.warn("Failed to geocode parsed address:", geocodeError);
        }
      }
    }

    // Skip confirmation screen and proceed directly
    handleDirectContinue(manualBusinessData);
  };

  // New function to directly continue with minimal information
  const handleDirectContinue = (data: Partial<Location>) => {
    console.log(`🚀 handleDirectContinue called with data:`, {
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip: data.zip,
      latitude: data.latitude,
      longitude: data.longitude,
      timezone: data.timezone,
      searchMethod: data.searchMethod,
    });

    // IMPORTANT: Always use the current businessData state which contains the latest form values
    // This ensures that manually entered city, state, zip are not lost when clicking "Skip details"
    const finalData = {
      ...data,
      // Override with current form state to ensure we have the latest manually entered values
      name: data.name,
      address: data.address,
      city: data.city,
      state: data.state,
      zip: data.zip,
      phone: data.phone,
      website: data.website,
      contact_email: data.contact_email,
      // Keep coordinates and timezone if they exist
      latitude: data.latitude,
      longitude: data.longitude,
      timezone: data.timezone, // Preserve timezone from geocoding
    };

    console.log(
      `🎯 handleDirectContinue using finalData with current form state:`,
      {
        name: finalData.name,
        address: finalData.address,
        city: finalData.city,
        state: finalData.state,
        zip: finalData.zip,
        latitude: finalData.latitude,
        longitude: finalData.longitude,
        timezone: finalData.timezone,
        searchMethod: finalData.searchMethod,
      }
    );

    // Show success toast
    toast.success(t("Business added successfully!"), {
      duration: 2000,
    });

    // Call the onBusinessSelect with the final data that includes current form state
    onBusinessSelect(finalData);

    // If onContinue is provided, call it
    if (onContinue) {
      onContinue();
    }
  };

  const getAddressComponent = (components: any[], type: string) => {
    const component = components?.find(
      (component: any) => component.types[0] === type
    );
    return component ? component.short_name : "";
  };

  const handleInputChange = (field: string, value: string) => {
    // Reset address validation if address field changes
    if (
      field === "address" ||
      field === "city" ||
      field === "state" ||
      field === "zip"
    ) {
      setAddressValidated(false);
    }

    setBusinessData({
      ...businessData,
      [field]: value,
    });

    // Clear error for this field if it was previously marked as error
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [field]: false,
      });
    }
  };

  const validateForm = () => {
    const errors = {
      contact_email: !businessData.contact_email,
      phone: !businessData.phone,
      address: !businessData.address,
    };

    setFormErrors(errors);

    // Check if any required fields are missing
    const hasErrors = Object.values(errors).some((error) => error);

    return !hasErrors;
  };

  const validateAddress = async (): Promise<boolean> => {
    if (!businessData.address) return false;

    try {
      // Construct a complete address string for better geocoding results
      const addressToGeocode = `${businessData.address}${
        businessData.city ? `, ${businessData.city}` : ""
      }${businessData.state ? `, ${businessData.state}` : ""}${
        businessData.zip ? ` ${businessData.zip}` : ""
      }`;

      console.log(`Attempting to geocode address: ${addressToGeocode}`);

      // Use the searchPlaces API to find the location
      const response = await miscService.searchPlaces(addressToGeocode);

      if (response?.suggestions && response.suggestions.length > 0) {
        // Use the first suggestion's place ID to get detailed geocoding info
        const placeId = response.suggestions[0].placePrediction.placeId;
        const geocodeResponse = await miscService.geocodePlace(placeId);

        if (geocodeResponse?.results && geocodeResponse.results.length > 0) {
          const result = geocodeResponse.results[0];

          // Extract coordinates with validation
          let latitude = 0;
          let longitude = 0;

          if (result.geometry?.location) {
            latitude = result.geometry.location.lat || 0;
            longitude = result.geometry.location.lng || 0;
          }

          // Validate coordinates before using them
          if (!validateCoordinates(latitude, longitude)) {
            console.warn(
              `Invalid coordinates from geocoding: lat=${latitude}, lng=${longitude}`
            );
            toast.error(
              t(
                "onboarding.steps.company_info.coordinates_invalid",
                "Address found but coordinates are invalid. Please check the address."
              )
            );
            return false;
          }

          console.log(
            `Successfully geocoded address: lat=${latitude}, lng=${longitude}`
          );

          // Extract components from the validated address
          setBusinessData({
            ...businessData,
            city:
              getAddressComponent(result.address_components, "locality") ||
              businessData.city,
            state:
              getAddressComponent(
                result.address_components,
                "administrative_area_level_1"
              ) || businessData.state,
            zip:
              getAddressComponent(result.address_components, "postal_code") ||
              businessData.zip,
            country:
              getAddressComponent(result.address_components, "country") || "US",
            latitude: latitude,
            longitude: longitude,
            timezone: getTimezoneFromCoordinates(latitude, longitude),
          });

          setHasAddress(true);
          setAddressValidated(true);
          toast.success(
            t(
              "onboarding.steps.company_info.address_validated",
              "Address validated successfully!"
            )
          );
          return true;
        } else {
          console.warn("No geocoding results found");
          toast.error(
            t(
              "onboarding.steps.company_info.address_validation_failed",
              "Could not validate address. Please check and try again."
            )
          );
          return false;
        }
      } else {
        console.warn("No place suggestions found");
        toast.error(
          t(
            "onboarding.steps.company_info.address_not_found",
            "Address not found. Please check and try again."
          )
        );
        return false;
      }
    } catch (error) {
      console.error("Error validating address:", error);
      toast.error(
        t(
          "onboarding.steps.company_info.address_validation_error",
          "Error validating address. Please try again."
        )
      );
      return false;
    }
  };

  const handleContinue = async () => {
    // First validate basic form fields
    const isValid = validateForm();
    if (!isValid) {
      // Show a prominent toast notification for missing fields
      toast.error(
        t(
          "onboarding.steps.company_info.missing_required_fields",
          "Please fill in all required fields"
        ),
        {
          duration: 4000, // Show for longer duration
          position: "top-center", // Position at top center
          style: {
            borderRadius: "10px",
            background: "#f44336",
            color: "#fff",
            fontSize: "16px",
          },
        }
      );
      return;
    }

    // Set loading state for the Continue button
    setIsContinueLoading(true);

    // If address hasn't been validated yet, validate it now
    if (!addressValidated && businessData.address) {
      const addressIsValid = await validateAddress();
      if (!addressIsValid) {
        setIsContinueLoading(false);
        return;
      }
    }

    console.log(`✅ handleContinue called with businessData:`, {
      name: businessData.name,
      address: businessData.address,
      city: businessData.city,
      state: businessData.state,
      zip: businessData.zip,
      latitude: businessData.latitude,
      longitude: businessData.longitude,
      searchMethod: businessData.searchMethod,
    });

    // Save the business data
    onBusinessSelect(businessData);

    // Reset loading state
    setIsContinueLoading(false);

    if (onContinue) {
      onContinue();
    }
  };

  const handleNewSearch = () => {
    setShowConfirmation(false);
    setSearchQuery("");
  };

  return (
    <div className="space-y-4">
      {/* Add Toaster component */}
      <Toaster
        position="top-center"
        toastOptions={{
          style: {
            zIndex: 9999,
            background: "#333",
            color: "#fff",
            fontSize: "16px",
            padding: "16px",
            border: "1px solid #555",
          },
        }}
      />

      {/* Search Section - Now with additional button to go back to search */}
      {showConfirmation && selectedBusiness?.name && !isEdit && (
        <div className="flex justify-center mb-2">
          <Button
            variant="ghost"
            onClick={() => setShowConfirmation(false)}
            className="text-sm text-primary-accent"
          >
            <span className="mr-2">←</span>
            {t("onboarding.steps.company_info.back_to_search")}
          </Button>
        </div>
      )}

      {/* Business Search Section */}
      {!showConfirmation && (
        <>
          {/* Search Type Selection */}
          <div className="mb-3">
            <RadioInput
              label="Search by"
              value={searchType}
              onChange={(value) => {
                console.log(`🔄 Switching search type from "${searchType}" to "${value}" for query: "${searchQuery}"`);
                console.log(`📋 Current search results before clearing:`, searchResults.length);

                // Cancel any ongoing search immediately
                if (searchControllerRef.current) {
                  searchControllerRef.current.abort();
                  searchControllerRef.current = null;
                }

                // Cancel the debounced function to prevent delayed execution
                debouncedSearch.cancel();

                // IMMEDIATE clearing using both state and ref
                const emptyResults: SearchResult[] = [];
                setSearchResults(emptyResults);
                searchResultsRef.current = emptyResults;
                setIsLoading(false);
                setShowAllResults(false);

                console.log(`🧹 IMMEDIATE clearing - ref results:`, searchResultsRef.current.length);

                // Update search type
                setSearchType(value);

                // Re-run search immediately with the new search type
                if (searchQuery.trim()) {
                  console.log(`🔍 Re-running search with FORCED type "${value}" for query: "${searchQuery}"`);
                  console.log(`🔍 Ref results before new search:`, searchResultsRef.current.length);

                  // Use a shorter delay and force the search type
                  setTimeout(() => {
                    debouncedSearch(searchQuery, value); // Pass the new search type explicitly
                  }, 50);
                }
              }}
              options={[
                { key: 'name' as const, label: "Name" },
                { key: 'location' as const, label: "Location" }
              ]}
              toValue={(option) => option.key}
            />
          </div>

          <div className="flex gap-2 items-center">
            <div className="flex-1">
              <TextInput
                name="business_search"
                value={searchQuery}
                onChange={(value) => {
                  setSearchQuery(value);
                  setShowAllResults(false); // Reset to showing just a few results when typing
                }}
                placeholder={
                  searchType === 'location'
                    ? "Search by city or state..."
                    : "Search by business name..."
                }
              />
            </div>
            {searchQuery.trim() && (
              <Button
                variant="secondary"
                onClick={handleSearch}
                disabled={isLoading}
                className="h-10 flex items-center justify-center px-3"
              >
                {isLoading ? (
                  <>
                    <span className="mr-2 inline-block animate-spin">⏳</span>{" "}
                    {t("searching")}
                  </>
                ) : (
                  <>
                    <span className="mr-2">✨</span> {t("search")}
                  </>
                )}
              </Button>
            )}
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="mt-2 bg-surface-secondary border border-divider rounded-lg overflow-hidden shadow-sm">
              <ul className="divide-y divide-divider">
                {searchResults.map((result) => (
                  <li
                    key={result.id}
                    className="p-3 hover:bg-surface cursor-pointer"
                    onClick={() => handleResultSelect(result)}
                  >
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="text-sm font-medium flex items-center">
                          {result.mainText}
                          {result.isFromOurDatabase === true && (
                            <>
                              <span className="ml-2 text-[#3EDC81]">★</span>
                              <span className="ml-2 text-xs text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-0.5">
                                {result.productCount} products
                              </span>
                            </>
                          )}
                          {result.isFromOurDatabase === false && (
                            <div
                              className="flex items-center ml-2"
                              style={{ width: "14px", height: "4px" }}
                            >
                              <GoogleIcon />
                            </div>
                          )}
                        </div>
                        <div className="text-sm text-primary-soft">
                          {result.secondaryText}
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
              {!showAllResults && searchResults.length >= 3 && (
                <div
                  className="p-2 text-center text-primary-accent cursor-pointer hover:bg-surface"
                  onClick={() => setShowAllResults(true)}
                >
                  {t("show_more_results")}
                </div>
              )}
            </div>
          )}

          {searchQuery.trim() && !isLoading && searchResults.length === 0 && (
            <div className="mt-2 bg-surface-secondary p-4 rounded-lg text-center">
              <p className="mb-2 text-primary-soft">
                {t("no_businesses_found")}
              </p>
            </div>
          )}

          {/* Always show Manual Entry button */}
          {searchQuery.trim() && (
            <div className="mt-4 flex justify-center">
              <Button
                variant="secondary"
                onClick={handleManualEntry}
                className="w-full max-w-md"
              >
                <span className="mr-2">✏️</span>
                {t(
                  "onboarding.steps.company_info.enter_manually",
                  "Enter manually"
                )}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Unified Form - Optional "Continue with minimal details" button */}
      {showConfirmation && (
        <div className="bg-surface-secondary border border-divider rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-medium">
              {businessData.searchMethod === "manual"
                ? t("onboarding.steps.company_info.enter_business_details")
                : t("onboarding.steps.company_info.edit_business_details")}
            </h4>
            <div className="flex items-center">
              {/* Skip additional details button */}
              {!isEdit && (
                <Button
                  variant="ghost"
                  onClick={() => handleDirectContinue(businessData)}
                  className="text-sm mr-2"
                >
                  {t("onboarding.steps.company_info.skip_details")}
                </Button>
              )}
              <Button
                variant="ghost"
                onClick={handleNewSearch}
                className="text-sm"
              >
                <span className="mr-1">🔍</span>{" "}
                {t("onboarding.steps.company_info.new_search")}
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {/* Business Details Section */}
            <div>
              <div className="space-y-3">
                <TextInput
                  name="name"
                  label={t("onboarding.steps.company_info.business_name")}
                  value={businessData.name || ""}
                  onChange={(value) => handleInputChange("name", value)}
                  required
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <TextInput
                    name="website"
                    label={t("onboarding.steps.company_info.website")}
                    value={businessData.website || ""}
                    onChange={(value) => handleInputChange("website", value)}
                    placeholder="https://"
                  />
                  <TextInput
                    name="phone"
                    label={t("onboarding.steps.company_info.phone")}
                    value={businessData.phone || ""}
                    onChange={(value) => handleInputChange("phone", value)}
                    placeholder="(*************"
                    required
                    error={
                      formErrors.phone
                        ? t("onboarding.steps.company_info.required_field")
                        : ""
                    }
                  />
                </div>
              </div>
            </div>

            {/* Contact Information Section */}
            <div>
              <div>
                <TextInput
                  name="contact_email"
                  label={t("onboarding.steps.company_info.contact_email")}
                  value={businessData.contact_email || ""}
                  onChange={(value) =>
                    handleInputChange("contact_email", value)
                  }
                  required
                  error={
                    formErrors.contact_email
                      ? t("onboarding.steps.company_info.required_field")
                      : ""
                  }
                />
              </div>
            </div>

            {/* Address Section */}
            <div>
              <div className="space-y-3">
                <TextInput
                  name="address"
                  label={t("onboarding.steps.company_info.address")}
                  value={businessData.address || ""}
                  onChange={(value) => handleInputChange("address", value)}
                  required
                  error={
                    formErrors.address
                      ? t("onboarding.steps.company_info.required_field")
                      : ""
                  }
                />

                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <TextInput
                    name="city"
                    label={t("onboarding.steps.company_info.city")}
                    value={businessData.city || ""}
                    onChange={(value) => handleInputChange("city", value)}
                  />
                  <TextInput
                    name="state"
                    label={t("onboarding.steps.company_info.state")}
                    value={businessData.state || ""}
                    onChange={(value) => handleInputChange("state", value)}
                  />
                  <TextInput
                    name="zip"
                    label={t("onboarding.steps.company_info.zip")}
                    value={businessData.zip || ""}
                    onChange={(value) => handleInputChange("zip", value)}
                  />
                </div>

                {businessData.latitude && businessData.longitude && (
                  <div className="text-xs text-primary-soft">
                    {t("onboarding.steps.company_info.coordinates")}:{" "}
                    {businessData.latitude.toFixed(6)},{" "}
                    {businessData.longitude.toFixed(6)}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 flex justify-end gap-3">
            {!isEdit && (
              <Button
                variant="ghost"
                onClick={() => handleDirectContinue(businessData)}
              >
                {t(
                  "onboarding.steps.company_info.skip_details",
                  "Skip details"
                )}
              </Button>
            )}
            <Button
              variant="primary"
              onClick={handleContinue}
              disabled={isContinueLoading}
            >
              {isContinueLoading ? (
                <>
                  <span className="mr-2 inline-block animate-spin">⏳</span>
                  {t("validating")}
                </>
              ) : (
                t("onboarding.steps.continue", "Continue")
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Google Places Warning Modal */}
      <GooglePlacesWarningModal
        open={showGooglePlacesWarning}
        onClose={() => setShowGooglePlacesWarning(false)}
        businessName={pendingGooglePlacesSelection?.dispensary_name || ""}
        onConfirm={() => {
          if (pendingGooglePlacesSelection) {
            proceedWithBusinessSelection(pendingGooglePlacesSelection);
            setPendingGooglePlacesSelection(null);
          }
        }}
        onCancel={() => {
          setPendingGooglePlacesSelection(null);
        }}
      />
    </div>
  );
}
