import React, { useCallback, useContext } from "react";
import { useTranslation } from "react-i18next";
import { LocationContext, UserContext } from "../../contexts";
import api from "../../api";
import { Order } from "../../types";
import { SearchTable, useSearchTableQueryState } from "../../ui/SearchTable";
import { formatCurrency, formatDateString } from "../../utils/format";
import { EmptyStateCard } from "../../ui/EmptyCard";
import { FiShoppingCart } from "react-icons/fi";
import { useNavigate } from "react-router-dom";

// Simple Badge component
interface BadgeProps {
  children: React.ReactNode;
  variant?: "primary" | "secondary" | "success" | "destructive";
}

const Badge = ({ children, variant = "secondary" }: BadgeProps) => {
  const variantClasses = {
    primary: "bg-blue-100 text-blue-800",
    secondary: "bg-gray-100 text-gray-800",
    success: "bg-green-100 text-green-800",
    destructive: "bg-red-100 text-red-800",
  };

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${variantClasses[variant]}`}
    >
      {children}
    </span>
  );
};

const UserOrders: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);
  const [user] = useContext(UserContext);

  const state = useSearchTableQueryState<Order>(
    useCallback(
      async (params) => {
        // Use the general orders search API with user filter
        return await api.orders.search(location.id, {
          ...params,
          filter: { user_id: user.id },
        });
      },
      [location.id, user.id]
    )
  );

  const handleViewOrder = (order: Order) => {
    navigate(`/locations/${location.id}/orders/${order.id}`);
  };

  const getStatusBadgeVariant = (status: string): BadgeProps["variant"] => {
    switch (status) {
      case "completed":
        return "success";
      case "cancelled":
        return "destructive";
      case "processing":
        return "primary";
      case "pending":
      default:
        return "secondary";
    }
  };

  return (
    <SearchTable
      {...state}
      enableSearch={false}
      columns={[
        {
          key: "id",
          title: t("order_id"),
          sortable: true,
          cell: ({ item }) => <span className="font-medium">#{item.id}</span>,
        },
        {
          key: "created_at",
          title: t("date"),
          sortable: true,
          cell: ({ item }) => <span>{formatDateString(item.created_at)}</span>,
        },
        {
          key: "status",
          title: t("status"),
          sortable: true,
          cell: ({ item }) => (
            <Badge variant={getStatusBadgeVariant(item.status)}>
              {t(item.status)}
            </Badge>
          ),
        },
        {
          key: "items",
          title: t("items"),
          cell: ({ item }) => (
            <span>
              {item.items?.length || 0} {t("items")}
            </span>
          ),
        },
        {
          key: "total_amount",
          title: t("total"),
          sortable: true,
          cell: ({ item }) => (
            <span className="font-medium">
              {formatCurrency(item.total_amount)}
            </span>
          ),
        },
      ]}
      onRowClick={handleViewOrder}
      searchPlaceholder={t("search_orders", "Search orders...")}
      emptyStateMessage={t(
        "no_orders_found",
        "This user hasn't placed any orders yet."
      )}
    />
  );
};

export default UserOrders;
