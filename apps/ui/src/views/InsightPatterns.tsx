import React, { useState, useEffect, useContext, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON>dal, Alert } from "../ui";
import { useTranslation } from "react-i18next";
import api from "../api";
import { InsightPattern, CreatePatternData, UpdatePatternData, insightPatterns } from "../api/insightPatterns";
import { LocationContext } from "../contexts";
import "./InsightPatterns.css";

interface PatternFormData {
  pattern: string;
  description: string;
  type: "non_actionable" | "actionable";
  is_active: boolean;
  priority: number;
  examples: string[];
}

const defaultFormData: PatternFormData = {
  pattern: "",
  description: "",
  type: "non_actionable",
  is_active: true,
  priority: 0,
  examples: [],
};

// Helper function to safely get examples as array
const getExamplesArray = (examples: any): string[] => {
  if (Array.isArray(examples)) {
    return examples;
  }
  if (typeof examples === 'string') {
    try {
      const parsed = JSON.parse(examples);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }
  return [];
};

export default function InsightPatterns() {
  const { t } = useTranslation();
  const [location] = useContext(LocationContext);
  const [patterns, setPatterns] = useState<InsightPattern[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingPattern, setEditingPattern] = useState<InsightPattern | null>(null);
  const [formData, setFormData] = useState<PatternFormData>(defaultFormData);
  const [testText, setTestText] = useState("");
  const [testResult, setTestResult] = useState<{ matches: boolean; error?: string } | null>(null);

  const loadPatterns = useCallback(async () => {
    if (!location?.id) return;

    try {
      setLoading(true);
      const data = await insightPatterns.getAll(location.id);

      // Ensure examples is always an array
      const normalizedData = data.map(pattern => ({
        ...pattern,
        examples: getExamplesArray(pattern.examples)
      }));

      setPatterns(normalizedData);
      setError(null);
    } catch (err) {
      setError("Failed to load insight patterns");
      console.error("Error loading patterns:", err);
    } finally {
      setLoading(false);
    }
  }, [location?.id]);

  useEffect(() => {
    if (location?.id) {
      loadPatterns();
    }
  }, [location?.id, loadPatterns]);

  const handleCreate = () => {
    setEditingPattern(null);
    setFormData(defaultFormData);
    setTestResult(null);
    setShowModal(true);
  };

  const handleEdit = (pattern: InsightPattern) => {
    setEditingPattern(pattern);
    setFormData({
      pattern: pattern.pattern,
      description: pattern.description,
      type: pattern.type,
      is_active: pattern.is_active,
      priority: pattern.priority,
      examples: getExamplesArray(pattern.examples),
    });
    setTestResult(null);
    setShowModal(true);
  };

  const handleDelete = async (id: number) => {
    if (!location?.id) return;
    if (!confirm("Are you sure you want to delete this pattern?")) {
      return;
    }

    try {
      await insightPatterns.delete(location.id, id);
      await loadPatterns();
    } catch (err) {
      setError("Failed to delete pattern");
      console.error("Error deleting pattern:", err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!location?.id) return;

    try {
      if (editingPattern) {
        await insightPatterns.update(location.id, editingPattern.id, formData);
      } else {
        await insightPatterns.create(location.id, formData);
      }

      setShowModal(false);
      await loadPatterns();
    } catch (err) {
      setError("Failed to save pattern");
      console.error("Error saving pattern:", err);
    }
  };

  const handleTestPattern = async () => {
    if (!location?.id || !formData.pattern || !testText) {
      return;
    }

    try {
      const result = await insightPatterns.test(location.id, {
        pattern: formData.pattern,
        testText: testText,
      });
      setTestResult(result);
    } catch (err) {
      setTestResult({ matches: false, error: "Failed to test pattern" });
      console.error("Error testing pattern:", err);
    }
  };

  const handleExampleAdd = () => {
    setFormData({
      ...formData,
      examples: [...formData.examples, ""],
    });
  };

  const handleExampleChange = (index: number, value: string) => {
    const newExamples = [...formData.examples];
    newExamples[index] = value;
    setFormData({
      ...formData,
      examples: newExamples,
    });
  };

  const handleExampleRemove = (index: number) => {
    const newExamples = formData.examples.filter((_, i) => i !== index);
    setFormData({
      ...formData,
      examples: newExamples,
    });
  };

  if (loading) {
    return <div className="loading">Loading insight patterns...</div>;
  }

  return (
    <div className="insight-patterns">
      <div className="insight-patterns-header">
        <h1>Insight Pattern Management</h1>
        <p>Manage patterns that determine whether insights are actionable for automation or require alternative actions.</p>
        <Button onClick={handleCreate} variant="primary">
          Add New Pattern
        </Button>
      </div>

      {error && (
        <Alert variant="error" title={undefined}>
          {error}
        </Alert>
      )}

      <div className="patterns-grid">
        {patterns.map((pattern) => (
          <div key={pattern.id} className={`pattern-card ${pattern.type}`}>
            <div className="pattern-header">
              <h3>{pattern.description}</h3>
              <div className="pattern-badges">
                <span className={`type-badge ${pattern.type}`}>
                  {pattern.type === "non_actionable" ? "Non-Actionable" : "Actionable"}
                </span>
                {!pattern.is_active && (
                  <span className="status-badge inactive">Inactive</span>
                )}
                <span className="priority-badge">Priority: {pattern.priority}</span>
              </div>
            </div>
            
            <div className="pattern-content">
              <div className="pattern-regex">
                <strong>Pattern:</strong> <code>{pattern.pattern}</code>
              </div>
              
              {(() => {
                const examples = getExamplesArray(pattern.examples);
                return examples.length > 0 && (
                  <div className="pattern-examples">
                    <strong>Examples:</strong>
                    <ul>
                      {examples.slice(0, 3).map((example, index) => (
                        <li key={index}>{example}</li>
                      ))}
                      {examples.length > 3 && (
                        <li>... and {examples.length - 3} more</li>
                      )}
                    </ul>
                  </div>
                );
              })()}
            </div>

            <div className="pattern-actions">
              <Button onClick={() => handleEdit(pattern)} size="small">
                Edit
              </Button>
              <Button 
                onClick={() => handleDelete(pattern.id)} 
                variant="secondary" 
                size="small"
              >
                Delete
              </Button>
            </div>
          </div>
        ))}
      </div>

      {showModal && (
        <Modal
          open={showModal}
          onClose={() => setShowModal(false)}
          title={editingPattern ? "Edit Pattern" : "Create New Pattern"}
        >
          <form onSubmit={handleSubmit} className="pattern-form">
            <div className="form-group">
              <label htmlFor="description">Description</label>
              <input
                id="description"
                type="text"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Human-readable description of what this pattern matches"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="pattern">Regex Pattern</label>
              <input
                id="pattern"
                type="text"
                value={formData.pattern}
                onChange={(e) => setFormData({ ...formData, pattern: e.target.value })}
                placeholder="e.g., develop.*presence"
                required
              />
              <small>Use JavaScript regex syntax (case-insensitive)</small>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="type">Type</label>
                <select
                  id="type"
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as "non_actionable" | "actionable" })}
                >
                  <option value="non_actionable">Non-Actionable</option>
                  <option value="actionable">Actionable</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="priority">Priority</label>
                <input
                  id="priority"
                  type="number"
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 0 })}
                  min="0"
                />
              </div>

              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                  />
                  Active
                </label>
              </div>
            </div>

            <div className="form-group">
              <label>Examples</label>
              {formData.examples.map((example, index) => (
                <div key={index} className="example-input">
                  <input
                    type="text"
                    value={example}
                    onChange={(e) => handleExampleChange(index, e.target.value)}
                    placeholder="Example phrase this pattern should match"
                  />
                  <Button
                    type="button"
                    onClick={() => handleExampleRemove(index)}
                    variant="secondary"
                    size="small"
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <Button type="button" onClick={handleExampleAdd} variant="secondary" size="small">
                Add Example
              </Button>
            </div>

            <div className="form-group">
              <label htmlFor="testText">Test Pattern</label>
              <div className="test-pattern">
                <input
                  id="testText"
                  type="text"
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  placeholder="Enter text to test against the pattern"
                />
                <Button
                  type="button"
                  onClick={handleTestPattern}
                  variant="secondary"
                  size="small"
                  disabled={!formData.pattern || !testText}
                >
                  Test
                </Button>
              </div>
              {testResult && (
                <div className={`test-result ${testResult.matches ? "match" : "no-match"}`}>
                  {testResult.error ? (
                    <span className="error">Error: {testResult.error}</span>
                  ) : (
                    <span>{testResult.matches ? "✅ Matches" : "❌ No match"}</span>
                  )}
                </div>
              )}
            </div>

            <div className="form-actions">
              <Button type="submit" variant="primary">
                {editingPattern ? "Update Pattern" : "Create Pattern"}
              </Button>
              <Button type="button" onClick={() => setShowModal(false)} variant="secondary">
                Cancel
              </Button>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
}
