import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert } from "../../ui";
import Button from "../../ui/Button";

// Demo component to show how Firebase Auth errors are handled
export default function AuthErrorDemo() {
  const { t } = useTranslation();
  const [currentError, setCurrentError] = useState<string | null>(null);

  const simulateError = (errorCode: string) => {
    // This function simulates different Firebase Auth errors
    const mockError = {
      code: errorCode,
      message: `Firebase Auth Error: ${errorCode}`,
    };

    const getFirebaseErrorMessage = (
      error: any,
      t: (key: string) => string,
      isRegister: boolean
    ): string => {
      if (error?.code) {
        switch (error.code) {
          case "auth/email-already-in-use":
            return t("auth_error_email_already_in_use");
          case "auth/weak-password":
            return t("auth_error_weak_password");
          case "auth/invalid-email":
            return t("auth_error_invalid_email");
          case "auth/user-disabled":
            return t("auth_error_user_disabled");
          case "auth/user-not-found":
            return t("auth_error_user_not_found");
          case "auth/wrong-password":
            return t("auth_error_wrong_password");
          case "auth/too-many-requests":
            return t("auth_error_too_many_requests");
          case "auth/operation-not-allowed":
            return t("auth_error_operation_not_allowed");
          case "auth/invalid-credential":
            return t("auth_error_invalid_credential");
          case "auth/requires-recent-login":
            return t("auth_error_requires_recent_login");
          case "auth/popup-closed-by-user":
            return t("auth_error_popup_closed");
          case "auth/popup-blocked":
            return t("auth_error_popup_blocked");
          case "auth/cancelled-popup-request":
            return t("auth_error_popup_cancelled");
          default:
            break;
        }
      }

      // Check for EMAIL_EXISTS from Google Identity Toolkit
      if (
        error?.message?.includes("EMAIL_EXISTS") ||
        error?.error?.message === "EMAIL_EXISTS"
      ) {
        return t("auth_error_email_already_in_use");
      }

      return "Unknown error occurred";
    };

    const errorMessage = getFirebaseErrorMessage(mockError, t, true);
    setCurrentError(errorMessage);
  };

  const errorTypes = [
    { code: "auth/email-already-in-use", label: "Email Already In Use" },
    { code: "auth/weak-password", label: "Weak Password" },
    { code: "auth/invalid-email", label: "Invalid Email" },
    { code: "auth/user-not-found", label: "User Not Found" },
    { code: "auth/wrong-password", label: "Wrong Password" },
    { code: "auth/too-many-requests", label: "Too Many Requests" },
  ];

  // Simulate Google Identity Toolkit EMAIL_EXISTS error
  const simulateEmailExistsError = () => {
    const mockError = {
      error: {
        code: 400,
        message: "EMAIL_EXISTS",
        errors: [
          {
            message: "EMAIL_EXISTS",
            domain: "global",
            reason: "invalid",
          },
        ],
      },
    };

    const errorMessage = t("auth_error_email_already_in_use");
    setCurrentError(errorMessage);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "600px", margin: "0 auto" }}>
      <h2>Firebase Auth Error Handling Demo</h2>
      <p>
        Click the buttons below to simulate different Firebase Auth errors and
        see how they are displayed:
      </p>

      {currentError && (
        <Alert variant="error" title="Authentication Error">
          {currentError}
        </Alert>
      )}

      <div style={{ marginTop: "20px" }}>
        <h3>Firebase Auth Errors:</h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "10px",
            marginBottom: "20px",
          }}
        >
          {errorTypes.map((errorType) => (
            <Button
              key={errorType.code}
              variant="secondary"
              onClick={() => simulateError(errorType.code)}
            >
              {errorType.label}
            </Button>
          ))}
        </div>

        <h3>Google Identity Toolkit Error:</h3>
        <Button variant="secondary" onClick={simulateEmailExistsError}>
          EMAIL_EXISTS (Google Identity Toolkit)
        </Button>

        <div style={{ marginTop: "20px" }}>
          <Button variant="plain" onClick={() => setCurrentError(null)}>
            Clear Error
          </Button>
        </div>
      </div>

      <div
        style={{
          marginTop: "30px",
          padding: "15px",
          backgroundColor: "#f5f5f5",
          borderRadius: "5px",
        }}
      >
        <h4>Implementation Details:</h4>
        <ul>
          <li>
            ✅ Handles standard Firebase Auth error codes
            (auth/email-already-in-use, etc.)
          </li>
          <li>✅ Handles Google Identity Toolkit EMAIL_EXISTS errors</li>
          <li>
            ✅ Provides user-friendly error messages in multiple languages
          </li>
          <li>
            ✅ Displays errors with appropriate styling (red for errors, info
            for success)
          </li>
          <li>
            ✅ Clears previous error messages when starting new operations
          </li>
        </ul>
      </div>
    </div>
  );
}
