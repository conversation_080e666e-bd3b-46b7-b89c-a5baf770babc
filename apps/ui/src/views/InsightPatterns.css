.insight-patterns {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.insight-patterns-header {
  margin-bottom: 32px;
}

.insight-patterns-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.insight-patterns-header p {
  margin: 0 0 24px 0;
  color: var(--color-text-secondary);
  font-size: 16px;
  line-height: 1.5;
}

.loading {
  text-align: center;
  padding: 48px;
  color: var(--color-text-secondary);
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.pattern-card {
  background: var(--color-surface-elevated);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.pattern-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-border-hover);
}

.pattern-card.non_actionable {
  border-left: 4px solid #f59e0b;
}

.pattern-card.actionable {
  border-left: 4px solid #10b981;
}

.pattern-header {
  margin-bottom: 16px;
}

.pattern-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.pattern-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-badge.non_actionable {
  background: #fef3c7;
  color: #92400e;
}

.type-badge.actionable {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: #f3f4f6;
  color: #6b7280;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: #e5e7eb;
  color: #374151;
}

.pattern-content {
  margin-bottom: 16px;
}

.pattern-regex {
  margin-bottom: 12px;
}

.pattern-regex code {
  background: var(--color-background-subtle);
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: var(--color-text-primary);
}

.pattern-examples ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.pattern-examples li {
  margin-bottom: 4px;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.pattern-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Modal Form Styles */
.pattern-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 600px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: var(--color-text-primary);
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  background: var(--color-surface);
  color: var(--color-text-primary);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group small {
  color: var(--color-text-secondary);
  font-size: 12px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.example-input {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.example-input input {
  flex: 1;
}

.test-pattern {
  display: flex;
  gap: 8px;
  align-items: center;
}

.test-pattern input {
  flex: 1;
}

.test-result {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

.test-result.match {
  background: #d1fae5;
  color: #065f46;
}

.test-result.no-match {
  background: #fee2e2;
  color: #991b1b;
}

.test-result .error {
  color: #dc2626;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

/* Checkbox styling */
.form-group label input[type="checkbox"] {
  margin-right: 8px;
  width: auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .insight-patterns {
    padding: 16px;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .pattern-form {
    max-width: none;
  }
}
