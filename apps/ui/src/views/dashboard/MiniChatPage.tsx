import React, {
  useState,
  useCallback,
  useEffect,
  useRef,
  useContext,
} from "react";
import { useTranslation } from "react-i18next";
import { LocationContext } from "../../contexts";
import useAuth from "../../hooks/useAuth";
import ReactMarkdown from "react-markdown";
import { SendIcon, ChevronDownIcon } from "../../ui/icons";
import {
  FaThumbsUp,
  FaThumbsDown,
  FaDownload,
  FaRegThumbsUp,
  FaRegThumbsDown,
  FaPaperclip,
  FaTimesCircle,
  FaFile,
  FaImage,
  FaInfoCircle,
} from "react-icons/fa";
import SmokeyIcon from "../../assets/smokey_icon.png";
import "./MiniChatPage.css";
import { chatService, Chat } from "../../api/chat";
import { Product } from "../../ui/ProductCard";
import api from "../../api";

// Special content components
import ChatSpecialContent from "../../ui/ChatSpecialContent";
import SVGGraphRenderer from "../../ui/SVGGraphRenderer";
import TableRenderer from "../../ui/TableRenderer";
import ProductTable from "../../ui/ProductTable";

// Import necessary package for HTML in Markdown
import rehypeRaw from "rehype-raw";

// Import the new hook
import { useAgentBasedQuestions } from "../../hooks/useAgentBasedQuestions";

// Types from ChatPage.tsx
interface ChatMessage {
  message_id: string;
  role: "human" | "ai";
  content: string;
  timestamp: string;
  feedback?: "like" | "dislike";
  data?: {
    type?:
      | "insight"
      | "automation"
      | "step"
      | "products"
      | "productType"
      | "feelings"
      | "graph"
      | "image"
      | "table";
    insight?: any;
    plan?: any;
    step?: any;
    products?: Product[];
    productTypes?: Array<{
      name: string;
      description: string;
      image: string;
    }>;
    feelings?: Array<{
      name: string;
      description?: string;
      image?: string;
    }>;
    graph?: {
      svgContent?: string;
      svgUrl?: string;
      graphData?: any;
      title?: string;
      type?: "chart" | "table";
    };
    images?: Array<{
      url: string;
      prompt: string;
      path: string;
    }>;
    table?: {
      title?: string;
      headers: string[];
      rows: string[][];
    };
    query_type?: string;
    prompt?: string;
    error_messages?: string[];
    suggested_next_questions?: string[];
    sales_data?: any[];
    sales_data_type?: string;
    agent?: {
      name: string;
      role?: string;
    };
    attachments?: Array<{
      name: string;
      size: number;
      type: string;
      url?: string;
      id?: string;
    }>;
  };
  specialType?:
    | "products"
    | "productType"
    | "feelings"
    | "graph"
    | "image"
    | "table";
  isStreaming?: boolean;
  error?: boolean;
  chat_id?: string;
  agent_id?: string;
  metadata?: any;
}

// Using the Chat type from the chat API
interface ChatAgentProps {
  agents:
    | Array<{
        agent_id: string;
        name: string;
        role?: string;
        avatar?: string;
      }>
    | undefined;
}

interface MiniChatPageProps {
  activeTab?: "chat" | "insights";
}

// TypingIndicator component for showing when the AI is typing
const TypingIndicator = () => (
  <div className="typing-indicator">
    <span></span>
    <span></span>
    <span></span>
  </div>
);

// ChatAgents component to show the agents involved in a chat
const ChatAgents: React.FC<ChatAgentProps> = ({ agents }) => {
  if (!agents || agents.length === 0) {
    return null;
  }

  const maxVisibleAgents = 3;
  const visibleAgents = agents.slice(0, maxVisibleAgents);
  const remainingCount =
    agents.length > maxVisibleAgents ? agents.length - maxVisibleAgents : 0;

  return (
    <div className="mini-chat-agents">
      {visibleAgents.map((agent, index) => (
        <div
          key={agent.agent_id}
          className="mini-chat-agent-avatar"
          style={{
            zIndex: visibleAgents.length - index,
            marginLeft: index === 0 ? "0" : "-8px",
            backgroundColor: !agent.avatar
              ? `var(--color-agent-${index % 5})`
              : undefined,
          }}
        >
          {agent.avatar ? (
            <img src={agent.avatar} alt={agent.name} />
          ) : (
            <div className="mini-avatar-placeholder">
              {agent.name.charAt(0)}
            </div>
          )}
          <div className="mini-agent-tooltip">
            <div className="mini-agent-name">{agent.name}</div>
            {agent.role && <div className="mini-agent-role">{agent.role}</div>}
          </div>
        </div>
      ))}
      {remainingCount > 0 && (
        <div className="mini-chat-agent-more">
          <span>+{remainingCount}</span>
        </div>
      )}
    </div>
  );
};

// Update the greetings to include subtitles like in ModernChat.tsx
const greetingPhrases = [
  {
    greeting: "What's up, bud?",
    subtitle: "Your cannabis marketing assistant is ready to roll.",
  },
  {
    greeting: "High there!",
    subtitle: "Let's elevate your cannabis marketing strategy today.",
  },
  {
    greeting: "How's it growing?",
    subtitle: "Ask me anything about your cannabis business needs.",
  },
  {
    greeting: "Need a lift?",
    subtitle: "I'm here to help boost your cannabis business.",
  },
  {
    greeting: "Ready to elevate?",
    subtitle: "Let's take your marketing strategy to new heights.",
  },
];

const MiniChatPage: React.FC<MiniChatPageProps> = ({ activeTab = "chat" }) => {
  const { t } = useTranslation();
  const [location] = useContext(LocationContext);
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [feedbackGiven, setFeedbackGiven] = useState<{
    [key: string]: "like" | "dislike" | null;
  }>({});

  // State for handling special content types
  const [wizardSelections, setWizardSelections] = useState<{
    [key: string]: boolean;
  }>({});

  // Add state for agent availability
  const [agentAvailability, setAgentAvailability] = useState<any>(null);

  // Add state for showing agent unavailable notification
  const [showAgentNotification, setShowAgentNotification] = useState<{
    show: boolean;
    agentStatus: any;
  }>({ show: false, agentStatus: null });

  // Use the new dynamic question generation hook instead of hardcoded questions
  const { questions: thoughtBubbles, loading: questionsLoading } =
    useAgentBasedQuestions();

  // Initialize conversation starters and greeting
  const [randomGreeting] = useState(
    greetingPhrases[Math.floor(Math.random() * greetingPhrases.length)]
  );

  // Helper function to check if an agent is available
  const getAgentStatus = (agentId: string) => {
    if (!agentAvailability) return { available: true, agent: null };

    // Check if agent is in available list
    const availableAgent = agentAvailability.available?.find(
      (a: any) => a.agentId === agentId
    );
    if (availableAgent) return { available: true, agent: availableAgent };

    // Check if agent is unavailable
    const unavailableAgent = agentAvailability.unavailable?.find(
      (a: any) => a.agentId === agentId
    );
    if (unavailableAgent) return { available: false, agent: unavailableAgent };

    // Check if agent is partially available
    const partialAgent = agentAvailability.partial?.find(
      (a: any) => a.agentId === agentId
    );
    if (partialAgent)
      return { available: true, agent: partialAgent, partial: true };

    return { available: true, agent: null }; // Default to available if not found
  };

  // Component for agent unavailable notification
  const AgentUnavailableNotification = ({
    agentStatus,
    onDismiss,
  }: {
    agentStatus: { available: boolean; agent: any; partial?: boolean };
    onDismiss: () => void;
  }) => {
    if (agentStatus.available) return null;

    return (
      <div className="agent-unavailable-notification">
        <div className="notification-content">
          <FaInfoCircle className="notification-icon" />
          <div className="notification-text">
            <strong>
              {agentStatus.agent?.name || "This agent"} is currently
              unavailable.
            </strong>
            <p>Missing: {agentStatus.agent?.missingRequirements?.join(", ")}</p>
            <p>
              Your message will be handled by the general assistant instead.
            </p>
          </div>
          <button className="notification-dismiss" onClick={onDismiss}>
            ×
          </button>
        </div>
      </div>
    );
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Add these states after other useState declarations in MiniChatPage
  const [attachments, setAttachments] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch user chats on component mount
  useEffect(() => {
    const loadChats = async () => {
      try {
        if (!user) return;
        const chatsData = await chatService.getChats();
        setChats(chatsData);
      } catch (error) {
        console.error("Error loading chats:", error);
      }
    };

    const loadAgentAvailability = async () => {
      try {
        if (location && location.id) {
          const availability = await api.agents.getAvailability(location.id);
          setAgentAvailability(availability);
        }
      } catch (error) {
        console.error("Error loading agent availability:", error);
      }
    };

    loadChats();
    loadAgentAvailability();
  }, [user, location.id]);

  // Load messages for selected chat
  const loadMessages = useCallback(async (chatId: string) => {
    try {
      setIsLoading(true);
      const messagesData = await chatService.getChatMessages(chatId);

      // Convert API message format to ChatMessage format
      const formattedMessages: ChatMessage[] = messagesData.map((msg: any) => ({
        message_id: msg.id || msg.message_id,
        role: msg.role === "user" ? "human" : "ai",
        content: msg.content,
        timestamp: msg.timestamp,
        data: msg.data,
        chat_id: chatId,
        agent_id: msg.agent_id,
        metadata: msg.metadata,
      }));

      // Sort messages by timestamp in ascending order (oldest first)
      const sortedMessages = formattedMessages.sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      setMessages(sortedMessages);
    } catch (error) {
      console.error("Error loading messages:", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Load new messages only when a chat is selected
  useEffect(() => {
    if (currentChat) {
      loadMessages(currentChat.chat_id);
    }
  }, [currentChat, loadMessages]);

  // Handle chat selection
  const handleChatSelect = async (chat: Chat) => {
    setCurrentChat(chat);
    await loadMessages(chat.chat_id);
    setIsDropdownOpen(false);
  };

  // Handle creating a new chat
  const handleNewChat = () => {
    setCurrentChat(null);
    setMessages([]);
  };

  // Add this function to handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setAttachments((prev) => [...prev, ...newFiles]);
      // Reset the input so the same file can be selected again
      e.target.value = "";
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  // Handle input changes to detect @mentions and check agent availability
  const handleInputChange = (value: string) => {
    setInputValue(value);

    // Check for @mentions
    const mentionMatch = value.match(/@(\w+)/);
    if (mentionMatch) {
      const mentionedName = mentionMatch[1].toLowerCase();
      const agentNameToId: { [key: string]: string } = {
        smokey: "1",
        craig: "2",
        pops: "3",
        ezal: "4",
        "money mike": "5",
        "mrs. parker": "6",
        "mrs parker": "6",
        parker: "6",
        deebo: "7",
      };

      const agentId = agentNameToId[mentionedName];
      if (agentId) {
        const agentStatus = getAgentStatus(agentId);
        if (!agentStatus.available) {
          setShowAgentNotification({ show: true, agentStatus });
          return;
        }
      }
    }

    // Clear notification if no mention or agent is available
    setShowAgentNotification({ show: false, agentStatus: null });
  };

  // Modify the handleSendMessage function to include attachments
  const handleSendMessage = async () => {
    if ((!inputValue.trim() && attachments.length === 0) || isLoading) return;

    // Store the input value and clear the input field immediately
    const messageContent = inputValue.trim();
    setInputValue(""); // Clear input right away for better UX

    // Dismiss any active notifications
    setShowAgentNotification({ show: false, agentStatus: null });

    const messageId = Date.now().toString();
    const responseMessageId = `${messageId}_response`;

    // Store attachments before clearing them from the UI
    const messagesToUpload = [...attachments];
    setAttachments([]); // Clear attachments for better UX

    // Create a readable message showing attachments
    let displayContent = messageContent;
    if (messagesToUpload.length > 0) {
      const fileNames = messagesToUpload.map((file) => file.name).join(", ");
      if (messageContent) {
        displayContent = `${messageContent}\n\nAttached: ${fileNames}`;
      } else {
        displayContent = `Attached: ${fileNames}`;
      }
    }

    // Create user message with proper ID and chat_id
    const userMessage: ChatMessage = {
      message_id: messageId,
      role: "human",
      content: displayContent,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
      // Add attachments to the message data
      data:
        messagesToUpload.length > 0
          ? {
              attachments: messagesToUpload.map((file) => ({
                name: file.name,
                size: file.size,
                type: file.type,
                // No URL yet - will be updated after upload
              })),
            }
          : undefined,
    };

    // Create initial loading message for the AI response with same chat_id
    const loadingMessage: ChatMessage = {
      message_id: responseMessageId,
      role: "ai",
      content: "",
      timestamp: new Date().toISOString(),
      isStreaming: true,
      chat_id: currentChat?.chat_id || "",
    };

    // Add both messages to chat
    setMessages((prev) => [...prev, userMessage, loadingMessage]);
    setIsLoading(true);

    try {
      // Get location ID from URL
      const locationId = window.location.pathname.split("/")[2];

      // Let the backend handle agent selection based on intent analysis
      // Only detect @mentions to pass specific agent requests, otherwise use default
      let agentId = "1"; // Default to SMOKEY

      // Check for explicit @mentions in the message
      const mentionMatch = messageContent.match(/@(\w+)/);
      if (mentionMatch) {
        const mentionedName = mentionMatch[1].toLowerCase();
        // Map common agent names/aliases to IDs
        const agentNameToId: { [key: string]: string } = {
          smokey: "1",
          craig: "2",
          pops: "3",
          ezal: "4",
          "money mike": "5",
          "mrs. parker": "6",
          "mrs parker": "6",
          parker: "6",
          deebo: "7",
        };

        agentId = agentNameToId[mentionedName] || "1";

        if (agentNameToId[mentionedName]) {
          console.log(
            `Agent mention detected: ${mentionedName} -> Agent ${agentId}`
          );
        }
      }

      // Upload files if any
      let uploadedFiles: {
        name: string;
        size: number;
        type: string;
        url: string;
        id: string;
      }[] = [];
      if (messagesToUpload.length > 0) {
        try {
          setUploadProgress(0);
          uploadedFiles = await Promise.all(
            messagesToUpload.map(async (file) => {
              const response = await new Promise<{
                ok: boolean;
                json: () => any;
              }>((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(
                  "POST",
                  `/api/admin/locations/${locationId}/chats/uploads`
                );

                xhr.upload.addEventListener("progress", (progressEvent) => {
                  let percentCompleted = 0;
                  if (progressEvent.total) {
                    percentCompleted = Math.round(
                      (progressEvent.loaded * 100) / progressEvent.total
                    );
                  }
                  setUploadProgress(percentCompleted);
                });

                xhr.onload = () => {
                  if (xhr.status >= 200 && xhr.status < 300) {
                    resolve({
                      ok: true,
                      json: () => JSON.parse(xhr.responseText),
                    });
                  } else {
                    reject(new Error(`Failed to upload file: ${file.name}`));
                  }
                };

                xhr.onerror = () =>
                  reject(new Error(`Failed to upload file: ${file.name}`));

                const formData = new FormData();
                formData.append("file", file);
                formData.append("chat_id", currentChat?.chat_id || "");
                formData.append("location_id", locationId);

                xhr.send(formData);
              });

              const data = await response.json();
              return {
                name: file.name,
                size: file.size,
                type: file.type,
                url: data.url,
                id: data.id,
              };
            })
          );
          setUploadProgress(null);
        } catch (error) {
          console.error("File upload error:", error);
          setUploadProgress(null);
          // Continue with the message send even if file upload fails
          // We'll update the message to indicate failed uploads
        }
      }

      // Send message to the backend
      const response = await chatService.sendMessage({
        message: messageContent,
        agentId, // Use dynamic agent ID instead of hardcoded "1"
        chatId: currentChat?.chat_id || undefined,
        locationId,
        attachments:
          uploadedFiles.length > 0
            ? uploadedFiles.map((f) => ({ id: f.id }))
            : undefined,
      });

      // Update with the final AI response
      setMessages((prev) => {
        const updatedMessages = [...prev];

        // Find and update the user message with uploaded file URLs
        const userMessageIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === messageId
        );

        if (userMessageIndex !== -1 && uploadedFiles.length > 0) {
          updatedMessages[userMessageIndex] = {
            ...updatedMessages[userMessageIndex],
            data: {
              ...updatedMessages[userMessageIndex].data,
              attachments: uploadedFiles,
            },
          };
        }

        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          // Handle different response formats by first casting to unknown
          const aiResponse = response as unknown as any;

          // Check if we have a messages array in the response (new format)
          if (
            aiResponse &&
            aiResponse.messages &&
            aiResponse.messages.length > 0
          ) {
            // Get the last message which should be the assistant's response
            const assistantMessage = aiResponse.messages.find(
              (msg: any) => msg.role === "assistant"
            );

            if (assistantMessage) {
              const finalMessage: ChatMessage = {
                message_id:
                  (assistantMessage.id || "").toString() || responseMessageId,
                role: "ai",
                content: assistantMessage.content || "",
                timestamp:
                  assistantMessage.timestamp || new Date().toISOString(),
                isStreaming: false,
                chat_id: aiResponse.chat?.chat_id || currentChat?.chat_id || "",
                data: assistantMessage.data,
                agent_id: assistantMessage.agent_id || undefined,
                metadata: assistantMessage.metadata || {},
              };
              updatedMessages[loadingIndex] = finalMessage;
            }
          } else if (aiResponse) {
            // Fallback to the old format for backward compatibility
            const finalMessage: ChatMessage = {
              message_id: (aiResponse.id || "").toString() || responseMessageId,
              role: "ai",
              content: aiResponse.content || "",
              timestamp: aiResponse.timestamp || new Date().toISOString(),
              isStreaming: false,
              chat_id: aiResponse.chat?.chat_id || currentChat?.chat_id || "",
              data: aiResponse.data,
              agent_id: aiResponse.agent_id || undefined,
              metadata: aiResponse.metadata || {},
            };
            updatedMessages[loadingIndex] = finalMessage;
          }
        }
        return updatedMessages;
      });

      // Update chat list if needed
      if (!currentChat && response.chat) {
        const chatsData = await chatService.getChats();
        setChats(chatsData);
        const newChat = chatsData.find(
          (chat: Chat) => chat.chat_id === response.chat?.chat_id
        );
        if (newChat) setCurrentChat(newChat);
      } else {
        // Refresh the chat list to get updated chat names
        // This ensures we have the latest chat names after auto-generation
        const chatsData = await chatService.getChats();
        setChats(chatsData);

        // If we have a current chat, make sure it's updated with the latest name
        if (currentChat) {
          const updatedCurrentChat = chatsData.find(
            (chat: Chat) => chat.chat_id === currentChat.chat_id
          );
          if (updatedCurrentChat) setCurrentChat(updatedCurrentChat);
        }
      }
    } catch (error: any) {
      console.error("Error sending message:", error);

      // Determine appropriate error message based on error type
      let errorMessage = "Sorry, I encountered an error. Please try again.";

      if (error.response?.status === 400 || error.response?.status === 404) {
        const errorData = error.response?.data;

        // Check if this is an agent availability error
        if (
          errorData?.error?.message?.includes("Agent not available") ||
          errorData?.error?.message?.includes("required integrations missing")
        ) {
          errorMessage = `No product data available. Please import the product data first to use this feature.`;
        } else if (errorData?.message?.includes("Agent not available")) {
          errorMessage =
            "This agent is currently unavailable. Please try a different agent or contact your administrator.";
        } else if (error.response?.status === 404) {
          errorMessage =
            "The requested resource was not found. Please try again.";
        }
      } else if (error.response?.status >= 500) {
        errorMessage = "Server error occurred. Please try again in a moment.";
      }

      // Update the loading message with error state
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          updatedMessages[loadingIndex] = {
            message_id: responseMessageId,
            role: "ai",
            content: errorMessage,
            timestamp: new Date().toISOString(),
            isStreaming: false,
            error: true,
            chat_id: currentChat?.chat_id || "",
          };
        }
        return updatedMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle feedback for messages
  const handleFeedback = async (
    messageId: string,
    feedbackType: "like" | "dislike"
  ) => {
    try {
      // Update local state immediately for responsiveness
      setFeedbackGiven((prev) => ({ ...prev, [messageId]: feedbackType }));

      // Update feedback locally only - API doesn't support feedback yet
      console.log("Feedback recorded locally:", messageId, feedbackType);
    } catch (error) {
      console.error("Error saving feedback:", error);
    }
  };

  const handleProductTypeSelect = (productType: any) => {
    // Create a new user message with the selected product type
    const userMessage: ChatMessage = {
      message_id: Date.now().toString(),
      role: "human",
      content: `I'm interested in ${productType.name}`,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Trigger a new AI response based on the selection
    handleSendSpecialMessage(`Show me ${productType.name} products`);
  };

  const handleFeelingsSelect = (feelings: string[]) => {
    // Create a new user message with the selected feelings
    const userMessage: ChatMessage = {
      message_id: Date.now().toString(),
      role: "human",
      content: `I want to feel: ${feelings.join(", ")}`,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    setMessages((prev) => [...prev, userMessage]);

    // Trigger a new AI response based on the selection
    handleSendSpecialMessage(
      `Show me products that make me feel ${feelings.join(", ")}`
    );
  };

  // Helper function to send a message without user input
  const handleSendSpecialMessage = async (content: string) => {
    const messageId = Date.now().toString();
    const responseMessageId = `${messageId}_response`;

    // Create user message with the content
    const userMessage: ChatMessage = {
      message_id: messageId,
      role: "human",
      content: content,
      timestamp: new Date().toISOString(),
      chat_id: currentChat?.chat_id || "",
    };

    // Create initial loading message for the AI response
    const loadingMessage: ChatMessage = {
      message_id: responseMessageId,
      role: "ai",
      content: "",
      timestamp: new Date().toISOString(),
      isStreaming: true,
      chat_id: currentChat?.chat_id || "",
    };

    // Add both messages to chat to ensure correct order
    setMessages((prev) => [...prev, userMessage, loadingMessage]);
    setIsLoading(true);

    try {
      // Get location ID from URL
      const locationId = window.location.pathname.split("/")[2];

      // Let the backend handle agent selection based on intent analysis
      // Only detect @mentions to pass specific agent requests, otherwise use default
      let agentId = "1"; // Default to SMOKEY

      // Check for explicit @mentions in the message
      const mentionMatch = content.match(/@(\w+)/);
      if (mentionMatch) {
        const mentionedName = mentionMatch[1].toLowerCase();
        // Map common agent names/aliases to IDs
        const agentNameToId: { [key: string]: string } = {
          smokey: "1",
          craig: "2",
          pops: "3",
          ezal: "4",
          "money mike": "5",
          "mrs. parker": "6",
          "mrs parker": "6",
          parker: "6",
          deebo: "7",
        };

        agentId = agentNameToId[mentionedName] || "1";

        if (agentNameToId[mentionedName]) {
          console.log(
            `Agent mention detected: ${mentionedName} -> Agent ${agentId}`
          );
        }
      }

      // Send the message to the backend - let it handle agent selection
      const response = await chatService.sendMessage({
        message: content,
        agentId, // Use dynamic agent ID instead of hardcoded "1"
        chatId: currentChat?.chat_id || undefined,
        locationId,
      });

      // Update with the final AI response
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          // Handle different response formats by first casting to unknown
          const aiResponse = response as unknown as any;

          // Check if we have a messages array in the response (new format)
          if (
            aiResponse &&
            aiResponse.messages &&
            aiResponse.messages.length > 0
          ) {
            // Get the last message which should be the assistant's response
            const assistantMessage = aiResponse.messages.find(
              (msg: any) => msg.role === "assistant"
            );

            if (assistantMessage) {
              const finalMessage: ChatMessage = {
                message_id:
                  (assistantMessage.id || "").toString() || responseMessageId,
                role: "ai",
                content: assistantMessage.content || "",
                timestamp:
                  assistantMessage.timestamp || new Date().toISOString(),
                isStreaming: false,
                chat_id: aiResponse.chat?.chat_id || "",
                data: assistantMessage.data,
                agent_id: assistantMessage.agent_id || undefined,
                metadata: assistantMessage.metadata || {},
              };
              updatedMessages[loadingIndex] = finalMessage;
            }
          } else if (aiResponse) {
            // Fallback to the old format for backward compatibility
            const finalMessage: ChatMessage = {
              message_id: (aiResponse.id || "").toString() || responseMessageId,
              role: "ai",
              content: aiResponse.content || "",
              timestamp: aiResponse.timestamp || new Date().toISOString(),
              isStreaming: false,
              chat_id: aiResponse.chat?.chat_id || "",
              data: aiResponse.data,
              agent_id: aiResponse.agent_id || undefined,
              metadata: aiResponse.metadata || {},
            };
            updatedMessages[loadingIndex] = finalMessage;
          }
        }
        return updatedMessages;
      });

      // Update chat list if needed
      if (!currentChat && response.chat) {
        const chatsData = await chatService.getChats();
        setChats(chatsData);
        const newChat = chatsData.find(
          (chat: Chat) => chat.chat_id === response.chat?.chat_id
        );
      }
    } catch (error: any) {
      console.error("Error sending message:", error);

      // Determine appropriate error message based on error type
      let errorMessage = "Sorry, I encountered an error. Please try again.";

      if (error.response?.status === 400 || error.response?.status === 404) {
        const errorData = error.response?.data;

        // Check if this is an agent availability error
        if (
          errorData?.error?.message?.includes("Agent not available") ||
          errorData?.error?.message?.includes("required integrations missing")
        ) {
          errorMessage = `No product data available. Please import the product data first to use this feature.`;
        } else if (errorData?.message?.includes("Agent not available")) {
          errorMessage =
            "This agent is currently unavailable. Please try a different agent or contact your administrator.";
        } else if (error.response?.status === 404) {
          errorMessage =
            "The requested resource was not found. Please try again.";
        }
      } else if (error.response?.status >= 500) {
        errorMessage = "Server error occurred. Please try again in a moment.";
      }

      // Update the loading message with error state
      setMessages((prev) => {
        const updatedMessages = [...prev];
        const loadingIndex = updatedMessages.findIndex(
          (msg) => msg.message_id === responseMessageId
        );

        if (loadingIndex !== -1) {
          updatedMessages[loadingIndex] = {
            message_id: responseMessageId,
            role: "ai",
            content: errorMessage,
            timestamp: new Date().toISOString(),
            isStreaming: false,
            error: true,
            chat_id: currentChat?.chat_id || "",
          };
        }
        return updatedMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Render message content
  const renderMessageContent = (message: ChatMessage) => {
    if (message.isStreaming) {
      return (
        <div className="message-content-wrapper">
          {message.content && (
            <ReactMarkdown rehypePlugins={[rehypeRaw]}>
              {message.content}
            </ReactMarkdown>
          )}
          <TypingIndicator />
        </div>
      );
    }
    // Handle different special content types
    const isProductsContent =
      message.data?.type === "products" ||
      message.specialType === "products" ||
      (message.data?.products && message.data.products.length > 0);

    const isProductTypeContent =
      message.data?.type === "productType" ||
      message.specialType === "productType" ||
      (message.data?.productTypes && message.data.productTypes.length > 0);

    const isFeelingsContent =
      message.data?.type === "feelings" ||
      message.specialType === "feelings" ||
      (message.data?.feelings && message.data.feelings.length > 0);

    const isGraphContent =
      message.data?.type === "graph" ||
      message.specialType === "graph" ||
      message.data?.graph?.svgContent ||
      message.data?.graph?.graphData;

    const isTableContent =
      message.data?.type === "table" ||
      message.specialType === "table" ||
      (message.data?.sales_data && message.data.sales_data.length > 0) ||
      message.data?.graph?.graphData?.type === "table";

    const isImageContent =
      message.data?.type === "image" ||
      message.specialType === "image" ||
      message.data?.images;

    // Handle attachments display
    const hasAttachments =
      message.data?.attachments && message.data.attachments.length > 0;

    if (hasAttachments) {
      return (
        <div className="message-content">
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {message.content}
          </ReactMarkdown>

          <div className="message-attachments">
            {message.data?.attachments &&
              message.data.attachments.map((attachment: any, index: number) => {
                const isImage = attachment.type.startsWith("image/");
                return (
                  <div key={index} className="attachment-item">
                    {isImage ? (
                      <div className="attachment-image">
                        <img
                          src={attachment.url}
                          alt={attachment.name}
                          onClick={() => window.open(attachment.url, "_blank")}
                        />
                      </div>
                    ) : (
                      <div
                        className="attachment-file"
                        onClick={() => window.open(attachment.url, "_blank")}
                      >
                        <div className="attachment-icon">
                          <FaFile size={24} />
                        </div>
                        <div className="attachment-info">
                          <div className="attachment-name">
                            {attachment.name}
                          </div>
                          <div className="attachment-size">
                            {formatFileSize(attachment.size)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      );
    }

    // If the message has product data, render product grid
    if (isProductsContent && message.data?.products) {
      return (
        <div className="message-content">
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {message.content}
          </ReactMarkdown>
          <div className="product-grid">
            {message.data.products.map((product: any) => (
              <div
                key={product.id}
                className="product-card"
                onClick={() => handleProductClick(product)}
              >
                <div className="product-image">
                  {product.imageUrl ? (
                    <img src={product.imageUrl} alt={product.name} />
                  ) : (
                    <div className="no-image">No image</div>
                  )}
                </div>
                <div className="product-info">
                  <h3>{product.name}</h3>
                  <p className="product-price">${product.price}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    // If the message has feelings data, render feelings selection
    if (isFeelingsContent && message.data?.feelings) {
      return (
        <div className="message-content">
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {message.content}
          </ReactMarkdown>
          <div className="feelings-selection">
            <h3>Select how you want to feel:</h3>
            <div className="feelings-grid">
              {Array.isArray(message.data.feelings) &&
                message.data.feelings.map((feeling: any) => (
                  <button
                    key={typeof feeling === "string" ? feeling : feeling.name}
                    className="feeling-btn"
                    onClick={() =>
                      handleFeelingsSelect([
                        typeof feeling === "string" ? feeling : feeling.name,
                      ])
                    }
                  >
                    {typeof feeling === "string" ? feeling : feeling.name}
                  </button>
                ))}
            </div>
          </div>
        </div>
      );
    }

    // If the message has product types data, render product type selection
    if (isProductTypeContent && message.data?.productTypes) {
      return (
        <div className="message-content">
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {message.content}
          </ReactMarkdown>
          <div className="product-types-selection">
            <h3>What are you interested in?</h3>
            <div className="product-types-grid">
              {message.data.productTypes.map((type: any) => (
                <button
                  key={type.id}
                  className="product-type-btn"
                  onClick={() => handleProductTypeSelect(type)}
                >
                  {type.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      );
    }

    // Handle graph content
    if (isGraphContent && message.data?.graph) {
      return (
        <div className="message-content">
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {message.content}
          </ReactMarkdown>
          <SVGGraphRenderer
            svgContent={message.data.graph.svgContent}
            svgUrl={message.data.graph.svgUrl}
            graphData={message.data.graph.graphData}
            title={message.data.graph.title}
          />
        </div>
      );
    }

    // Handle table content
    if (isTableContent) {
      let tableData:
        | { title?: string; headers: string[]; rows: string[][] }
        | undefined;
      let textContent = message.content;

      try {
        // Try to parse table data from the content
        if (
          typeof message.content === "string" &&
          message.content.includes('"table":')
        ) {
          const parsed = JSON.parse(message.content);
          tableData = parsed.table;
          textContent = parsed.text || "";
        } else if (message.data && "table" in message.data) {
          tableData = message.data.table as {
            title?: string;
            headers: string[];
            rows: string[][];
          };
        }
      } catch (e) {
        console.error("Failed to parse table data:", e);
      }

      return (
        <div className="message-content">
          {textContent && (
            <ReactMarkdown
              rehypePlugins={[rehypeRaw]}
              components={{
                h1: ({ node, ...props }) => (
                  <h1 style={{ fontSize: "1.4rem" }} {...props} />
                ),
                h2: ({ node, ...props }) => (
                  <h2 style={{ fontSize: "1.3rem" }} {...props} />
                ),
                h3: ({ node, ...props }) => (
                  <h3 style={{ fontSize: "1.1rem" }} {...props} />
                ),
                h4: ({ node, ...props }) => (
                  <h4 style={{ fontSize: "1rem" }} {...props} />
                ),
                h5: ({ node, ...props }) => (
                  <h5 style={{ fontSize: "0.9rem" }} {...props} />
                ),
                h6: ({ node, ...props }) => (
                  <h6 style={{ fontSize: "0.85rem" }} {...props} />
                ),
              }}
            >
              {textContent}
            </ReactMarkdown>
          )}

          {tableData && (
            <div className="table-container mt-4">
              {tableData.title && (
                <div className="table-title font-semibold mb-2">
                  {tableData.title}
                </div>
              )}
              <table className="min-w-full border-collapse border border-gray-300">
                <thead className="bg-gray-100">
                  <tr>
                    {tableData.headers.map((header: string, i: number) => (
                      <th
                        key={i}
                        className="border border-gray-300 px-4 py-2 text-left text-sm"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tableData.rows.map((row: string[], i: number) => (
                    <tr
                      key={i}
                      className={i % 2 === 0 ? "bg-white" : "bg-gray-50"}
                    >
                      {row.map((cell: string, j: number) => (
                        <td
                          key={j}
                          className="border border-gray-300 px-4 py-2 text-sm"
                        >
                          {cell}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Render suggestion buttons if available */}
          {message.data?.suggested_next_questions &&
            message.data.suggested_next_questions.length > 0 && (
              <div className="suggestion-buttons">
                {message.data.suggested_next_questions.map(
                  (question: string, index: number) => (
                    <button
                      key={index}
                      className="suggestion-button"
                      onClick={() => {
                        setInputValue(question);
                        handleSendMessage();
                      }}
                    >
                      {question}
                    </button>
                  )
                )}
              </div>
            )}
        </div>
      );
    }

    // Handle image content
    if (isImageContent) {
      return (
        <div className="message-content">
          <ReactMarkdown
            rehypePlugins={[rehypeRaw]}
            components={{
              h1: ({ node, ...props }) => (
                <h1 style={{ fontSize: "1.4rem" }} {...props} />
              ),
              h2: ({ node, ...props }) => (
                <h2 style={{ fontSize: "1.3rem" }} {...props} />
              ),
              h3: ({ node, ...props }) => (
                <h3 style={{ fontSize: "1.1rem" }} {...props} />
              ),
              h4: ({ node, ...props }) => (
                <h4 style={{ fontSize: "1rem" }} {...props} />
              ),
              h5: ({ node, ...props }) => (
                <h5 style={{ fontSize: "0.9rem" }} {...props} />
              ),
              h6: ({ node, ...props }) => (
                <h6 style={{ fontSize: "0.85rem" }} {...props} />
              ),
            }}
          >
            {message.content}
          </ReactMarkdown>
          <div className="message-images">
            {message.data?.images &&
              message.data.images.map((image: any, index: number) => (
                <div key={index} className="message-image">
                  <img
                    src={image.url}
                    alt={image.prompt || "Generated image"}
                  />
                  {image.prompt && (
                    <div className="image-prompt">{image.prompt}</div>
                  )}
                </div>
              ))}
          </div>
        </div>
      );
    }

    // Default case: render markdown content with suggested questions if available
    return (
      <div className="message-content">
        {message.content && (
          <ReactMarkdown
            rehypePlugins={[rehypeRaw]}
            components={{
              h1: ({ node, ...props }) => (
                <h1
                  style={{
                    fontSize: "1.4rem",
                    marginTop: "1rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h2: ({ node, ...props }) => (
                <h2
                  style={{
                    fontSize: "1.3rem",
                    marginTop: "1rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h3: ({ node, ...props }) => (
                <h3
                  style={{
                    fontSize: "1.1rem",
                    marginTop: "0.75rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h4: ({ node, ...props }) => (
                <h4
                  style={{
                    fontSize: "1rem",
                    marginTop: "0.75rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h5: ({ node, ...props }) => (
                <h5
                  style={{
                    fontSize: "0.9rem",
                    marginTop: "0.5rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              h6: ({ node, ...props }) => (
                <h6
                  style={{
                    fontSize: "0.85rem",
                    marginTop: "0.5rem",
                    marginBottom: "0.5rem",
                    fontWeight: "bold",
                  }}
                  {...props}
                />
              ),
              blockquote: ({ node, ...props }) => (
                <blockquote
                  style={{
                    borderLeft: "4px solid #ddd",
                    paddingLeft: "1rem",
                    margin: "0.5rem 0",
                    color: "#555",
                  }}
                  {...props}
                />
              ),
              hr: ({ node, ...props }) => (
                <hr
                  style={{
                    margin: "1rem 0",
                    border: "none",
                    height: "1px",
                    backgroundColor: "#ddd",
                  }}
                  {...props}
                />
              ),
              code: ({ node, className, children, ...props }: any) => {
                const match = /language-(\w+)/.exec(className || "");
                const isInline = !match && !className;
                return isInline ? (
                  <code
                    style={{
                      backgroundColor: "#f0f0f0",
                      padding: "0.1rem 0.3rem",
                      borderRadius: "3px",
                      fontSize: "0.9em",
                    }}
                    {...props}
                  >
                    {children}
                  </code>
                ) : (
                  <code
                    style={{
                      display: "block",
                      backgroundColor: "#f5f5f5",
                      padding: "0.5rem 1rem",
                      borderRadius: "4px",
                      overflowX: "auto",
                      fontSize: "0.9em",
                    }}
                    {...props}
                  >
                    {children}
                  </code>
                );
              },
            }}
            className="markdown-body"
          >
            {message.content}
          </ReactMarkdown>
        )}

        {/* Render suggestion buttons if available */}
        {message.data?.suggested_next_questions &&
          message.data.suggested_next_questions.length > 0 && (
            <div className="suggestion-buttons">
              {message.data.suggested_next_questions.map(
                (question: string, index: number) => (
                  <button
                    key={index}
                    className="suggestion-button"
                    onClick={() => {
                      setInputValue(question);
                      handleSendMessage();
                    }}
                  >
                    {question}
                  </button>
                )
              )}
            </div>
          )}
      </div>
    );
  };

  // Function to format message time
  const formatMessageTime = (dateString: string) => {
    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn("Invalid date string:", dateString);
        return "";
      }

      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "";
    }
  };

  // Handle thought bubble click
  const handleWelcomeOptionClick = (prompt: string) => {
    setInputValue(prompt);
    handleSendMessage();
  };

  // Helper functions to extract image URLs from markdown
  const extractImageUrls = (content: string): string[] => {
    const regex = /!\[.*?\]\((.*?)\)/g;
    const urls: string[] = [];
    let match;
    while ((match = regex.exec(content)) !== null) {
      urls.push(match[1]);
    }
    return urls;
  };

  // Handle image download
  const handleImageDownload = (content: string) => {
    const imageUrls = extractImageUrls(content);
    imageUrls.forEach((url) => {
      const link = document.createElement("a");
      link.href = url;
      link.download = url.split("/").pop() || "image.png";
      link.target = "_blank";
      link.rel = "noopener noreferrer";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    });
  };

  // Handle special content interactions
  const handleProductClick = (product: Product) => {
    console.log("Product clicked:", product);
    // Implement product click behavior
  };

  // Add helper function to format file sizes
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + " B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + " KB";
    else return (bytes / 1048576).toFixed(1) + " MB";
  };

  // Add CSS for enhanced markdown components
  useEffect(() => {
    // Add custom styles for markdown-enhanced elements
    const style = document.createElement("style");
    style.textContent = `
      .markdown-body .highlight {
        border-left: 4px solid #f0b849;
        padding: 0.5rem 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
      }
      
      .markdown-body .note {
        border-left: 4px solid #1890ff;
        padding: 0.5rem 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
      }
      
      .markdown-body details {
        border: 1px solid #eaeaea;
        border-radius: 4px;
        padding: 0.5rem;
        margin: 0.5rem 0;
      }
      
      .markdown-body details summary {
        cursor: pointer;
        font-weight: bold;
        padding: 0.25rem 0;
      }
      
      .markdown-body ul.checklist {
        list-style-type: none;
        padding-left: 1rem;
      }
      
      .markdown-body ul.checklist li {
        margin-bottom: 0.25rem;
        display: flex;
        align-items: flex-start;
      }
      
      .markdown-body table {
        border-collapse: collapse;
        margin: 1rem 0;
        width: 100%;
      }
      
      .markdown-body table th,
      .markdown-body table td {
        border: 1px solid #ddd;
        padding: 0.5rem;
      }
      
      .markdown-body table th {
        font-weight: bold;
        text-align: left;
      }
      
      .fallback-indicator {
        position: relative;
        display: inline-flex;
        align-items: center;
        margin-left: 8px;
        color: var(--color-warning-text, #f5a623);
      }

      .fallback-indicator .fallback-tooltip {
        visibility: hidden;
        width: 240px;
        background-color: var(--color-background-dark, #2a2a2a);
        color: #fff;
        text-align: left;
        border-radius: 6px;
        padding: 10px;
        position: absolute;
        z-index: 10;
        bottom: 125%;
        left: 50%;
        margin-left: -120px;
        opacity: 0;
        transition: opacity 0.3s;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      }

      .fallback-indicator:hover .fallback-tooltip {
        visibility: visible;
        opacity: 1;
      }

      .fallback-tooltip p {
        margin: 0 0 5px 0;
        font-size: 0.9em;
      }

      .fallback-tooltip ul {
        margin: 0;
        padding-left: 18px;
        font-size: 0.85em;
      }

      .fallback-tooltip li {
        margin-bottom: 4px;
      }

      .agent-unavailable-notification {
        position: absolute;
        bottom: 100%;
        left: 0;
        right: 0;
        margin-bottom: 8px;
        background-color: var(--color-warning-bg, #fff3cd);
        border: 1px solid var(--color-warning-border, #ffeaa7);
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 10;
      }

      .notification-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .notification-icon {
        color: var(--color-warning-text, #856404);
        margin-top: 2px;
        flex-shrink: 0;
      }

      .notification-text {
        flex: 1;
      }

      .notification-text strong {
        display: block;
        margin-bottom: 4px;
        color: var(--color-warning-text, #856404);
      }

      .notification-text p {
        margin: 0 0 4px 0;
        font-size: 0.875rem;
        color: var(--color-warning-text, #856404);
      }

      .notification-dismiss {
        background: none;
        border: none;
        color: var(--color-warning-text, #856404);
        cursor: pointer;
        font-size: 18px;
        line-height: 1;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
      }

      .notification-dismiss:hover {
        background-color: rgba(0,0,0,0.1);
      }

      .mini-message-input-container {
        position: relative;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Render the chat UI
  return (
    <div className="mini-chat-container">
      {/* Header with Chat Dropdown */}
      <div className="mini-chat-header">
        <div className="mini-chat-title-row">
          <div
            className="mini-chat-dropdown"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <span className="mini-chat-selected">
              {currentChat ? currentChat.name : t("new_chat")}
            </span>
            <ChevronDownIcon />

            {isDropdownOpen && (
              <div className="mini-chat-dropdown-content">
                <div
                  className="mini-chat-dropdown-item"
                  onClick={handleNewChat}
                >
                  <span>{t("create_new_chat")}</span>
                </div>
                <div className="mini-chat-dropdown-divider"></div>
                {chats.map((chat) => (
                  <div
                    key={chat.chat_id}
                    className="mini-chat-dropdown-item"
                    onClick={() => handleChatSelect(chat)}
                  >
                    <span>{chat.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="mini-chat-messages" ref={messagesEndRef}>
        {messages.length === 0 && !isLoading ? (
          <div className="mini-chat-welcome">
            <div className="mini-smokey-image">
              <img src={SmokeyIcon} alt="Smokey" />
            </div>
            <h3>{randomGreeting.greeting}</h3>
            <p>{randomGreeting.subtitle}</p>

            <div className="mini-thought-cloud">
              {questionsLoading ? (
                <div className="mini-thought-bubble loading">
                  Loading personalized questions...
                </div>
              ) : (
                thoughtBubbles.slice(0, 4).map((bubble, index) => (
                  <button
                    key={index}
                    className="mini-thought-bubble"
                    onClick={() => handleWelcomeOptionClick(bubble.prompt)}
                    title={bubble.description}
                  >
                    <span className="bubble-text">{bubble.prompt}</span>
                    {bubble.agent && (
                      <span className="bubble-agent">via {bubble.agent}</span>
                    )}
                  </button>
                ))
              )}
            </div>
          </div>
        ) : (
          messages.map((message, index) => {
            const isUser = message.role === "human";
            const prevMessage = index > 0 ? messages[index - 1] : null;
            const showHeader =
              !isUser && (!prevMessage || prevMessage.role !== "ai");

            return (
              <div
                key={message.message_id}
                className={`mini-chat-message-row ${
                  isUser ? "user-message-row" : "bot-message-row"
                }`}
              >
                {!isUser && (
                  <div className="mini-message-avatar bot-avatar">
                    <div className="mini-message-avatar-placeholder">
                      {message.metadata?.agent_name
                        ? message.metadata.agent_name.charAt(0)
                        : "A"}
                    </div>
                  </div>
                )}
                <div className="mini-chat-message-content">
                  {showHeader && (
                    <div className="mini-message-header bot-header">
                      {message.metadata?.agent_name ||
                        message.data?.agent?.name ||
                        "Smokey"}
                      {message.metadata?.agent_role && (
                        <span className="mini-agent-role">
                          {message.metadata.agent_role}
                        </span>
                      )}
                      {message.metadata?.isFallbackResponse &&
                        message.metadata?.originalRequestedAgent && (
                          <div className="fallback-indicator">
                            <FaInfoCircle size={12} />
                            <div className="fallback-tooltip">
                              <p>
                                <strong>
                                  {message.metadata.originalRequestedAgent.name}{" "}
                                  is unavailable.
                                </strong>
                              </p>
                              <p>Missing requirements:</p>
                              <ul>
                                {message.metadata.originalRequestedAgent.missingRequirements.map(
                                  (req: string) => (
                                    <li key={req}>{req}</li>
                                  )
                                )}
                              </ul>
                            </div>
                          </div>
                        )}
                      {message.metadata?.selected_by && (
                        <span className="mini-agent-selection-info">
                          • {message.metadata.selected_by}
                        </span>
                      )}
                    </div>
                  )}
                  <div
                    className={`mini-chat-message ${
                      isUser ? "mini-user-message" : "mini-bot-message"
                    }`}
                  >
                    {renderMessageContent(message)}
                    <span className="mini-message-timestamp">
                      {formatMessageTime(message.timestamp)}
                    </span>
                  </div>
                  {!isUser && (
                    <div className="mini-feedback-buttons">
                      <button
                        onClick={() =>
                          handleFeedback(message.message_id, "like")
                        }
                        className={`mini-feedback-button ${
                          feedbackGiven[message.message_id] === "like"
                            ? "feedback-given"
                            : ""
                        }`}
                      >
                        <FaRegThumbsUp size={12} />
                      </button>
                      <button
                        onClick={() =>
                          handleFeedback(message.message_id, "dislike")
                        }
                        className={`mini-feedback-button ${
                          feedbackGiven[message.message_id] === "dislike"
                            ? "feedback-given"
                            : ""
                        }`}
                      >
                        <FaRegThumbsDown size={12} />
                      </button>
                    </div>
                  )}
                </div>
                {isUser && (
                  <div className="mini-message-avatar user-avatar">
                    <div className="mini-message-avatar-placeholder">U</div>
                  </div>
                )}
              </div>
            );
          })
        )}
        {/* {isLoading && <TypingIndicator />} */}
      </div>

      {/* Message Input */}
      <div className="mini-message-input-container">
        {/* Show attachments preview */}
        {attachments.length > 0 && (
          <div className="attachments-preview">
            {attachments.map((file, index) => (
              <div key={index} className="attachment-preview-item">
                <div className="attachment-preview-icon">
                  {file.type.startsWith("image/") ? (
                    <div className="attachment-preview-image">
                      <img src={URL.createObjectURL(file)} alt={file.name} />
                    </div>
                  ) : (
                    <FaFile size={16} />
                  )}
                </div>
                <span className="attachment-preview-name">{file.name}</span>
                <button
                  className="attachment-remove-btn"
                  onClick={() => removeAttachment(index)}
                >
                  <FaTimesCircle size={16} />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Show upload progress if uploading */}
        {uploadProgress !== null && (
          <div className="upload-progress">
            <div
              className="upload-progress-bar"
              style={{ width: `${uploadProgress}%` }}
            ></div>
            <span className="upload-progress-text">{uploadProgress}%</span>
          </div>
        )}

        <input
          type="text"
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyPress={(e) => {
            if (
              e.key === "Enter" &&
              (inputValue.trim() || attachments.length > 0) &&
              !isLoading
            ) {
              e.preventDefault();
              handleSendMessage();
            }
          }}
          placeholder={t("type_message")}
          className="mini-message-input"
          disabled={isLoading}
        />

        {/* File upload button and hidden input */}
        {/* <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileSelect}
          style={{ display: "none" }}
          multiple
        />

        <button
          className="mini-attachment-button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isLoading}
        >
          <FaPaperclip />
        </button> */}

        <button
          className="mini-send-button"
          onClick={handleSendMessage}
          disabled={
            isLoading || (!inputValue.trim() && attachments.length === 0)
          }
        >
          <SendIcon />
        </button>

        {/* Show agent unavailable notification */}
        {showAgentNotification.show && (
          <AgentUnavailableNotification
            agentStatus={showAgentNotification.agentStatus}
            onDismiss={() =>
              setShowAgentNotification({ show: false, agentStatus: null })
            }
          />
        )}
      </div>
    </div>
  );
};

export default MiniChatPage;
