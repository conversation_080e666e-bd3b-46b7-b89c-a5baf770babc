import React from "react";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";

interface MarkdownRendererProps {
  content: string;
  className?: string;
  compact?: boolean; // For smaller text rendering
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = "",
  compact = false,
}) => {
  const baseFontSize = compact ? "0.9rem" : "1rem";
  const headingScale = compact ? 0.8 : 1;

  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw]}
      className={`markdown-renderer ${className}`}
      components={{
        h1: ({ node, ...props }) => (
          <h1
            style={{
              fontSize: `${1.4 * headingScale}rem`,
              marginTop: compact ? "0.5rem" : "1rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h2: ({ node, ...props }) => (
          <h2
            style={{
              fontSize: `${1.3 * headingScale}rem`,
              marginTop: compact ? "0.5rem" : "1rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h3: ({ node, ...props }) => (
          <h3
            style={{
              fontSize: `${1.1 * headingScale}rem`,
              marginTop: compact ? "0.4rem" : "0.75rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h4: ({ node, ...props }) => (
          <h4
            style={{
              fontSize: `${1.05 * headingScale}rem`,
              marginTop: compact ? "0.4rem" : "0.75rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h5: ({ node, ...props }) => (
          <h5
            style={{
              fontSize: baseFontSize,
              marginTop: compact ? "0.3rem" : "0.5rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        h6: ({ node, ...props }) => (
          <h6
            style={{
              fontSize: baseFontSize,
              marginTop: compact ? "0.3rem" : "0.5rem",
              marginBottom: "0.5rem",
              fontWeight: "bold",
              lineHeight: "1.3",
            }}
            {...props}
          />
        ),
        p: ({ node, ...props }) => (
          <p
            style={{
              fontSize: baseFontSize,
              lineHeight: "1.5",
              marginBottom: compact ? "0.5rem" : "0.75rem",
            }}
            {...props}
          />
        ),
        blockquote: ({ node, ...props }) => (
          <blockquote
            style={{
              borderLeft: "4px solid #ddd",
              paddingLeft: "1rem",
              margin: compact ? "0.5rem 0" : "0.75rem 0",
              color: "#666",
              fontStyle: "italic",
            }}
            {...props}
          />
        ),
        hr: ({ node, ...props }) => (
          <hr
            style={{
              margin: compact ? "0.75rem 0" : "1rem 0",
              border: "none",
              height: "1px",
              backgroundColor: "#ddd",
            }}
            {...props}
          />
        ),
        ul: ({ node, ...props }) => (
          <ul
            style={{
              paddingLeft: "1.5rem",
              marginBottom: compact ? "0.5rem" : "0.75rem",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        ol: ({ node, ...props }) => (
          <ol
            style={{
              paddingLeft: "1.5rem",
              marginBottom: compact ? "0.5rem" : "0.75rem",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        li: ({ node, ...props }) => (
          <li
            style={{
              marginBottom: "0.25rem",
              lineHeight: "1.5",
            }}
            {...props}
          />
        ),
        strong: ({ node, ...props }) => (
          <strong
            style={{
              fontWeight: "600",
            }}
            {...props}
          />
        ),
        em: ({ node, ...props }) => (
          <em
            style={{
              fontStyle: "italic",
            }}
            {...props}
          />
        ),
        code: ({ node, className, children, ...props }: any) => {
          const match = /language-(\w+)/.exec(className || "");
          const isInline = !match && !className;
          const fontSize = compact ? "0.8rem" : "0.9rem";

          return isInline ? (
            <code
              style={{
                backgroundColor: "#f0f0f0",
                padding: "0.1rem 0.3rem",
                borderRadius: "3px",
                fontSize,
                fontFamily: "monospace",
              }}
              {...props}
            >
              {children}
            </code>
          ) : (
            <code
              style={{
                display: "block",
                backgroundColor: "#f5f5f5",
                padding: compact ? "0.4rem 0.8rem" : "0.5rem 1rem",
                borderRadius: "4px",
                overflowX: "auto",
                fontSize,
                fontFamily: "monospace",
                marginBottom: compact ? "0.5rem" : "0.75rem",
              }}
              {...props}
            >
              {children}
            </code>
          );
        },
        table: ({ node, ...props }) => (
          <table
            style={{
              borderCollapse: "collapse",
              margin: compact ? "0.5rem 0" : "1rem 0",
              width: "100%",
              fontSize: baseFontSize,
            }}
            {...props}
          />
        ),
        th: ({ node, ...props }) => (
          <th
            style={{
              border: "1px solid #ddd",
              padding: compact ? "0.4rem" : "0.5rem",
              backgroundColor: "#f8f9fa",
              fontWeight: "bold",
              textAlign: "left",
            }}
            {...props}
          />
        ),
        td: ({ node, ...props }) => (
          <td
            style={{
              border: "1px solid #ddd",
              padding: compact ? "0.4rem" : "0.5rem",
            }}
            {...props}
          />
        ),
        a: ({ node, ...props }) => (
          <a
            style={{
              color: "#007bff",
              textDecoration: "underline",
            }}
            target="_blank"
            rel="noopener noreferrer"
            {...props}
          />
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default MarkdownRenderer;
