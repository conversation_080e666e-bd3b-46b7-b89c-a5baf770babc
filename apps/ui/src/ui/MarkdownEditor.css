.markdown-editor {
  width: 100%;
}

.markdown-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.markdown-editor .form-label {
  font-weight: 500;
  color: var(--color-text, #374151);
  font-size: 0.875rem;
}

.markdown-editor .required {
  color: var(--color-error, #ef4444);
  margin-left: 0.25rem;
}

.markdown-editor .form-subtitle {
  font-size: 0.75rem;
  color: var(--color-text-muted, #6b7280);
}

.markdown-editor-actions {
  display: flex;
  gap: 0.5rem;
}

.markdown-preview-container {
  border: 1px solid var(--color-border, #d1d5db);
  border-radius: 0.375rem;
  background-color: var(--color-background, #ffffff);
  min-height: 100px;
}

.markdown-preview {
  padding: 0.75rem;
  max-height: 300px;
  overflow-y: auto;
}

.markdown-help {
  margin-top: 0.5rem;
}

.markdown-help summary {
  outline: none;
}

.markdown-examples pre {
  font-size: 0.75rem;
  line-height: 1.4;
  overflow-x: auto;
}

/* Utility classes used in the component */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-500 {
  color: #9ca3af;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.p-2 {
  padding: 0.5rem;
}

.rounded {
  border-radius: 0.25rem;
}

.cursor-pointer {
  cursor: pointer;
}

/* Responsive grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
} 