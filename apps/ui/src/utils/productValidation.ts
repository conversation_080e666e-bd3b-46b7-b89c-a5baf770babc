import { Product } from "../types";

export interface AIReadinessResult {
  status: "ready" | "needs-improvement" | "enhanced" | "unknown";
  label: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  suggestions?: string[];
}

/**
 * Validates if a product has sufficient data for AI indexing
 */
export function validateProductForAI(
  product: Partial<Product>
): AIReadinessResult {
  // First check if product is already AI enhanced
  if (product.ai_enhanced_fields && product.ai_enhanced_fields.length > 0) {
    return {
      status: "enhanced",
      label: "AI Enhanced",
      description:
        "This product has been enhanced by AI and is optimally discoverable",
      icon: "✨",
      color: "#8b5cf6",
      bgColor: "#f3f4f6",
      suggestions: [],
    };
  }

  // Check for required fields
  if (!product.product_name || product.product_name.trim().length === 0) {
    return {
      status: "unknown",
      label: "Incomplete",
      description: "Missing essential product information",
      icon: "❓",
      color: "#6b7280",
      bgColor: "#f9fafb",
      suggestions: ["Add product name"],
    };
  }

  // Check for meaningful content
  const hasDescription =
    product.product_description &&
    product.product_description.trim().length > 10;
  const hasCategory = product.category && product.category.trim().length > 0;
  const hasBrand = product.brand_name && product.brand_name.trim().length > 0;
  const hasThcCbd =
    (product.percentage_thc && product.percentage_thc > 0) ||
    (product.percentage_cbd && product.percentage_cbd > 0) ||
    ((product as any).thc && (product as any).thc > 0) ||
    ((product as any).cbd && (product as any).cbd > 0);
  const hasTags = product.product_tags && product.product_tags.length > 0;
  const hasMood = product.mood && product.mood.length > 0;
  const hasEffects =
    product.effects &&
    ((typeof product.effects === "string" &&
      product.effects.trim().length > 0) ||
      (typeof product.effects === "object" &&
        product.effects !== null &&
        Object.keys(product.effects).length > 0) ||
      (Array.isArray(product.effects) && product.effects.length > 0));

  const meaningfulDataCount = [
    hasDescription,
    hasCategory,
    hasBrand,
    hasThcCbd,
    hasTags,
    hasMood,
    hasEffects,
  ].filter(Boolean).length;

  // Generate suggestions for missing data
  const suggestions: string[] = [];
  if (!hasDescription) suggestions.push("Add detailed description");
  if (!hasBrand) suggestions.push("Add brand name");
  if (!hasThcCbd) suggestions.push("Add THC/CBD percentages");
  if (!hasTags) suggestions.push("Add product tags");
  if (!hasMood) suggestions.push("Add mood effects");
  if (!hasEffects) suggestions.push("Add effects information");

  if (meaningfulDataCount < 2) {
    return {
      status: "needs-improvement",
      label: "Needs Enhancement",
      description:
        "Minimal product information. May be hard to discover by AI budtender.",
      icon: "⚠️",
      color: "#f59e0b",
      bgColor: "#fef3c7",
      suggestions: suggestions.slice(0, 3),
    };
  }

  // Check estimated text length for vectorization
  const productName = product.product_name || "";
  const category = product.category || "";
  const brand = product.brand_name || "";
  const description = product.product_description || "";

  const estimatedTextLength =
    `Product: ${productName}, Brand: ${brand}, Category: ${category}. ${description}`
      .length;

  if (estimatedTextLength < 50) {
    return {
      status: "needs-improvement",
      label: "Needs Enhancement",
      description:
        "Product information is too brief for optimal AI recommendations.",
      icon: "⚠️",
      color: "#f59e0b",
      bgColor: "#fef3c7",
      suggestions: suggestions.slice(0, 3),
    };
  }

  return {
    status: "ready",
    label: "AI Ready",
    description:
      "Product has sufficient information for good AI recommendations",
    icon: "✅",
    color: "#10b981",
    bgColor: "#d1fae5",
    suggestions: [],
  };
}
