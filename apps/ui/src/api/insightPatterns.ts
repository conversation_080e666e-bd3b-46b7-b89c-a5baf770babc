import { ApiClient } from "./client";

// Create a local API client instance
const api = new ApiClient();

export interface InsightPattern {
  id: number;
  pattern: string;
  description: string;
  type: "non_actionable" | "actionable";
  is_active: boolean;
  priority: number;
  examples: string[];
  created_at: string;
  updated_at: string;
}

export interface CreatePatternData {
  pattern: string;
  description: string;
  type: "non_actionable" | "actionable";
  is_active?: boolean;
  priority?: number;
  examples?: string[];
}

export interface UpdatePatternData {
  pattern?: string;
  description?: string;
  type?: "non_actionable" | "actionable";
  is_active?: boolean;
  priority?: number;
  examples?: string[];
}

export interface TestPatternData {
  pattern: string;
  testText: string;
}

export interface TestPatternResult {
  matches: boolean;
  error?: string;
}

/**
 * Get all insight patterns
 */
export async function getAll(locationId: number): Promise<InsightPattern[]> {
  const response: any = await api.get(`/locations/${locationId}/insights/patterns`);
  return response.data;
}

/**
 * Create a new insight pattern
 */
export async function create(locationId: number, data: CreatePatternData): Promise<InsightPattern> {
  const response: any = await api.post(`/locations/${locationId}/insights/patterns`, data);
  return response.data;
}

/**
 * Update an existing insight pattern
 */
export async function update(locationId: number, id: number, data: UpdatePatternData): Promise<InsightPattern> {
  const response: any = await api.put(`/locations/${locationId}/insights/patterns/${id}`, data);
  return response.data;
}

/**
 * Delete an insight pattern
 */
export async function deletePattern(locationId: number, id: number): Promise<void> {
  await api.delete(`/locations/${locationId}/insights/patterns/${id}`);
}

/**
 * Test a pattern against sample text
 */
export async function test(locationId: number, data: TestPatternData): Promise<TestPatternResult> {
  const response: any = await api.post(`/locations/${locationId}/insights/patterns/test`, data);
  return response.data;
}

/**
 * Find patterns that match specific text
 */
export async function findMatching(locationId: number, title: string, description?: string): Promise<InsightPattern[]> {
  const response: any = await api.post(`/locations/${locationId}/insights/patterns/match`, { title, description });
  return response.data;
}

export const insightPatterns = {
  getAll,
  create,
  update,
  delete: deletePattern,
  test,
  findMatching,
};
