import api from "./index";

export interface Event {
  id: number;
  event_id: string;
  job_id: string;
  user_id: string;
  location_id?: number;
  event_name: string;
  category?: string[];
  start_time?: string;
  timezone?: string;
  host?: string;
  starting_price?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  image?: string;
  url: string;
  source: string;
  data?: any;
  created_at: string;
  updated_at: string;
}

export interface CreateEventRequest {
  event_name: string;
  category?: string[];
  start_time: string;
  timezone?: string;
  host?: string;
  starting_price?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  image?: string;
  url?: string;
}

export type UpdateEventRequest = Partial<CreateEventRequest>;

export interface GetEventsResponse {
  events: Event[];
  total_count: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface GetEventsParams {
  page?: number;
  limit?: number;
  search?: string;
  future_only?: boolean;
}

export const events = {
  // Get events for a location
  async getLocationEvents(
    locationId: number,
    params: GetEventsParams = {}
  ): Promise<GetEventsResponse> {
    const searchParams = new URLSearchParams();
    if (params.page) searchParams.append("page", params.page.toString());
    if (params.limit) searchParams.append("limit", params.limit.toString());
    if (params.search) searchParams.append("search", params.search);
    if (params.future_only !== undefined)
      searchParams.append("future_only", params.future_only.toString());

    const queryString = searchParams.toString();
    const url = `/locations/${locationId}/events${
      queryString ? `?${queryString}` : ""
    }`;

    return await api.get(url);
  },

  // Get specific event by ID
  async getEvent(locationId: number, eventId: string): Promise<Event> {
    return await api.get(`/locations/${locationId}/events/${eventId}`);
  },

  // Create new event
  async createEvent(
    locationId: number,
    eventData: CreateEventRequest
  ): Promise<Event> {
    return await api.post(`/locations/${locationId}/events`, eventData);
  },

  // Update event
  async updateEvent(
    locationId: number,
    eventId: string,
    eventData: UpdateEventRequest
  ): Promise<Event> {
    return await api.put(
      `/locations/${locationId}/events/${eventId}`,
      eventData
    );
  },

  // Delete event
  async deleteEvent(locationId: number, eventId: string): Promise<void> {
    await api.delete(`/locations/${locationId}/events/${eventId}`);
  },

  // Search public events (existing endpoint)
  async searchPublicEvents(params: {
    query?: string;
    city?: string;
    state?: string;
    limit?: number;
    page?: number;
  }): Promise<GetEventsResponse> {
    return await api.post("/misc/events/search", params);
  },
};
