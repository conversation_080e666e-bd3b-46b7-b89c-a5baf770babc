import React, { useRef, useState, useCallback } from "react";
import { toast } from "react-hot-toast";
import html2canvas from "html2canvas";
import axios from "axios";
import Spinner from "../../ui/Spinner";
import Modal from "../../ui/Modal";
import {
  FacebookIcon,
  TwitterIcon,
  LinkedinIcon,
  WhatsappIcon,
} from "react-share";
import smokeyIcon from "../../assets/smokey_icon.png";

// Update interfaces to match the new data structure
interface HotCategory {
  category: string;
  weight: string;
  marketAvg: number;
  yourAvg: number;
  youVsMarketPct: number;
  competitorCount: number;
  totalCompetitorProductCount: number;
  yourProductCount: number;
  averageCompetitorProductCount: number;
  competitorBreakdown: Array<{
    retailerId: string;
    retailerName: string;
    productCount: number;
  }>;
}

interface MarketSnapshot {
  competitorCount: number;
  hotCategories: HotCategory[];
  gaps: string[];
  insights: string[];
  recommendations: string[];
  categoryComparison: Array<{
    category: string;
    yourProductCount: number;
    competitorBreakdown: Array<{
      retailerId: string;
      retailerName: string;
      productCount: number;
    }>;
    totalCompetitorProducts: number;
    averageCompetitorProducts: number;
  }>;
  generatedAt: string;
  totalProductsAnalyzed: number;
  categoriesFound: number;
}

export interface MarketAnalysisInsightsProps {
  competitors: any[];
  businessName?: string;
  marketSnapshot: MarketSnapshot | null;
  isLoading: boolean;
  error?: string | null;
  isOnboarding?: boolean;
  locationId?: number;
}

const MarketAnalysisInsights: React.FC<MarketAnalysisInsightsProps> = ({
  competitors,
  businessName = "my dispensary",
  marketSnapshot,
  isLoading,
  error,
  isOnboarding = false,
  locationId,
}) => {
  // Reference to the insights container for generating shareable image
  const insightsRef = useRef<HTMLDivElement>(null);

  // State for share functionality
  const [isGeneratingShareImage, setIsGeneratingShareImage] = useState(false);
  const [shareImageUrl, setShareImageUrl] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [shareUrls, setShareUrls] = useState<{
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    whatsapp?: string;
  }>({});

  // Add API base URL constant
  const API_BASE_URL = process.env.REACT_APP_API_URL || "";

  // Add frontend base URL constant - prioritize env var, then detect current host
  const FRONTEND_BASE_URL = process.env.REACT_APP_FRONTEND_URL ||
    (typeof window !== 'undefined' ? `${window.location.protocol}//${window.location.host}` : 'https://beta.bakedbot.ai');

  // Function to capture insights as image
  const captureInsightsAsImage = useCallback(async () => {
    if (!insightsRef.current) return null;

    // Show loading state
    setIsGeneratingShareImage(true);

    try {
      // Add share-mode class to enhance styling for image capture
      insightsRef.current.classList.add("share-mode");
      // Make sure the share attribution is visible
      const attributionEl =
        insightsRef.current.querySelector(".share-attribution");
      if (attributionEl) {
        attributionEl.classList.remove("hidden");
      }

      // Capture the DOM node as an image
      const canvas = await html2canvas(insightsRef.current, {
        backgroundColor: "#ffffff",
        scale: 2, // Higher resolution
        logging: false,
        useCORS: true,
      });

      // Convert to data URL
      const imageUrl = canvas.toDataURL("image/png");

      // Reset styling
      insightsRef.current.classList.remove("share-mode");
      if (attributionEl) {
        attributionEl.classList.add("hidden");
      }

      setShareImageUrl(imageUrl);
      return imageUrl;
    } catch (error) {
      console.error("Error capturing insights as image:", error);
      toast.error("Failed to generate shareable image");
      return null;
    } finally {
      setIsGeneratingShareImage(false);
    }
  }, []);

  // Function to download the captured image
  const downloadInsightsImage = useCallback(async () => {
    const imageUrl = shareImageUrl || (await captureInsightsAsImage());
    if (!imageUrl) return;

    const link = document.createElement("a");
    link.href = imageUrl;
    link.download = `ezal-market-analysis-${businessName}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success("Image downloaded successfully!");
  }, [shareImageUrl, captureInsightsAsImage, businessName]);

  // Function to upload image to Cloudinary via backend
  const uploadImageToCloudinary = useCallback(
    async (imageUrl: string): Promise<string | null> => {
      if (!imageUrl) return null;

      try {
        // Set uploading state
        setIsUploadingImage(true);
        toast.loading("Preparing shareable link...");

        // Convert Data URL to Blob
        const res = await fetch(imageUrl);
        const blob = await res.blob();

        // Create FormData for upload
        const formData = new FormData();
        formData.append("file", blob, `ezal-analysis-${Date.now()}.png`);

        // Add metadata for the shared image
        formData.append("type", "market_analysis");
        formData.append("businessName", businessName);
        formData.append("folder", "shared_images");

        // Add locationId if available
        if (locationId) {
          formData.append("locationId", locationId.toString());
        }

        // Use the correct endpoint for SharedAssetsController
        const response = await axios.post(
          `${API_BASE_URL}/api/shared-assets/upload`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        toast.dismiss();
        setIsUploadingImage(false);

        if (response.data && response.data.url) {
          toast.success("Image ready to share!");
          return response.data.url;
        } else {
          throw new Error("Invalid response from server");
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast.dismiss();
        setIsUploadingImage(false);
        toast.error("Failed to upload image. Using fallback method.");
        return null;
      }
    },
    [businessName, locationId, API_BASE_URL]
  );

  // Function to generate share URLs for different platforms
  const generateShareUrl = useCallback(
    async (platform: string) => {
      // First ensure we have an image URL
      const imageUrl = shareImageUrl || (await captureInsightsAsImage());
      if (!imageUrl) return null;

      // Generate sharing text from new data structure
      const shareText =
        marketSnapshot && marketSnapshot.insights.length > 0
          ? `Check out this market analysis: ${marketSnapshot.insights[0]}`
          : `Check out this market analysis for ${businessName}`;

      let shareUrl = "";
      let hostedImageUrl = null;

      // For platforms that need hosted images, upload to storage
      if (
        platform === "facebook" ||
        platform === "linkedin" ||
        platform === "twitter" ||
        platform === "whatsapp"
      ) {
        // Upload to our storage
        hostedImageUrl = await uploadImageToCloudinary(imageUrl);
      }

      switch (platform) {
        case "twitter":
          if (hostedImageUrl) {
            // Use the direct image URL for Twitter
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
              `${shareText} #EzalAnalysis`
            )}&url=${encodeURIComponent(hostedImageUrl)}`;
          } else {
            // Fallback without image
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
              `${shareText} #EzalAnalysis`
            )}&url=${encodeURIComponent(
              `${FRONTEND_BASE_URL}?utm_source=twitter`
            )}`;
          }
          break;

        case "facebook":
          if (hostedImageUrl) {
            // Use the direct image URL for Facebook
            shareUrl = `https://www.facebook.com/sharer.php?u=${encodeURIComponent(
              hostedImageUrl
            )}&quote=${encodeURIComponent(shareText)}`;
          } else {
            // Fallback without image
            shareUrl = `https://www.facebook.com/sharer.php?u=${encodeURIComponent(
              `${FRONTEND_BASE_URL}?utm_source=facebook`
            )}&quote=${encodeURIComponent(shareText)}`;
          }
          break;

        case "linkedin":
          if (hostedImageUrl) {
            // Use the direct image URL for LinkedIn
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
              hostedImageUrl
            )}`;
          } else {
            // Fallback without image
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
              `${FRONTEND_BASE_URL}?utm_source=linkedin`
            )}`;
          }
          break;

        case "whatsapp":
          if (hostedImageUrl) {
            // Use the direct image URL for WhatsApp
            shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(
              `${shareText} ${hostedImageUrl}`
            )}`;
          } else {
            // Fallback without image
            shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(
              `${shareText} ${FRONTEND_BASE_URL}?utm_source=whatsapp`
            )}`;
          }
          break;
      }

      return shareUrl;
    },
    [
      shareImageUrl,
      captureInsightsAsImage,
      marketSnapshot,
      businessName,
      uploadImageToCloudinary,
      FRONTEND_BASE_URL,
    ]
  );

  // Function to generate all share URLs when image is ready
  const generateAllShareUrls = useCallback(async () => {
    if (!shareImageUrl) return;

    const platforms = ['facebook', 'twitter', 'linkedin', 'whatsapp'];
    const urls: { [key: string]: string } = {};

    for (const platform of platforms) {
      const url = await generateShareUrl(platform);
      if (url) {
        urls[platform] = url;
      }
    }

    setShareUrls(urls);
  }, [shareImageUrl, generateShareUrl]);

  // Generate share URLs when image is ready
  React.useEffect(() => {
    if (shareImageUrl && Object.keys(shareUrls).length === 0) {
      generateAllShareUrls();
    }
  }, [shareImageUrl, shareUrls, generateAllShareUrls]);

  // Render the insights using the new data structure
  const renderInsights = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-4">
          <Spinner size="small" />
          <span className="ml-2">Loading market analysis...</span>
        </div>
      );
    }

    if (error) {
      return <div className="p-4 text-red-500">{error}</div>;
    }

    // If no data yet, show loading placeholder
    if (!marketSnapshot) {
      return (
        <div className="flex items-center justify-center p-4">
          <p className="text-sm text-gray-500">Processing competitor data...</p>
        </div>
      );
    }

    // Helper function to get price difference arrow and color
    const getPriceDifferenceIndicator = (youVsMarketPct: number) => {
      if (youVsMarketPct === 0) return { arrow: "", color: "" };

      if (youVsMarketPct > 0) {
        return {
          arrow: "↑", // Up arrow for higher prices
          color: "text-green-500",
        };
      } else {
        return {
          arrow: "↓", // Down arrow for lower prices
          color: "text-red-500",
        };
      }
    };

    // Helper function to find the most common package size for user in each category
    const getMostCommonUserSize = (category: string): HotCategory | null => {
      const categoryData = marketSnapshot.hotCategories.filter(
        (cat: HotCategory) =>
          cat.category === category && cat.yourProductCount > 0
      );

      if (categoryData.length === 0) return null;

      // Find the size with the most products
      return categoryData.reduce((max: HotCategory, current: HotCategory) =>
        current.yourProductCount > max.yourProductCount ? current : max
      );
    };

    // Helper function to analyze package size differences
    const analyzePackageSizeDifferences = (): string[] => {
      const insights: string[] = [];

      // Group categories by base category name (removing size info)
      const categoryGroups = new Map<string, HotCategory[]>();

      marketSnapshot.hotCategories.forEach((cat: HotCategory) => {
        const baseCategory = cat.category;
        if (!categoryGroups.has(baseCategory)) {
          categoryGroups.set(baseCategory, []);
        }
        categoryGroups.get(baseCategory)?.push(cat);
      });

      categoryGroups.forEach(
        (categories: HotCategory[], baseCategory: string) => {
          // Find user's most common size
          const userSizes = categories.filter(
            (cat: HotCategory) => cat.yourProductCount > 0
          );
          if (userSizes.length === 0) return;

          const mostCommonUserSize = userSizes.reduce(
            (max: HotCategory, current: HotCategory) =>
              current.yourProductCount > max.yourProductCount ? current : max
          );

          // Find competitors' most common size
          const competitorSizes = categories.map((cat: HotCategory) => ({
            weight: cat.weight,
            totalProducts: cat.totalCompetitorProductCount,
          }));

          const mostCommonCompetitorSize = competitorSizes.reduce(
            (
              max: { weight: string; totalProducts: number },
              current: { weight: string; totalProducts: number }
            ) => (current.totalProducts > max.totalProducts ? current : max)
          );

          // Check if there's a significant difference (>60% difference in preference)
          const userPreference = mostCommonUserSize.yourProductCount;
          const competitorPreference = mostCommonCompetitorSize.totalProducts;
          const totalUserProducts = userSizes.reduce(
            (sum: number, cat: HotCategory) => sum + cat.yourProductCount,
            0
          );
          const totalCompetitorProducts = competitorSizes.reduce(
            (sum: number, cat: { weight: string; totalProducts: number }) =>
              sum + cat.totalProducts,
            0
          );

          const userPreferencePct = userPreference / totalUserProducts;
          const competitorPreferencePct =
            competitorPreference / totalCompetitorProducts;

          // Only show if significant difference and different sizes
          if (
            Math.abs(userPreferencePct - competitorPreferencePct) > 0.4 &&
            mostCommonUserSize.weight !== mostCommonCompetitorSize.weight
          ) {
            const userSize = mostCommonUserSize.weight;
            const competitorSize = mostCommonCompetitorSize.weight;

            insights.push(
              `You focus on ${userSize} ${baseCategory.toLowerCase()} packages, but competitors primarily offer ${competitorSize} packages.`
            );
          }
        }
      );

      return insights;
    };

    // Get categories with user data, prioritizing the most common size for each category
    const categoriesWithUserData: HotCategory[] = [];
    const categoryGroups = new Map<string, HotCategory[]>();

    // Group by category name
    marketSnapshot.hotCategories.forEach((cat: HotCategory) => {
      if (!categoryGroups.has(cat.category)) {
        categoryGroups.set(cat.category, []);
      }
      categoryGroups.get(cat.category)?.push(cat);
    });

    // For each category, pick the size with the most user products
    categoryGroups.forEach(
      (categories: HotCategory[], categoryName: string) => {
        const mostCommonSize = getMostCommonUserSize(categoryName);
        if (mostCommonSize) {
          categoriesWithUserData.push(mostCommonSize);
        } else {
          // If no user data, pick the size with most competitor data
          const mostCompetitorData = categories.reduce(
            (max: HotCategory, current: HotCategory) =>
              current.totalCompetitorProductCount >
              max.totalCompetitorProductCount
                ? current
                : max
          );
          categoriesWithUserData.push(mostCompetitorData);
        }
      }
    );

    // Get the top 4 categories - prioritize user data differences, then fall back to market data
    const categoriesWithUserPricing = categoriesWithUserData.filter(
      (cat) => cat.yourAvg > 0 && cat.youVsMarketPct !== 0
    );
    const categoriesWithMarketData = categoriesWithUserData.filter(
      (cat) => cat.marketAvg > 0
    );

    // If user has pricing data, show biggest differences; otherwise show top market categories
    const topCategories =
      categoriesWithUserPricing.length > 0
        ? categoriesWithUserPricing
            .sort(
              (a, b) => Math.abs(b.youVsMarketPct) - Math.abs(a.youVsMarketPct)
            ) // Sort by biggest difference
            .slice(0, 4) // Limit to top 4
        : categoriesWithMarketData
            .sort(
              (a, b) =>
                b.totalCompetitorProductCount - a.totalCompetitorProductCount
            ) // Sort by most competitor products
            .slice(0, 4); // Limit to top 4

    // Check if user has any pricing data
    const hasUserData = topCategories.some((cat) => cat.yourAvg > 0);

    // Get insights and recommendations (already arrays in new structure)
    const allInsights = marketSnapshot.insights.slice(0, 3);
    const allRecommendations = marketSnapshot.recommendations.slice(0, 3);

    // Add package size insights
    const packageSizeInsights = analyzePackageSizeDifferences();
    const combinedInsights = [...allInsights, ...packageSizeInsights].slice(
      0,
      3
    );

    return (
      <div className="market-analysis-insights-container">
        <div className="market-analysis-grid">
          <div className="market-analysis-card">
            <h4 className="market-analysis-card-title">
              {hasUserData ? "Price Positioning" : "Market Averages"}
            </h4>
            <div className="market-analysis-card-content">
              {hasUserData ? (
                <>
                  <div className="space-y-1">
                    {topCategories.map((category) => {
                      const indicator = getPriceDifferenceIndicator(
                        category.youVsMarketPct
                      );
                      return (
                        <div
                          key={`${category.category}-${category.weight}`}
                          className="flex items-center bg-gray-50 rounded-md px-3 py-1"
                        >
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900">
                              {category.category}
                            </div>
                            <div className="text-xs text-gray-500">
                              {category.weight}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <div className="text-center w-16">
                              <div className="text-xs text-gray-500">
                                Market
                              </div>
                              <div className="font-medium text-sm">
                                ${category.marketAvg.toFixed(2)}
                              </div>
                            </div>
                            {category.youVsMarketPct !== 0 &&
                              category.yourAvg > 0 && (
                                <>
                                  <div className="text-center w-16 ml-2">
                                    <div className="text-xs text-gray-500">
                                      You
                                    </div>
                                    <div className="font-medium text-sm">
                                      ${category.yourAvg.toFixed(2)}
                                    </div>
                                  </div>
                                  <div
                                    className={`text-center w-12 ml-2 ${indicator.color}`}
                                  >
                                    <div className="text-md font-bold">
                                      {indicator.arrow}
                                    </div>
                                    <div className="text-xs font-medium">
                                      {category.youVsMarketPct > 0 ? "+" : ""}
                                      {category.youVsMarketPct.toFixed(1)}%
                                    </div>
                                  </div>
                                </>
                              )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : (
                <>
                  <div className="space-y-2">
                    {topCategories.map((category) => (
                      <div
                        key={`${category.category}-${category.weight}`}
                        className="flex items-center bg-gray-50 rounded-md py-2 px-3"
                      >
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900">
                            {category.category}
                          </div>
                          <div className="text-xs text-gray-500">
                            {category.weight}
                          </div>
                        </div>
                        <div className="text-center w-16">
                          <div className="text-xs text-gray-500">
                            Market Avg
                          </div>
                          <div className="font-medium text-sm">
                            ${category.marketAvg.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="text-gray-500 text-xs mt-3 text-center italic">
                    Add your product pricing to see comparisons
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="market-analysis-card">
            <h4 className="market-analysis-card-title">Market Insights</h4>
            <div className="market-analysis-card-content">
              <ul className="insights-list">
                {combinedInsights.map((insight, index) => (
                  <li key={`insight-${index}`}>{insight}</li>
                ))}
                {combinedInsights.length === 0 && (
                  <li className="text-gray-500 text-xs italic">
                    Add more competitors to generate insights
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {hasUserData && (
          <div className="text-center mt-2">
            {allRecommendations.length > 0 ? (
              <p className="text-xs text-gray-400 italic">
                {allRecommendations[0]}
              </p>
            ) : (
              <p className="text-xs text-gray-400 italic">
                AI projections will appear as more competitor data is analyzed
              </p>
            )}
          </div>
        )}

        {/* Attribution for sharing - only visible in share-mode */}
        <div className="share-attribution hidden">
          <div className="flex items-center justify-center mt-3 pt-2 border-t border-gray-200">
            <img src="/logo.png" alt="Ezal Logo" className="h-4 mr-1" />
            <span className="text-xs text-gray-400">Generated with Ezal</span>
          </div>
        </div>
      </div>
    );
  };

  // Share modal component
  const renderShareModal = () => {
    if (!showShareModal) return null;

    // Generate a text summary of the insights to share using new data structure
    let shareText = `Check out this market analysis for ${businessName}!\n\n`;

    // Add pricing insights from hot categories
    if (marketSnapshot && marketSnapshot.hotCategories.length > 0) {
      const topPricedCategories = marketSnapshot.hotCategories
        .filter((cat) => cat.yourAvg > 0)
        .slice(0, 3);

      topPricedCategories.forEach((category) => {
        shareText += `${category.category} (${
          category.weight
        }): Market avg $${category.marketAvg.toFixed(2)}`;
        if (category.youVsMarketPct !== 0) {
          shareText += ` (${
            category.youVsMarketPct > 0 ? "+" : ""
          }${category.youVsMarketPct.toFixed(1)}%)\n`;
        } else {
          shareText += "\n";
        }
      });
    }

    // Add a key insight if available
    if (marketSnapshot && marketSnapshot.insights.length > 0) {
      shareText += `\nInsight: ${marketSnapshot.insights[0]}\n`;
    }

    // Add recommendation if available
    if (marketSnapshot && marketSnapshot.recommendations.length > 0) {
      shareText += `\nRecommendation: ${marketSnapshot.recommendations[0]}\n`;
    }

    // Add link back to the current host
    shareText += `\nGenerated with Ezal. Learn more at ${FRONTEND_BASE_URL}`;



    return (
      <Modal
        open={showShareModal}
        onClose={() => setShowShareModal(false)}
        title="Share Market Analysis"
      >
        {/* Image preview section */}
        <div className="mb-4">
          <p className="font-medium mb-2 text-sm">Share as image:</p>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-2 mb-2 flex justify-center">
            {isGeneratingShareImage ? (
              <div className="py-10 text-center">
                <div className="inline-block animate-spin text-2xl mb-2">
                  ⏳
                </div>
                <p className="text-sm text-gray-500">
                  Generating shareable image...
                </p>
              </div>
            ) : isUploadingImage ? (
              <div className="py-10 text-center">
                <div className="inline-block animate-spin text-2xl mb-2">
                  🔄
                </div>
                <p className="text-sm text-gray-500">
                  Preparing shareable link...
                </p>
              </div>
            ) : shareImageUrl ? (
              <img
                src={shareImageUrl}
                alt="Market Analysis"
                className="max-w-full max-h-[200px] object-contain"
              />
            ) : (
              <button
                onClick={captureInsightsAsImage}
                className="py-8 px-4 w-full text-sm bg-[#3EDC81]/10 text-[#3EDC81] rounded-lg hover:bg-[#3EDC81]/20"
              >
                Click to generate shareable image
              </button>
            )}
          </div>

          {/* Direct social media image sharing buttons */}
          {shareImageUrl && (
            <>
              {/* Direct social media image sharing buttons */}
              <div className="pt-3 mb-3">
                <p className="text-xs text-center text-gray-500 mb-2">
                  Share image to:
                </p>
                <div className="flex justify-center space-x-4">
                  <a
                    href={shareUrls.facebook || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-2 rounded-full bg-[#4267B2]/10 hover:bg-[#4267B2]/20 ${
                      !shareUrls.facebook || isUploadingImage
                        ? 'opacity-50 pointer-events-none'
                        : ''
                    }`}
                    title="Share to Facebook"
                  >
                    <FacebookIcon size={24} round />
                  </a>
                  <a
                    href={shareUrls.twitter || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-2 rounded-full bg-black/10 hover:bg-black/20 ${
                      !shareUrls.twitter || isUploadingImage
                        ? 'opacity-50 pointer-events-none'
                        : ''
                    }`}
                    title="Share to Twitter"
                  >
                    <TwitterIcon size={24} round />
                  </a>
                  <a
                    href={shareUrls.linkedin || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-2 rounded-full bg-[#0077B5]/10 hover:bg-[#0077B5]/20 ${
                      !shareUrls.linkedin || isUploadingImage
                        ? 'opacity-50 pointer-events-none'
                        : ''
                    }`}
                    title="Share to LinkedIn"
                  >
                    <LinkedinIcon size={24} round />
                  </a>
                  <a
                    href={shareUrls.whatsapp || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-2 rounded-full bg-[#25D366]/10 hover:bg-[#25D366]/20 ${
                      !shareUrls.whatsapp || isUploadingImage
                        ? 'opacity-50 pointer-events-none'
                        : ''
                    }`}
                    title="Share to WhatsApp"
                  >
                    <WhatsappIcon size={24} round />
                  </a>
                </div>
              </div>
            </>
          )}
        </div>
      </Modal>
    );
  };

  // Don't render if no competitors or no meaningful market data
  if (
    competitors.length === 0 ||
    (marketSnapshot && marketSnapshot.hotCategories.length === 0)
  ) {
    return null;
  }

  return (
    <div className="market-analysis-container mb-4 relative">
      {/* Top right buttons container */}
      <div className="absolute top-3 right-3 flex items-center space-x-2 z-10">
        {/* Smokey mascot link */}
        <a
          href="https://bakedbot.ai/"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center text-xs text-[#3EDC81] hover:opacity-80 transition-opacity"
          title="Visit BakedBot.ai"
        >
          <img
            src={smokeyIcon}
            alt="Smokey Mascot"
            className="w-4 h-4"
          />
        </a>

        {/* Share button - subtle design */}
        <button
          onClick={() => {
            setShowShareModal(true);
            // Pre-generate the image when opening share modal
            if (!shareImageUrl) {
              captureInsightsAsImage();
            }
          }}
          className="flex items-center text-xs text-[#3EDC81] hover:underline transition-colors"
          title="Share this analysis"
        >
          <span className="flex items-center">
            <svg
              className="w-3 h-3 mr-1"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
            </svg>
            Share
          </span>
        </button>
      </div>

      <div className="market-analysis-header">
        <div className="market-analysis-icon">🔍</div>
        <div>
          <h3 className="market-analysis-title">
            {isOnboarding ? "Ezal's Competitive Analysis" : "Market Analysis"}
          </h3>
          <p className="market-analysis-subtitle">
            Based on {competitors.length} competitor
            {competitors.length !== 1 ? "s" : ""}
            {marketSnapshot && (
              <span>
                {" "}
                • {marketSnapshot.totalProductsAnalyzed} products analyzed
              </span>
            )}
          </p>
        </div>
      </div>

      {/* Insights content - wrap with ref for sharing */}
      <div ref={insightsRef}>{renderInsights()}</div>

      {/* Conditional footer - only show if user doesn't have data */}
      {marketSnapshot &&
        !marketSnapshot.hotCategories.some((cat) => cat.yourAvg > 0) && (
          <div className="market-analysis-footer">
            Connect your product data to get personalized pricing
            recommendations
          </div>
        )}

      {/* Share Modal */}
      {renderShareModal()}
    </div>
  );
};

// Add CSS for share mode
const styles = `
.share-mode {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 600px;
  position: relative;
  border: 1px solid #e5e7eb;
}

.share-mode .share-attribution {
  display: block !important;
}

.share-mode::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3EDC81, #2bb7da);
  border-radius: 4px 4px 0 0;
}

/* Add styling for attribution */
.share-mode .share-attribution {
  text-align: center;
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e5e7eb;
}

.share-mode .share-attribution img {
  height: 20px;
  margin-right: 4px;
}
`;

// Inject the styles
if (typeof document !== "undefined") {
  const styleEl = document.createElement("style");
  styleEl.textContent = styles;
  document.head.appendChild(styleEl);
}

export default MarketAnalysisInsights;
