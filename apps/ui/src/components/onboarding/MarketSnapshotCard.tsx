import React from "react";
import {
  ShareIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "@heroicons/react/24/solid";
import Button from "../../ui/Button";

interface HotCategory {
  category: string;
  weight: string;
  marketAvg: number;
  yourAvg: number;
  youVsMarketPct: number;
}

interface MarketSnapshot {
  competitorCount: number;
  hotCategories: HotCategory[];
  gaps: string[];
  insights: string[];
  recommendations: string[];
  generatedAt: string;
}

interface MarketSnapshotCardProps {
  snapshot: MarketSnapshot | null;
  businessName: string;
  isLoading: boolean;
  onShare: () => void;
}

const MarketSnapshotCard: React.FC<MarketSnapshotCardProps> = ({
  snapshot,
  businessName,
  isLoading,
  onShare,
}) => {
  if (isLoading) {
    return (
      <div className="bg-surface border border-divider rounded-lg p-4 shadow-sm animate-pulse">
        <div className="h-8 bg-gray-700 rounded w-3/4 mb-4"></div>
        <div className="space-y-3">
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="h-4 bg-gray-700 rounded w-5/6"></div>
          <div className="h-4 bg-gray-700 rounded w-4/6"></div>
        </div>
      </div>
    );
  }

  if (!snapshot) {
    return null;
  }

  const renderPriceComparison = (hotCategory: HotCategory) => {
    const isPricier = hotCategory.youVsMarketPct > 0;
    const isCheaper = hotCategory.youVsMarketPct < 0;

    return (
      <div className="flex items-center">
        <span
          className={`font-bold ${
            isPricier
              ? "text-red-400"
              : isCheaper
              ? "text-green-400"
              : "text-primary-soft"
          }`}
        >
          {isPricier && <ArrowUpIcon className="h-4 w-4 inline-block mr-1" />}
          {isCheaper && <ArrowDownIcon className="h-4 w-4 inline-block mr-1" />}
          {Math.abs(hotCategory.youVsMarketPct).toFixed(0)}%
        </span>
        <span className="text-primary-soft ml-2">
          (You: ${hotCategory.yourAvg.toFixed(2)} vs Market: $
          {hotCategory.marketAvg.toFixed(2)})
        </span>
      </div>
    );
  };

  return (
    <div
      className="bg-surface border border-divider rounded-lg p-4 shadow-sm"
      id="market-snapshot"
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <h3 className="text-lg font-bold text-primary">
            Your Market Snapshot
          </h3>
          <p className="text-sm text-primary-soft">
            vs. {snapshot.competitorCount} competitors
          </p>
        </div>
        <Button variant="secondary" onClick={onShare} icon={<ShareIcon />}>
          Share
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h4 className="font-semibold text-primary mb-2">Popular Products</h4>
          <div className="space-y-3">
            {snapshot.hotCategories.map((cat) => (
              <div
                key={cat.category}
                className="bg-surface-secondary p-3 rounded-md"
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium text-primary">
                    {cat.category} - ({cat.weight})
                  </span>
                  <span className="font-mono text-lg text-primary">
                    ${cat.marketAvg.toFixed(2)}
                  </span>
                </div>
                {cat.yourAvg > 0 && renderPriceComparison(cat)}
              </div>
            ))}
          </div>
        </div>

        {snapshot.gaps.length > 0 && (
          <div>
            <h4 className="font-semibold text-primary mb-2">Opportunities</h4>
            <ul className="list-disc list-inside space-y-1 text-primary-soft">
              {snapshot.gaps.map((gap, i) => (
                <li key={i}>{gap}</li>
              ))}
            </ul>
          </div>
        )}

        {snapshot.insights.length > 0 && (
          <div>
            <h4 className="font-semibold text-primary mb-2">Key Insights</h4>
            <ul className="list-disc list-inside space-y-1 text-primary-soft">
              {snapshot.insights.map((insight, i) => (
                <li key={i}>{insight}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      <p className="text-xs text-right mt-4 text-gray-500">
        Generated: {new Date(snapshot.generatedAt).toLocaleString()}
      </p>
    </div>
  );
};

export default MarketSnapshotCard;
