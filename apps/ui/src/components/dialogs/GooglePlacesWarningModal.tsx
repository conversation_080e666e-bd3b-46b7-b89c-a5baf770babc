import React from "react";
import { useTranslation } from "react-i18next";
import Modal, { ModalStateProps } from "../../ui/Modal";
import Button from "../../ui/Button";
// import LinkIcon from "../../ui/icons/LinkIcon";
import { FaLink as LinkIcon } from "react-icons/fa";
import { FaDatabase as DatabaseIcon, FaExclamationTriangle as AlertTriangleIcon } from "react-icons/fa";

interface GooglePlacesWarningModalProps extends ModalStateProps {
  businessName: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export default function GooglePlacesWarningModal({
  open,
  onClose,
  businessName,
  onConfirm,
  onCancel,
}: GooglePlacesWarningModalProps) {
  const { t } = useTranslation();

  const handleConfirm = () => {
    onConfirm();
    onClose(false);
  };

  const handleCancel = () => {
    onCancel();
    onClose(false);
  };

  return (
    <Modal
      open={open}
      onClose={() => onClose(false)}
      title={
        <div className="flex items-center">
          <AlertTriangleIcon className="w-5 h-5 text-yellow-500 mr-2" />
          Limited Business Data Available
        </div>
      }
      size="regular"
      actions={
        <div className="flex space-x-3">
          <Button variant="secondary" onClick={handleCancel}>
            Choose Different Business
          </Button>
          <Button variant="primary" onClick={handleConfirm}>
            Continue Anyway
          </Button>
        </div>
      }
    >
      <div className="space-y-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangleIcon className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-1">
                Limited Capabilities Notice
              </h4>
              <p className="text-sm text-yellow-700">
                <strong>"{businessName}"</strong> was found through Google Places and is not yet in our cannabis business database.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <h5 className="font-medium text-gray-900">What this means:</h5>
          
          <div className="space-y-2">
            <div className="flex items-start">
              <DatabaseIcon className="w-4 h-4 text-red-500 mt-1 mr-3 flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <strong>No product data available</strong> - We don't have inventory, pricing, or product information for this business
              </div>
            </div>
            
            <div className="flex items-start">
              <AlertTriangleIcon className="w-4 h-4 text-yellow-500 mt-1 mr-3 flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <strong>Limited insights</strong> - Market analysis, competitor comparisons, and product recommendations will be restricted
              </div>
            </div>
            
            <div className="flex items-start">
              <LinkIcon className="w-4 h-4 text-blue-500 mt-1 mr-3 flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <strong>POS connection required</strong> - To unlock full capabilities, you'll need to connect your Point of Sale system
              </div>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <LinkIcon className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">
                Unlock Full Potential
              </h4>
              <p className="text-sm text-blue-700">
                Connect your POS system (Dutchie, Treez, etc.) to access:
              </p>
              <ul className="text-sm text-blue-700 mt-2 ml-4 list-disc">
                <li>Complete product inventory analysis</li>
                <li>Pricing optimization recommendations</li>
                <li>Detailed competitor comparisons</li>
                <li>AI-powered market insights</li>
                <li>Automated marketing campaigns</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          <p>
            <strong>Recommendation:</strong> If your business is already in our database, try searching with different terms or your exact business name. 
            Otherwise, you can continue and connect your POS system later to unlock all features.
          </p>
        </div>
      </div>
    </Modal>
  );
}
